{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/apps_chatbot_ddfe1775._.js", "server/edge/chunks/node_modules_jose_dist_browser_d404c1b8._.js", "server/edge/chunks/node_modules_zod_23d1dcf1._.js", "server/edge/chunks/node_modules_zod-to-json-schema_dist_esm_993e0d69._.js", "server/edge/chunks/node_modules_67fea34d._.js", "server/edge/chunks/[root-of-the-server]__c4fe2756._.js", "server/edge/chunks/apps_chatbot_edge-wrapper_80a9c72c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/chat/:id{(\\\\.json)}?", "originalSource": "/chat/:id"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/login{(\\\\.json)}?", "originalSource": "/login"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/register{(\\\\.json)}?", "originalSource": "/register"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "fVpYL6EEEeFSsVURHX9l8tViuvnYT4s/HgFjxHJRFf4=", "__NEXT_PREVIEW_MODE_ID": "869baa9e54c31f1a8e3f90297dcb199f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0f04d31e5407622edd344fca1458623f01a5e80c1dd36f0f020fe9953bd5a5a5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be961284b9dc5c2f708f329bba9378f78368357a9f1ebf1244de967f3e3aa572"}}}, "instrumentation": null, "functions": {}}