{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/chatbot/lib/db/utils.ts"], "sourcesContent": ["import { generateId } from 'ai';\nimport { genSaltSync, hashSync } from 'bcrypt-ts';\n\nexport function generateHashedPassword(password: string) {\n  const salt = genSaltSync(10);\n  const hash = hashSync(password, salt);\n\n  return hash;\n}\n\nexport function generateDummyPassword() {\n  const password = generateId();\n  const hashedPassword = generateHashedPassword(password);\n\n  return hashedPassword;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,uBAAuB,QAAgB;IACrD,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE;IACzB,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;IAEhC,OAAO;AACT;AAEO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD;IAC1B,MAAM,iBAAiB,uBAAuB;IAE9C,OAAO;AACT"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/chatbot/lib/constants.ts"], "sourcesContent": ["import { generateDummyPassword } from './db/utils';\n\nexport const isProductionEnvironment = process.env.NODE_ENV === 'production';\nexport const isDevelopmentEnvironment = process.env.NODE_ENV === 'development';\nexport const isTestEnvironment = Boolean(\n  process.env.PLAYWRIGHT_TEST_BASE_URL ||\n    process.env.PLAYWRIGHT ||\n    process.env.CI_PLAYWRIGHT,\n);\n\nexport const guestRegex = /^guest-\\d+$/;\n\nexport const DUMMY_PASSWORD = generateDummyPassword();\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,0BAA0B,oDAAyB;AACzD,MAAM,2BAA2B,oDAAyB;AAC1D,MAAM,oBAAoB,QAC/B,QAAQ,GAAG,CAAC,wBAAwB,IAClC,QAAQ,GAAG,CAAC,UAAU,IACtB,QAAQ,GAAG,CAAC,aAAa;AAGtB,MAAM,aAAa;AAEnB,MAAM,iBAAiB,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/chatbot/middleware.ts"], "sourcesContent": ["import { NextResponse, type NextRequest } from 'next/server';\nimport { getToken } from 'next-auth/jwt';\nimport { guestRegex, isDevelopmentEnvironment } from './lib/constants';\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  /*\n   * <PERSON><PERSON> starts the dev server and requires a 200 status to\n   * begin the tests, so this ensures that the tests can start\n   */\n  if (pathname.startsWith('/ping')) {\n    return new Response('pong', { status: 200 });\n  }\n\n  if (pathname.startsWith('/api/auth')) {\n    return NextResponse.next();\n  }\n\n  const token = await getToken({\n    req: request,\n    secret: process.env.AUTH_SECRET,\n    secureCookie: !isDevelopmentEnvironment,\n  });\n\n  if (!token) {\n    const redirectUrl = encodeURIComponent(request.url);\n\n    return NextResponse.redirect(\n      new URL(`/api/auth/guest?redirectUrl=${redirectUrl}`, request.url),\n    );\n  }\n\n  const isGuest = guestRegex.test(token?.email ?? '');\n\n  if (token && !isGuest && ['/login', '/register'].includes(pathname)) {\n    return NextResponse.redirect(new URL('/', request.url));\n  }\n\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    '/',\n    '/chat/:id',\n    '/api/:path*',\n    '/login',\n    '/register',\n\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico, sitemap.xml, robots.txt (metadata files)\n     */\n    '/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;AACA;;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC;;;GAGC,GACD,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,OAAO,IAAI,SAAS,QAAQ;YAAE,QAAQ;QAAI;IAC5C;IAEA,IAAI,SAAS,UAAU,CAAC,cAAc;QACpC,OAAO,gNAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,MAAM,QAAQ,MAAM,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B,KAAK;QACL,QAAQ,QAAQ,GAAG,CAAC,WAAW;QAC/B,cAAc,CAAC,2IAAA,CAAA,2BAAwB;IACzC;IAEA,IAAI,CAAC,OAAO;QACV,MAAM,cAAc,mBAAmB,QAAQ,GAAG;QAElD,OAAO,gNAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,IAAI,IAAI,CAAC,4BAA4B,EAAE,aAAa,EAAE,QAAQ,GAAG;IAErE;IAEA,MAAM,UAAU,2IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,OAAO,SAAS;IAEhD,IAAI,SAAS,CAAC,WAAW;QAAC;QAAU;KAAY,CAAC,QAAQ,CAAC,WAAW;QACnE,OAAO,gNAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;IACvD;IAEA,OAAO,gNAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;QACA;QAEA;;;;;KAKC,GACD;KACD;AACH"}}]}