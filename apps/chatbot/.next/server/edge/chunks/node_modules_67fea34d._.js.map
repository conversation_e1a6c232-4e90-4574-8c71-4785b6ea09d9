{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@panva/hkdf/dist/web/runtime/hkdf.js"], "sourcesContent": ["const getGlobal = () => {\n    if (typeof globalThis !== 'undefined')\n        return globalThis;\n    if (typeof self !== 'undefined')\n        return self;\n    if (typeof window !== 'undefined')\n        return window;\n    throw new Error('unable to locate global object');\n};\nexport default async (digest, ikm, salt, info, keylen) => {\n    const { crypto: { subtle }, } = getGlobal();\n    return new Uint8Array(await subtle.deriveBits({\n        name: 'HKDF',\n        hash: `SHA-${digest.substr(3)}`,\n        salt,\n        info,\n    }, await subtle.importKey('raw', ikm, 'HKDF', false, ['deriveBits']), keylen << 3));\n};\n"], "names": [], "mappings": ";;;AAAA,MAAM,YAAY;IACd,IAAI,OAAO,eAAe,aACtB,OAAO;IACX,IAAI,OAAO,SAAS,aAChB,OAAO;IACX,uCACI;;IAAa;IACjB,MAAM,IAAI,MAAM;AACpB;uCACe,OAAO,QAAQ,KAAK,MAAM,MAAM;IAC3C,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAG,GAAG;IAChC,OAAO,IAAI,WAAW,MAAM,OAAO,UAAU,CAAC;QAC1C,MAAM;QACN,MAAM,CAAC,IAAI,EAAE,OAAO,MAAM,CAAC,IAAI;QAC/B;QACA;IACJ,GAAG,MAAM,OAAO,SAAS,CAAC,OAAO,KAAK,QAAQ,OAAO;QAAC;KAAa,GAAG,UAAU;AACpF", "ignoreList": [0]}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@panva/hkdf/dist/web/index.js"], "sourcesContent": ["import derive from './runtime/hkdf.js';\nfunction normalizeDigest(digest) {\n    switch (digest) {\n        case 'sha256':\n        case 'sha384':\n        case 'sha512':\n        case 'sha1':\n            return digest;\n        default:\n            throw new TypeError('unsupported \"digest\" value');\n    }\n}\nfunction normalizeUint8Array(input, label) {\n    if (typeof input === 'string')\n        return new TextEncoder().encode(input);\n    if (!(input instanceof Uint8Array))\n        throw new TypeError(`\"${label}\"\" must be an instance of Uint8Array or a string`);\n    return input;\n}\nfunction normalizeIkm(input) {\n    const ikm = normalizeUint8Array(input, 'ikm');\n    if (!ikm.byteLength)\n        throw new TypeError(`\"ikm\" must be at least one byte in length`);\n    return ikm;\n}\nfunction normalizeInfo(input) {\n    const info = normalizeUint8Array(input, 'info');\n    if (info.byteLength > 1024) {\n        throw TypeError('\"info\" must not contain more than 1024 bytes');\n    }\n    return info;\n}\nfunction normalizeKeylen(input, digest) {\n    if (typeof input !== 'number' || !Number.isInteger(input) || input < 1) {\n        throw new TypeError('\"keylen\" must be a positive integer');\n    }\n    const hashlen = parseInt(digest.substr(3), 10) >> 3 || 20;\n    if (input > 255 * hashlen) {\n        throw new TypeError('\"keylen\" too large');\n    }\n    return input;\n}\nasync function hkdf(digest, ikm, salt, info, keylen) {\n    return derive(normalizeDigest(digest), normalizeIkm(ikm), normalizeUint8Array(salt, 'salt'), normalizeInfo(info), normalizeKeylen(keylen, digest));\n}\nexport { hkdf, hkdf as default };\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,gBAAgB,MAAM;IAC3B,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,UAAU;IAC5B;AACJ;AACA,SAAS,oBAAoB,KAAK,EAAE,KAAK;IACrC,IAAI,OAAO,UAAU,UACjB,OAAO,IAAI,cAAc,MAAM,CAAC;IACpC,IAAI,CAAC,CAAC,iBAAiB,UAAU,GAC7B,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,MAAM,gDAAgD,CAAC;IACnF,OAAO;AACX;AACA,SAAS,aAAa,KAAK;IACvB,MAAM,MAAM,oBAAoB,OAAO;IACvC,IAAI,CAAC,IAAI,UAAU,EACf,MAAM,IAAI,UAAU,CAAC,yCAAyC,CAAC;IACnE,OAAO;AACX;AACA,SAAS,cAAc,KAAK;IACxB,MAAM,OAAO,oBAAoB,OAAO;IACxC,IAAI,KAAK,UAAU,GAAG,MAAM;QACxB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,KAAK,EAAE,MAAM;IAClC,IAAI,OAAO,UAAU,YAAY,CAAC,OAAO,SAAS,CAAC,UAAU,QAAQ,GAAG;QACpE,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,UAAU,SAAS,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK;IACvD,IAAI,QAAQ,MAAM,SAAS;QACvB,MAAM,IAAI,UAAU;IACxB;IACA,OAAO;AACX;AACA,eAAe,KAAK,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;IAC/C,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAM,AAAD,EAAE,gBAAgB,SAAS,aAAa,MAAM,oBAAoB,MAAM,SAAS,cAAc,OAAO,gBAAgB,QAAQ;AAC9I", "ignoreList": [0]}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@auth/core/lib/utils/cookie.js"], "sourcesContent": ["var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _SessionStore_instances, _SessionStore_chunks, _SessionStore_option, _SessionStore_logger, _SessionStore_chunk, _SessionStore_clean;\n// Uncomment to recalculate the estimated size\n// of an empty session cookie\n// import { serialize } from \"cookie\"\n// console.log(\n//   \"Cookie estimated to be \",\n//   serialize(`__Secure.authjs.session-token.0`, \"\", {\n//     expires: new Date(),\n//     httpOnly: true,\n//     maxAge: Number.MAX_SAFE_INTEGER,\n//     path: \"/\",\n//     sameSite: \"strict\",\n//     secure: true,\n//     domain: \"example.com\",\n//   }).length,\n//   \" bytes\"\n// )\nconst ALLOWED_COOKIE_SIZE = 4096;\n// Based on commented out section above\nconst ESTIMATED_EMPTY_COOKIE_SIZE = 160;\nconst CHUNK_SIZE = ALLOWED_COOKIE_SIZE - ESTIMATED_EMPTY_COOKIE_SIZE;\n/**\n * Use secure cookies if the site uses HTTPS\n * This being conditional allows cookies to work non-HTTPS development URLs\n * Honour secure cookie option, which sets 'secure' and also adds '__Secure-'\n * prefix, but enable them by default if the site URL is HTTPS; but not for\n * non-HTTPS URLs like http://localhost which are used in development).\n * For more on prefixes see https://googlechrome.github.io/samples/cookie-prefixes/\n *\n * @TODO Review cookie settings (names, options)\n */\nexport function defaultCookies(useSecureCookies) {\n    const cookiePrefix = useSecureCookies ? \"__Secure-\" : \"\";\n    return {\n        // default cookie options\n        sessionToken: {\n            name: `${cookiePrefix}authjs.session-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n            },\n        },\n        callbackUrl: {\n            name: `${cookiePrefix}authjs.callback-url`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n            },\n        },\n        csrfToken: {\n            // Default to __Host- for CSRF token for additional protection if using useSecureCookies\n            // NB: The `__Host-` prefix is stricter than the `__Secure-` prefix.\n            name: `${useSecureCookies ? \"__Host-\" : \"\"}authjs.csrf-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n            },\n        },\n        pkceCodeVerifier: {\n            name: `${cookiePrefix}authjs.pkce.code_verifier`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n                maxAge: 60 * 15, // 15 minutes in seconds\n            },\n        },\n        state: {\n            name: `${cookiePrefix}authjs.state`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n                maxAge: 60 * 15, // 15 minutes in seconds\n            },\n        },\n        nonce: {\n            name: `${cookiePrefix}authjs.nonce`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n            },\n        },\n        webauthnChallenge: {\n            name: `${cookiePrefix}authjs.challenge`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: useSecureCookies,\n                maxAge: 60 * 15, // 15 minutes in seconds\n            },\n        },\n    };\n}\nexport class SessionStore {\n    constructor(option, cookies, logger) {\n        _SessionStore_instances.add(this);\n        _SessionStore_chunks.set(this, {});\n        _SessionStore_option.set(this, void 0);\n        _SessionStore_logger.set(this, void 0);\n        __classPrivateFieldSet(this, _SessionStore_logger, logger, \"f\");\n        __classPrivateFieldSet(this, _SessionStore_option, option, \"f\");\n        if (!cookies)\n            return;\n        const { name: sessionCookiePrefix } = option;\n        for (const [name, value] of Object.entries(cookies)) {\n            if (!name.startsWith(sessionCookiePrefix) || !value)\n                continue;\n            __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")[name] = value;\n        }\n    }\n    /**\n     * The JWT Session or database Session ID\n     * constructed from the cookie chunks.\n     */\n    get value() {\n        // Sort the chunks by their keys before joining\n        const sortedKeys = Object.keys(__classPrivateFieldGet(this, _SessionStore_chunks, \"f\")).sort((a, b) => {\n            const aSuffix = parseInt(a.split(\".\").pop() || \"0\");\n            const bSuffix = parseInt(b.split(\".\").pop() || \"0\");\n            return aSuffix - bSuffix;\n        });\n        // Use the sorted keys to join the chunks in the correct order\n        return sortedKeys.map((key) => __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")[key]).join(\"\");\n    }\n    /**\n     * Given a cookie value, return new cookies, chunked, to fit the allowed cookie size.\n     * If the cookie has changed from chunked to unchunked or vice versa,\n     * it deletes the old cookies as well.\n     */\n    chunk(value, options) {\n        // Assume all cookies should be cleaned by default\n        const cookies = __classPrivateFieldGet(this, _SessionStore_instances, \"m\", _SessionStore_clean).call(this);\n        // Calculate new chunks\n        const chunked = __classPrivateFieldGet(this, _SessionStore_instances, \"m\", _SessionStore_chunk).call(this, {\n            name: __classPrivateFieldGet(this, _SessionStore_option, \"f\").name,\n            value,\n            options: { ...__classPrivateFieldGet(this, _SessionStore_option, \"f\").options, ...options },\n        });\n        // Update stored chunks / cookies\n        for (const chunk of chunked) {\n            cookies[chunk.name] = chunk;\n        }\n        return Object.values(cookies);\n    }\n    /** Returns a list of cookies that should be cleaned. */\n    clean() {\n        return Object.values(__classPrivateFieldGet(this, _SessionStore_instances, \"m\", _SessionStore_clean).call(this));\n    }\n}\n_SessionStore_chunks = new WeakMap(), _SessionStore_option = new WeakMap(), _SessionStore_logger = new WeakMap(), _SessionStore_instances = new WeakSet(), _SessionStore_chunk = function _SessionStore_chunk(cookie) {\n    const chunkCount = Math.ceil(cookie.value.length / CHUNK_SIZE);\n    if (chunkCount === 1) {\n        __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")[cookie.name] = cookie.value;\n        return [cookie];\n    }\n    const cookies = [];\n    for (let i = 0; i < chunkCount; i++) {\n        const name = `${cookie.name}.${i}`;\n        const value = cookie.value.substr(i * CHUNK_SIZE, CHUNK_SIZE);\n        cookies.push({ ...cookie, name, value });\n        __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")[name] = value;\n    }\n    __classPrivateFieldGet(this, _SessionStore_logger, \"f\").debug(\"CHUNKING_SESSION_COOKIE\", {\n        message: `Session cookie exceeds allowed ${ALLOWED_COOKIE_SIZE} bytes.`,\n        emptyCookieSize: ESTIMATED_EMPTY_COOKIE_SIZE,\n        valueSize: cookie.value.length,\n        chunks: cookies.map((c) => c.value.length + ESTIMATED_EMPTY_COOKIE_SIZE),\n    });\n    return cookies;\n}, _SessionStore_clean = function _SessionStore_clean() {\n    const cleanedChunks = {};\n    for (const name in __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")) {\n        delete __classPrivateFieldGet(this, _SessionStore_chunks, \"f\")?.[name];\n        cleanedChunks[name] = {\n            name,\n            value: \"\",\n            options: { ...__classPrivateFieldGet(this, _SessionStore_option, \"f\").options, maxAge: 0 },\n        };\n    }\n    return cleanedChunks;\n};\n"], "names": [], "mappings": ";;;;AAAA,IAAI,yBAAyB,AAAC,IAAI,IAAI,IAAI,CAAC,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC3G,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AACA,IAAI,yBAAyB,AAAC,IAAI,IAAI,IAAI,CAAC,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpG,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AACA,IAAI,yBAAyB,sBAAsB,sBAAsB,sBAAsB,qBAAqB;AACpH,8CAA8C;AAC9C,6BAA6B;AAC7B,qCAAqC;AACrC,eAAe;AACf,+BAA+B;AAC/B,uDAAuD;AACvD,2BAA2B;AAC3B,sBAAsB;AACtB,uCAAuC;AACvC,iBAAiB;AACjB,0BAA0B;AAC1B,oBAAoB;AACpB,6BAA6B;AAC7B,eAAe;AACf,aAAa;AACb,IAAI;AACJ,MAAM,sBAAsB;AAC5B,uCAAuC;AACvC,MAAM,8BAA8B;AACpC,MAAM,aAAa,sBAAsB;AAWlC,SAAS,eAAe,gBAAgB;IAC3C,MAAM,eAAe,mBAAmB,cAAc;IACtD,OAAO;QACH,yBAAyB;QACzB,cAAc;YACV,MAAM,GAAG,aAAa,oBAAoB,CAAC;YAC3C,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACZ;QACJ;QACA,aAAa;YACT,MAAM,GAAG,aAAa,mBAAmB,CAAC;YAC1C,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACZ;QACJ;QACA,WAAW;YACP,wFAAwF;YACxF,oEAAoE;YACpE,MAAM,GAAG,mBAAmB,YAAY,GAAG,iBAAiB,CAAC;YAC7D,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACZ;QACJ;QACA,kBAAkB;YACd,MAAM,GAAG,aAAa,yBAAyB,CAAC;YAChD,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ,KAAK;YACjB;QACJ;QACA,OAAO;YACH,MAAM,GAAG,aAAa,YAAY,CAAC;YACnC,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ,KAAK;YACjB;QACJ;QACA,OAAO;YACH,MAAM,GAAG,aAAa,YAAY,CAAC;YACnC,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;YACZ;QACJ;QACA,mBAAmB;YACf,MAAM,GAAG,aAAa,gBAAgB,CAAC;YACvC,SAAS;gBACL,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ,KAAK;YACjB;QACJ;IACJ;AACJ;AACO,MAAM;IACT,YAAY,MAAM,EAAE,OAAO,EAAE,MAAM,CAAE;QACjC,wBAAwB,GAAG,CAAC,IAAI;QAChC,qBAAqB,GAAG,CAAC,IAAI,EAAE,CAAC;QAChC,qBAAqB,GAAG,CAAC,IAAI,EAAE,KAAK;QACpC,qBAAqB,GAAG,CAAC,IAAI,EAAE,KAAK;QACpC,uBAAuB,IAAI,EAAE,sBAAsB,QAAQ;QAC3D,uBAAuB,IAAI,EAAE,sBAAsB,QAAQ;QAC3D,IAAI,CAAC,SACD;QACJ,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG;QACtC,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;YACjD,IAAI,CAAC,KAAK,UAAU,CAAC,wBAAwB,CAAC,OAC1C;YACJ,uBAAuB,IAAI,EAAE,sBAAsB,IAAI,CAAC,KAAK,GAAG;QACpE;IACJ;IACA;;;KAGC,GACD,IAAI,QAAQ;QACR,+CAA+C;QAC/C,MAAM,aAAa,OAAO,IAAI,CAAC,uBAAuB,IAAI,EAAE,sBAAsB,MAAM,IAAI,CAAC,CAAC,GAAG;YAC7F,MAAM,UAAU,SAAS,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM;YAC/C,MAAM,UAAU,SAAS,EAAE,KAAK,CAAC,KAAK,GAAG,MAAM;YAC/C,OAAO,UAAU;QACrB;QACA,8DAA8D;QAC9D,OAAO,WAAW,GAAG,CAAC,CAAC,MAAQ,uBAAuB,IAAI,EAAE,sBAAsB,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;IACtG;IACA;;;;KAIC,GACD,MAAM,KAAK,EAAE,OAAO,EAAE;QAClB,kDAAkD;QAClD,MAAM,UAAU,uBAAuB,IAAI,EAAE,yBAAyB,KAAK,qBAAqB,IAAI,CAAC,IAAI;QACzG,uBAAuB;QACvB,MAAM,UAAU,uBAAuB,IAAI,EAAE,yBAAyB,KAAK,qBAAqB,IAAI,CAAC,IAAI,EAAE;YACvG,MAAM,uBAAuB,IAAI,EAAE,sBAAsB,KAAK,IAAI;YAClE;YACA,SAAS;gBAAE,GAAG,uBAAuB,IAAI,EAAE,sBAAsB,KAAK,OAAO;gBAAE,GAAG,OAAO;YAAC;QAC9F;QACA,iCAAiC;QACjC,KAAK,MAAM,SAAS,QAAS;YACzB,OAAO,CAAC,MAAM,IAAI,CAAC,GAAG;QAC1B;QACA,OAAO,OAAO,MAAM,CAAC;IACzB;IACA,sDAAsD,GACtD,QAAQ;QACJ,OAAO,OAAO,MAAM,CAAC,uBAAuB,IAAI,EAAE,yBAAyB,KAAK,qBAAqB,IAAI,CAAC,IAAI;IAClH;AACJ;AACA,uBAAuB,IAAI,WAAW,uBAAuB,IAAI,WAAW,uBAAuB,IAAI,WAAW,0BAA0B,IAAI,WAAW,sBAAsB,SAAS,oBAAoB,MAAM;IAChN,MAAM,aAAa,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG;IACnD,IAAI,eAAe,GAAG;QAClB,uBAAuB,IAAI,EAAE,sBAAsB,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,KAAK;QACnF,OAAO;YAAC;SAAO;IACnB;IACA,MAAM,UAAU,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACjC,MAAM,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC,EAAE,GAAG;QAClC,MAAM,QAAQ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,YAAY;QAClD,QAAQ,IAAI,CAAC;YAAE,GAAG,MAAM;YAAE;YAAM;QAAM;QACtC,uBAAuB,IAAI,EAAE,sBAAsB,IAAI,CAAC,KAAK,GAAG;IACpE;IACA,uBAAuB,IAAI,EAAE,sBAAsB,KAAK,KAAK,CAAC,2BAA2B;QACrF,SAAS,CAAC,+BAA+B,EAAE,oBAAoB,OAAO,CAAC;QACvE,iBAAiB;QACjB,WAAW,OAAO,KAAK,CAAC,MAAM;QAC9B,QAAQ,QAAQ,GAAG,CAAC,CAAC,IAAM,EAAE,KAAK,CAAC,MAAM,GAAG;IAChD;IACA,OAAO;AACX,GAAG,sBAAsB,SAAS;IAC9B,MAAM,gBAAgB,CAAC;IACvB,IAAK,MAAM,QAAQ,uBAAuB,IAAI,EAAE,sBAAsB,KAAM;QACxE,OAAO,uBAAuB,IAAI,EAAE,sBAAsB,MAAM,CAAC,KAAK;QACtE,aAAa,CAAC,KAAK,GAAG;YAClB;YACA,OAAO;YACP,SAAS;gBAAE,GAAG,uBAAuB,IAAI,EAAE,sBAAsB,KAAK,OAAO;gBAAE,QAAQ;YAAE;QAC7F;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@auth/core/errors.js"], "sourcesContent": ["/**\n * Base error class for all Auth.js errors.\n * It's optimized to be printed in the server logs in a nicely formatted way\n * via the [`logger.error`](https://authjs.dev/reference/core#logger) option.\n */\nexport class AuthError extends Error {\n    constructor(message, errorOptions) {\n        if (message instanceof Error) {\n            super(undefined, {\n                cause: { err: message, ...message.cause, ...errorOptions },\n            });\n        }\n        else if (typeof message === \"string\") {\n            if (errorOptions instanceof Error) {\n                errorOptions = { err: errorOptions, ...errorOptions.cause };\n            }\n            super(message, errorOptions);\n        }\n        else {\n            super(undefined, message);\n        }\n        this.name = this.constructor.name;\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.type = this.constructor.type ?? \"AuthError\";\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.kind = this.constructor.kind ?? \"error\";\n        Error.captureStackTrace?.(this, this.constructor);\n        const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;\n        this.message += `${this.message ? \". \" : \"\"}Read more at ${url}`;\n    }\n}\nexport class SignInError extends AuthError {\n}\nSignInError.kind = \"signIn\";\n/**\n * One of the database [`Adapter` methods](https://authjs.dev/reference/core/adapters#methods)\n * failed during execution.\n *\n * :::tip\n * If `debug: true` is set, you can check out `[auth][debug]` in the logs to learn more about the failed adapter method execution.\n * @example\n * ```sh\n * [auth][debug]: adapter_getUserByEmail\n * { \"args\": [undefined] }\n * ```\n * :::\n */\nexport class AdapterError extends AuthError {\n}\nAdapterError.type = \"AdapterError\";\n/**\n * Thrown when the execution of the [`signIn` callback](https://authjs.dev/reference/core/types#signin) fails\n * or if it returns `false`.\n */\nexport class AccessDenied extends AuthError {\n}\nAccessDenied.type = \"AccessDenied\";\n/**\n * This error occurs when the user cannot finish login.\n * Depending on the provider type, this could have happened for multiple reasons.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n *\n * For an [OAuth provider](https://authjs.dev/getting-started/authentication/oauth), possible causes are:\n * - The user denied access to the application\n * - There was an error parsing the OAuth Profile:\n *   Check out the provider's `profile` or `userinfo.request` method to make sure\n *   it correctly fetches the user's profile.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * For an [Email provider](https://authjs.dev/getting-started/authentication/email), possible causes are:\n * - The provided email/token combination was invalid/missing:\n *   Check if the provider's `sendVerificationRequest` method correctly sends the email.\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n *\n * For a [Credentials provider](https://authjs.dev/getting-started/authentication/credentials), possible causes are:\n * - The `authorize` method threw an uncaught error:\n *   Check the provider's `authorize` method.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * :::tip\n * Check out `[auth][cause]` in the error message for more details.\n * It will show the original stack trace.\n * :::\n */\nexport class CallbackRouteError extends AuthError {\n}\nCallbackRouteError.type = \"CallbackRouteError\";\n/**\n * Thrown when Auth.js is misconfigured and accidentally tried to require authentication on a custom error page.\n * To prevent an infinite loop, Auth.js will instead render its default error page.\n *\n * To fix this, make sure that the `error` page does not require authentication.\n *\n * Learn more at [Guide: Error pages](https://authjs.dev/guides/pages/error)\n */\nexport class ErrorPageLoop extends AuthError {\n}\nErrorPageLoop.type = \"ErrorPageLoop\";\n/**\n * One of the [`events` methods](https://authjs.dev/reference/core/types#eventcallbacks)\n * failed during execution.\n *\n * Make sure that the `events` methods are implemented correctly and uncaught errors are handled.\n *\n * Learn more at [`events`](https://authjs.dev/reference/core/types#eventcallbacks)\n */\nexport class EventError extends AuthError {\n}\nEventError.type = \"EventError\";\n/**\n * Thrown when Auth.js is unable to verify a `callbackUrl` value.\n * The browser either disabled cookies or the `callbackUrl` is not a valid URL.\n *\n * Somebody might have tried to manipulate the callback URL that Auth.js uses to redirect the user back to the configured `callbackUrl`/page.\n * This could be a malicious hacker trying to redirect the user to a phishing site.\n * To prevent this, Auth.js checks if the callback URL is valid and throws this error if it is not.\n *\n * There is no action required, but it might be an indicator that somebody is trying to attack your application.\n */\nexport class InvalidCallbackUrl extends AuthError {\n}\nInvalidCallbackUrl.type = \"InvalidCallbackUrl\";\n/**\n * Can be thrown from the `authorize` callback of the Credentials provider.\n * When an error occurs during the `authorize` callback, two things can happen:\n * 1. The user is redirected to the signin page, with `error=CredentialsSignin&code=credentials` in the URL. `code` is configurable.\n * 2. If you throw this error in a framework that handles form actions server-side, this error is thrown, instead of redirecting the user, so you'll need to handle.\n */\nexport class CredentialsSignin extends SignInError {\n    constructor() {\n        super(...arguments);\n        /**\n         * The error code that is set in the `code` query parameter of the redirect URL.\n         *\n         *\n         * ⚠ NOTE: This property is going to be included in the URL, so make sure it does not hint at sensitive errors.\n         *\n         * The full error is always logged on the server, if you need to debug.\n         *\n         * Generally, we don't recommend hinting specifically if the user had either a wrong username or password specifically,\n         * try rather something like \"Invalid credentials\".\n         */\n        this.code = \"credentials\";\n    }\n}\nCredentialsSignin.type = \"CredentialsSignin\";\n/**\n * One of the configured OAuth or OIDC providers is missing the `authorization`, `token` or `userinfo`, or `issuer` configuration.\n * To perform OAuth or OIDC sign in, at least one of these endpoints is required.\n *\n * Learn more at [`OAuth2Config`](https://authjs.dev/reference/core/providers#oauth2configprofile) or [Guide: OAuth Provider](https://authjs.dev/guides/configuring-oauth-providers)\n */\nexport class InvalidEndpoints extends AuthError {\n}\nInvalidEndpoints.type = \"InvalidEndpoints\";\n/**\n * Thrown when a PKCE, state or nonce OAuth check could not be performed.\n * This could happen if the OAuth provider is configured incorrectly or if the browser is blocking cookies.\n *\n * Learn more at [`checks`](https://authjs.dev/reference/core/providers#checks)\n */\nexport class InvalidCheck extends AuthError {\n}\nInvalidCheck.type = \"InvalidCheck\";\n/**\n * Logged on the server when Auth.js could not decode or encode a JWT-based (`strategy: \"jwt\"`) session.\n *\n * Possible causes are either a misconfigured `secret` or a malformed JWT or `encode/decode` methods.\n *\n * :::note\n * When this error is logged, the session cookie is destroyed.\n * :::\n *\n * Learn more at [`secret`](https://authjs.dev/reference/core#secret), [`jwt.encode`](https://authjs.dev/reference/core/jwt#encode-1) or [`jwt.decode`](https://authjs.dev/reference/core/jwt#decode-2) for more information.\n */\nexport class JWTSessionError extends AuthError {\n}\nJWTSessionError.type = \"JWTSessionError\";\n/**\n * Thrown if Auth.js is misconfigured. This could happen if you configured an Email provider but did not set up a database adapter,\n * or tried using a `strategy: \"database\"` session without a database adapter.\n * In both cases, make sure you either remove the configuration or add the missing adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database), [Email provider](https://authjs.dev/getting-started/authentication/email) or [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database-session)\n */\nexport class MissingAdapter extends AuthError {\n}\nMissingAdapter.type = \"MissingAdapter\";\n/**\n * Thrown similarily to [`MissingAdapter`](https://authjs.dev/reference/core/errors#missingadapter), but only some required methods were missing.\n *\n * Make sure you either remove the configuration or add the missing methods to the adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database)\n */\nexport class MissingAdapterMethods extends AuthError {\n}\nMissingAdapterMethods.type = \"MissingAdapterMethods\";\n/**\n * Thrown when a Credentials provider is missing the `authorize` configuration.\n * To perform credentials sign in, the `authorize` method is required.\n *\n * Learn more at [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n */\nexport class MissingAuthorize extends AuthError {\n}\nMissingAuthorize.type = \"MissingAuthorize\";\n/**\n * Auth.js requires a secret or multiple secrets to be set, but none was not found. This is used to encrypt cookies, JWTs and other sensitive data.\n *\n * :::note\n * If you are using a framework like Next.js, we try to automatically infer the secret from the `AUTH_SECRET`, `AUTH_SECRET_1`, etc. environment variables.\n * Alternatively, you can also explicitly set the [`AuthConfig.secret`](https://authjs.dev/reference/core#secret) option.\n * :::\n *\n *\n * :::tip\n * To generate a random string, you can use the Auth.js CLI: `npx auth secret`\n * :::\n */\nexport class MissingSecret extends AuthError {\n}\nMissingSecret.type = \"MissingSecret\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an OAuth account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link OAuth accounts to existing accounts if the user is not signed in.\n *\n * :::tip\n * If you trust the OAuth provider to have verified the user's email address,\n * you can enable automatic account linking by setting [`allowDangerousEmailAccountLinking: true`](https://authjs.dev/reference/core/providers#allowdangerousemailaccountlinking)\n * in the provider configuration.\n * :::\n */\nexport class OAuthAccountNotLinked extends SignInError {\n}\nOAuthAccountNotLinked.type = \"OAuthAccountNotLinked\";\n/**\n * Thrown when an OAuth provider returns an error during the sign in process.\n * This could happen for example if the user denied access to the application or there was a configuration error.\n *\n * For a full list of possible reasons, check out the specification [Authorization Code Grant: Error Response](https://www.rfc-editor.org/rfc/rfc6749#section-*******)\n */\nexport class OAuthCallbackError extends SignInError {\n}\nOAuthCallbackError.type = \"OAuthCallbackError\";\n/**\n * This error occurs during an OAuth sign in attempt when the provider's\n * response could not be parsed. This could for example happen if the provider's API\n * changed, or the [`OAuth2Config.profile`](https://authjs.dev/reference/core/providers#oauth2configprofile) method is not implemented correctly.\n */\nexport class OAuthProfileParseError extends AuthError {\n}\nOAuthProfileParseError.type = \"OAuthProfileParseError\";\n/**\n * Logged on the server when Auth.js could not retrieve a session from the database (`strategy: \"database\"`).\n *\n * The database adapter might be misconfigured or the database is not reachable.\n *\n * Learn more at [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database)\n */\nexport class SessionTokenError extends AuthError {\n}\nSessionTokenError.type = \"SessionTokenError\";\n/**\n * Happens when login by [OAuth](https://authjs.dev/getting-started/authentication/oauth) could not be started.\n *\n * Possible causes are:\n * - The Authorization Server is not compliant with the [OAuth 2.0](https://www.ietf.org/rfc/rfc6749.html) or the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *   Check the details in the error message.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n */\nexport class OAuthSignInError extends SignInError {\n}\nOAuthSignInError.type = \"OAuthSignInError\";\n/**\n * Happens when the login by an [Email provider](https://authjs.dev/getting-started/authentication/email) could not be started.\n *\n * Possible causes are:\n * - The email sent from the client is invalid, could not be normalized by [`EmailConfig.normalizeIdentifier`](https://authjs.dev/reference/core/providers/email#normalizeidentifier)\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n */\nexport class EmailSignInError extends SignInError {\n}\nEmailSignInError.type = \"EmailSignInError\";\n/**\n * Represents an error that occurs during the sign-out process. This error\n * is logged when there are issues in terminating a user's session, either\n * by failing to delete the session from the database (in database session\n * strategies) or encountering issues during other parts of the sign-out\n * process, such as emitting sign-out events or clearing session cookies.\n *\n * The session cookie(s) are emptied even if this error is logged.\n *\n */\nexport class SignOutError extends AuthError {\n}\nSignOutError.type = \"SignOutError\";\n/**\n * Auth.js was requested to handle an operation that it does not support.\n *\n * See [`AuthAction`](https://authjs.dev/reference/core/types#authaction) for the supported actions.\n */\nexport class UnknownAction extends AuthError {\n}\nUnknownAction.type = \"UnknownAction\";\n/**\n * Thrown when a Credentials provider is present but the JWT strategy (`strategy: \"jwt\"`) is not enabled.\n *\n * Learn more at [`strategy`](https://authjs.dev/reference/core#strategy) or [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n */\nexport class UnsupportedStrategy extends AuthError {\n}\nUnsupportedStrategy.type = \"UnsupportedStrategy\";\n/** Thrown when an endpoint was incorrectly called without a provider, or with an unsupported provider. */\nexport class InvalidProvider extends AuthError {\n}\nInvalidProvider.type = \"InvalidProvider\";\n/**\n * Thrown when the `trustHost` option was not set to `true`.\n *\n * Auth.js requires the `trustHost` option to be set to `true` since it's relying on the request headers' `host` value.\n *\n * :::note\n * Official Auth.js libraries might attempt to automatically set the `trustHost` option to `true` if the request is coming from a trusted host on a trusted platform.\n * :::\n *\n * Learn more at [`trustHost`](https://authjs.dev/reference/core#trusthost) or [Guide: Deployment](https://authjs.dev/getting-started/deployment)\n */\nexport class UntrustedHost extends AuthError {\n}\nUntrustedHost.type = \"UntrustedHost\";\n/**\n * The user's email/token combination was invalid.\n * This could be because the email/token combination was not found in the database,\n * or because the token has expired. Ask the user to log in again.\n */\nexport class Verification extends AuthError {\n}\nVerification.type = \"Verification\";\n/**\n * Error for missing CSRF tokens in client-side actions (`signIn`, `signOut`, `useSession#update`).\n * Thrown when actions lack the double submit cookie, essential for CSRF protection.\n *\n * CSRF ([Cross-Site Request Forgery](https://owasp.org/www-community/attacks/csrf))\n * is an attack leveraging authenticated user credentials for unauthorized actions.\n *\n * Double submit cookie pattern, a CSRF defense, requires matching values in a cookie\n * and request parameter. More on this at [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Glossary/CSRF).\n */\nexport class MissingCSRF extends SignInError {\n}\nMissingCSRF.type = \"MissingCSRF\";\nconst clientErrors = new Set([\n    \"CredentialsSignin\",\n    \"OAuthAccountNotLinked\",\n    \"OAuthCallbackError\",\n    \"AccessDenied\",\n    \"Verification\",\n    \"MissingCSRF\",\n    \"AccountNotLinked\",\n    \"WebAuthnVerificationError\",\n]);\n/**\n * Used to only allow sending a certain subset of errors to the client.\n * Errors are always logged on the server, but to prevent leaking sensitive information,\n * only a subset of errors are sent to the client as-is.\n * @internal\n */\nexport function isClientError(error) {\n    if (error instanceof AuthError)\n        return clientErrors.has(error.type);\n    return false;\n}\n/**\n * Thrown when multiple providers have `enableConditionalUI` set to `true`.\n * Only one provider can have this option enabled at a time.\n */\nexport class DuplicateConditionalUI extends AuthError {\n}\nDuplicateConditionalUI.type = \"DuplicateConditionalUI\";\n/**\n * Thrown when a WebAuthn provider has `enableConditionalUI` set to `true` but no formField has `webauthn` in its autocomplete param.\n *\n * The `webauthn` autocomplete param is required for conditional UI to work.\n */\nexport class MissingWebAuthnAutocomplete extends AuthError {\n}\nMissingWebAuthnAutocomplete.type = \"MissingWebAuthnAutocomplete\";\n/**\n * Thrown when a WebAuthn provider fails to verify a client response.\n */\nexport class WebAuthnVerificationError extends AuthError {\n}\nWebAuthnVerificationError.type = \"WebAuthnVerificationError\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link accounts to existing accounts if the user is not signed in.\n */\nexport class AccountNotLinked extends SignInError {\n}\nAccountNotLinked.type = \"AccountNotLinked\";\n/**\n * Thrown when an experimental feature is used but not enabled.\n */\nexport class ExperimentalFeatureNotEnabled extends AuthError {\n}\nExperimentalFeatureNotEnabled.type = \"ExperimentalFeatureNotEnabled\";\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,kBAAkB;IAC3B,YAAY,OAAO,EAAE,YAAY,CAAE;QAC/B,IAAI,mBAAmB,OAAO;YAC1B,KAAK,CAAC,WAAW;gBACb,OAAO;oBAAE,KAAK;oBAAS,GAAG,QAAQ,KAAK;oBAAE,GAAG,YAAY;gBAAC;YAC7D;QACJ,OACK,IAAI,OAAO,YAAY,UAAU;YAClC,IAAI,wBAAwB,OAAO;gBAC/B,eAAe;oBAAE,KAAK;oBAAc,GAAG,aAAa,KAAK;gBAAC;YAC9D;YACA,KAAK,CAAC,SAAS;QACnB,OACK;YACD,KAAK,CAAC,WAAW;QACrB;QACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,uEAAuE;QACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;QACrC,uEAAuE;QACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;QACrC,MAAM,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD,MAAM,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI;QAClE,IAAI,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,GAAG,aAAa,EAAE,KAAK;IACpE;AACJ;AACO,MAAM,oBAAoB;AACjC;AACA,YAAY,IAAI,GAAG;AAcZ,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAKb,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAwCb,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AASnB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AASd,MAAM,mBAAmB;AAChC;AACA,WAAW,IAAI,GAAG;AAWX,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AAOnB,MAAM,0BAA0B;IACnC,aAAc;QACV,KAAK,IAAI;QACT;;;;;;;;;;SAUC,GACD,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,kBAAkB,IAAI,GAAG;AAOlB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAOjB,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAYb,MAAM,wBAAwB;AACrC;AACA,gBAAgB,IAAI,GAAG;AAQhB,MAAM,uBAAuB;AACpC;AACA,eAAe,IAAI,GAAG;AAQf,MAAM,8BAA8B;AAC3C;AACA,sBAAsB,IAAI,GAAG;AAOtB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAcjB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAad,MAAM,8BAA8B;AAC3C;AACA,sBAAsB,IAAI,GAAG;AAOtB,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AAMnB,MAAM,+BAA+B;AAC5C;AACA,uBAAuB,IAAI,GAAG;AAQvB,MAAM,0BAA0B;AACvC;AACA,kBAAkB,IAAI,GAAG;AAgBlB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAWjB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAWjB,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAMb,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAMd,MAAM,4BAA4B;AACzC;AACA,oBAAoB,IAAI,GAAG;AAEpB,MAAM,wBAAwB;AACrC;AACA,gBAAgB,IAAI,GAAG;AAYhB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAMd,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAWb,MAAM,oBAAoB;AACjC;AACA,YAAY,IAAI,GAAG;AACnB,MAAM,eAAe,IAAI,IAAI;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAOM,SAAS,cAAc,KAAK;IAC/B,IAAI,iBAAiB,WACjB,OAAO,aAAa,GAAG,CAAC,MAAM,IAAI;IACtC,OAAO;AACX;AAKO,MAAM,+BAA+B;AAC5C;AACA,uBAAuB,IAAI,GAAG;AAMvB,MAAM,oCAAoC;AACjD;AACA,4BAA4B,IAAI,GAAG;AAI5B,MAAM,kCAAkC;AAC/C;AACA,0BAA0B,IAAI,GAAG;AAO1B,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAIjB,MAAM,sCAAsC;AACnD;AACA,8BAA8B,IAAI,GAAG", "ignoreList": [0]}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@auth/core/jwt.js"], "sourcesContent": ["/**\n *\n *\n * This module contains functions and types\n * to encode and decode {@link https://authjs.dev/concepts/session-strategies#jwt-session JWT}s\n * issued and used by Auth.js.\n *\n * The JWT issued by Auth.js is _encrypted by default_, using the _A256CBC-HS512_ algorithm ({@link https://www.rfc-editor.org/rfc/rfc7518.html#section-5.2.5 JWE}).\n * It uses the `AUTH_SECRET` environment variable or the passed `secret` property to derive a suitable encryption key.\n *\n * :::info Note\n * Auth.js JWTs are meant to be used by the same app that issued them.\n * If you need JWT authentication for your third-party API, you should rely on your Identity Provider instead.\n * :::\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install @auth/core\n * ```\n *\n * You can then import this submodule from `@auth/core/jwt`.\n *\n * ## Usage\n *\n * :::warning Warning\n * This module *will* be refactored/changed. We do not recommend relying on it right now.\n * :::\n *\n *\n * ## Resources\n *\n * - [What is a JWT session strategy](https://authjs.dev/concepts/session-strategies#jwt-session)\n * - [RFC7519 - JSON Web Token (JWT)](https://www.rfc-editor.org/rfc/rfc7519)\n *\n * @module jwt\n */\nimport { hkdf } from \"@panva/hkdf\";\nimport { EncryptJWT, base64url, calculateJwkThumbprint, jwtDecrypt } from \"jose\";\nimport { defaultCookies, SessionStore } from \"./lib/utils/cookie.js\";\nimport { MissingSecret } from \"./errors.js\";\nimport { parse } from \"cookie\";\nconst DEFAULT_MAX_AGE = 30 * 24 * 60 * 60; // 30 days\nconst now = () => (Date.now() / 1000) | 0;\nconst alg = \"dir\";\nconst enc = \"A256CBC-HS512\";\n/** Issues a JWT. By default, the JWT is encrypted using \"A256CBC-HS512\". */\nexport async function encode(params) {\n    const { token = {}, secret, maxAge = DEFAULT_MAX_AGE, salt } = params;\n    const secrets = Array.isArray(secret) ? secret : [secret];\n    const encryptionSecret = await getDerivedEncryptionKey(enc, secrets[0], salt);\n    const thumbprint = await calculateJwkThumbprint({ kty: \"oct\", k: base64url.encode(encryptionSecret) }, `sha${encryptionSecret.byteLength << 3}`);\n    // @ts-expect-error `jose` allows any object as payload.\n    return await new EncryptJWT(token)\n        .setProtectedHeader({ alg, enc, kid: thumbprint })\n        .setIssuedAt()\n        .setExpirationTime(now() + maxAge)\n        .setJti(crypto.randomUUID())\n        .encrypt(encryptionSecret);\n}\n/** Decodes an Auth.js issued JWT. */\nexport async function decode(params) {\n    const { token, secret, salt } = params;\n    const secrets = Array.isArray(secret) ? secret : [secret];\n    if (!token)\n        return null;\n    const { payload } = await jwtDecrypt(token, async ({ kid, enc }) => {\n        for (const secret of secrets) {\n            const encryptionSecret = await getDerivedEncryptionKey(enc, secret, salt);\n            if (kid === undefined)\n                return encryptionSecret;\n            const thumbprint = await calculateJwkThumbprint({ kty: \"oct\", k: base64url.encode(encryptionSecret) }, `sha${encryptionSecret.byteLength << 3}`);\n            if (kid === thumbprint)\n                return encryptionSecret;\n        }\n        throw new Error(\"no matching decryption secret\");\n    }, {\n        clockTolerance: 15,\n        keyManagementAlgorithms: [alg],\n        contentEncryptionAlgorithms: [enc, \"A256GCM\"],\n    });\n    return payload;\n}\nexport async function getToken(params) {\n    const { secureCookie, cookieName = defaultCookies(secureCookie ?? false).sessionToken.name, decode: _decode = decode, salt = cookieName, secret, logger = console, raw, req, } = params;\n    if (!req)\n        throw new Error(\"Must pass `req` to JWT getToken()\");\n    const headers = req.headers instanceof Headers ? req.headers : new Headers(req.headers);\n    const sessionStore = new SessionStore({ name: cookieName, options: { secure: secureCookie } }, parse(headers.get(\"cookie\") ?? \"\"), logger);\n    let token = sessionStore.value;\n    const authorizationHeader = headers.get(\"authorization\");\n    if (!token && authorizationHeader?.split(\" \")[0] === \"Bearer\") {\n        const urlEncodedToken = authorizationHeader.split(\" \")[1];\n        token = decodeURIComponent(urlEncodedToken);\n    }\n    if (!token)\n        return null;\n    if (raw)\n        return token;\n    if (!secret)\n        throw new MissingSecret(\"Must pass `secret` if not set to JWT getToken()\");\n    try {\n        return await _decode({ token, secret, salt });\n    }\n    catch {\n        return null;\n    }\n}\nasync function getDerivedEncryptionKey(enc, keyMaterial, salt) {\n    let length;\n    switch (enc) {\n        case \"A256CBC-HS512\":\n            length = 64;\n            break;\n        case \"A256GCM\":\n            length = 32;\n            break;\n        default:\n            throw new Error(\"Unsupported JWT Content Encryption Algorithm\");\n    }\n    return await hkdf(\"sha256\", keyMaterial, salt, `Auth.js Generated Encryption Key (${salt})`, length);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC;;;;;AACD;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AACA,MAAM,kBAAkB,KAAK,KAAK,KAAK,IAAI,UAAU;AACrD,MAAM,MAAM,IAAM,AAAC,KAAK,GAAG,KAAK,OAAQ;AACxC,MAAM,MAAM;AACZ,MAAM,MAAM;AAEL,eAAe,OAAO,MAAM;IAC/B,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,eAAe,EAAE,IAAI,EAAE,GAAG;IAC/D,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;KAAO;IACzD,MAAM,mBAAmB,MAAM,wBAAwB,KAAK,OAAO,CAAC,EAAE,EAAE;IACxE,MAAM,aAAa,MAAM,CAAA,GAAA,oKAAA,CAAA,yBAAsB,AAAD,EAAE;QAAE,KAAK;QAAO,GAAG,yMAAA,CAAA,YAAS,CAAC,MAAM,CAAC;IAAkB,GAAG,CAAC,GAAG,EAAE,iBAAiB,UAAU,IAAI,GAAG;IAC/I,wDAAwD;IACxD,OAAO,MAAM,IAAI,iKAAA,CAAA,aAAU,CAAC,OACvB,kBAAkB,CAAC;QAAE;QAAK;QAAK,KAAK;IAAW,GAC/C,WAAW,GACX,iBAAiB,CAAC,QAAQ,QAC1B,MAAM,CAAC,OAAO,UAAU,IACxB,OAAO,CAAC;AACjB;AAEO,eAAe,OAAO,MAAM;IAC/B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;IAChC,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;KAAO;IACzD,IAAI,CAAC,OACD,OAAO;IACX,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,OAAO,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE;QAC3D,KAAK,MAAM,UAAU,QAAS;YAC1B,MAAM,mBAAmB,MAAM,wBAAwB,KAAK,QAAQ;YACpE,IAAI,QAAQ,WACR,OAAO;YACX,MAAM,aAAa,MAAM,CAAA,GAAA,oKAAA,CAAA,yBAAsB,AAAD,EAAE;gBAAE,KAAK;gBAAO,GAAG,yMAAA,CAAA,YAAS,CAAC,MAAM,CAAC;YAAkB,GAAG,CAAC,GAAG,EAAE,iBAAiB,UAAU,IAAI,GAAG;YAC/I,IAAI,QAAQ,YACR,OAAO;QACf;QACA,MAAM,IAAI,MAAM;IACpB,GAAG;QACC,gBAAgB;QAChB,yBAAyB;YAAC;SAAI;QAC9B,6BAA6B;YAAC;YAAK;SAAU;IACjD;IACA,OAAO;AACX;AACO,eAAe,SAAS,MAAM;IACjC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,OAAO,YAAY,CAAC,IAAI,EAAE,QAAQ,UAAU,MAAM,EAAE,OAAO,UAAU,EAAE,MAAM,EAAE,SAAS,OAAO,EAAE,GAAG,EAAE,GAAG,EAAG,GAAG;IACjL,IAAI,CAAC,KACD,MAAM,IAAI,MAAM;IACpB,MAAM,UAAU,IAAI,OAAO,YAAY,UAAU,IAAI,OAAO,GAAG,IAAI,QAAQ,IAAI,OAAO;IACtF,MAAM,eAAe,IAAI,gKAAA,CAAA,eAAY,CAAC;QAAE,MAAM;QAAY,SAAS;YAAE,QAAQ;QAAa;IAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,GAAG,CAAC,aAAa,KAAK;IACnI,IAAI,QAAQ,aAAa,KAAK;IAC9B,MAAM,sBAAsB,QAAQ,GAAG,CAAC;IACxC,IAAI,CAAC,SAAS,qBAAqB,MAAM,IAAI,CAAC,EAAE,KAAK,UAAU;QAC3D,MAAM,kBAAkB,oBAAoB,KAAK,CAAC,IAAI,CAAC,EAAE;QACzD,QAAQ,mBAAmB;IAC/B;IACA,IAAI,CAAC,OACD,OAAO;IACX,IAAI,KACA,OAAO;IACX,IAAI,CAAC,QACD,MAAM,IAAI,gJAAA,CAAA,gBAAa,CAAC;IAC5B,IAAI;QACA,OAAO,MAAM,QAAQ;YAAE;YAAO;YAAQ;QAAK;IAC/C,EACA,OAAM;QACF,OAAO;IACX;AACJ;AACA,eAAe,wBAAwB,GAAG,EAAE,WAAW,EAAE,IAAI;IACzD,IAAI;IACJ,OAAQ;QACJ,KAAK;YACD,SAAS;YACT;QACJ,KAAK;YACD,SAAS;YACT;QACJ;YACI,MAAM,IAAI,MAAM;IACxB;IACA,OAAO,MAAM,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,UAAU,aAAa,MAAM,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC,EAAE;AACjG", "ignoreList": [0]}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@auth/core/node_modules/cookie/index.js"], "sourcesContent": ["/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nexports.parse = parse;\nexports.serialize = serialize;\n\n/**\n * Module variables.\n * @private\n */\n\nvar __toString = Object.prototype.toString\n\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n */\n\nvar cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n */\n\nvar cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\n\nvar domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\n\nvar pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n * @param {string} str\n * @param {object} [opt]\n * @return {object}\n * @public\n */\n\nfunction parse(str, opt) {\n  if (typeof str !== 'string') {\n    throw new TypeError('argument str must be a string');\n  }\n\n  var obj = {};\n  var len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  var dec = (opt && opt.decode) || decode;\n  var index = 0;\n  var eqIdx = 0;\n  var endIdx = 0;\n\n  do {\n    eqIdx = str.indexOf('=', index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    endIdx = str.indexOf(';', index);\n\n    if (endIdx === -1) {\n      endIdx = len;\n    } else if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(';', eqIdx - 1) + 1;\n      continue;\n    }\n\n    var keyStartIdx = startIndex(str, index, eqIdx);\n    var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    var key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (!obj.hasOwnProperty(key)) {\n      var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      var valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      if (str.charCodeAt(valStartIdx) === 0x22 /* \" */ && str.charCodeAt(valEndIdx - 1) === 0x22 /* \" */) {\n        valStartIdx++;\n        valEndIdx--;\n      }\n\n      var val = str.slice(valStartIdx, valEndIdx);\n      obj[key] = tryDecode(val, dec);\n    }\n\n    index = endIdx + 1\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str, index, max) {\n  do {\n    var code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str, index, min) {\n  while (index > min) {\n    var code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n *\n * @param {string} name\n * @param {string} val\n * @param {object} [opt]\n * @return {string}\n * @public\n */\n\nfunction serialize(name, val, opt) {\n  var enc = (opt && opt.encode) || encodeURIComponent;\n\n  if (typeof enc !== 'function') {\n    throw new TypeError('option encode is invalid');\n  }\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError('argument name is invalid');\n  }\n\n  var value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError('argument val is invalid');\n  }\n\n  var str = name + '=' + value;\n  if (!opt) return str;\n\n  if (null != opt.maxAge) {\n    var maxAge = Math.floor(opt.maxAge);\n\n    if (!isFinite(maxAge)) {\n      throw new TypeError('option maxAge is invalid')\n    }\n\n    str += '; Max-Age=' + maxAge;\n  }\n\n  if (opt.domain) {\n    if (!domainValueRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n\n    str += '; Domain=' + opt.domain;\n  }\n\n  if (opt.path) {\n    if (!pathValueRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n\n    str += '; Path=' + opt.path;\n  }\n\n  if (opt.expires) {\n    var expires = opt.expires\n\n    if (!isDate(expires) || isNaN(expires.valueOf())) {\n      throw new TypeError('option expires is invalid');\n    }\n\n    str += '; Expires=' + expires.toUTCString()\n  }\n\n  if (opt.httpOnly) {\n    str += '; HttpOnly';\n  }\n\n  if (opt.secure) {\n    str += '; Secure';\n  }\n\n  if (opt.partitioned) {\n    str += '; Partitioned'\n  }\n\n  if (opt.priority) {\n    var priority = typeof opt.priority === 'string'\n      ? opt.priority.toLowerCase() : opt.priority;\n\n    switch (priority) {\n      case 'low':\n        str += '; Priority=Low'\n        break\n      case 'medium':\n        str += '; Priority=Medium'\n        break\n      case 'high':\n        str += '; Priority=High'\n        break\n      default:\n        throw new TypeError('option priority is invalid')\n    }\n  }\n\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string'\n      ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n *\n * @param {string} str\n * @returns {string}\n */\n\nfunction decode (str) {\n  return str.indexOf('%') !== -1\n    ? decodeURIComponent(str)\n    : str\n}\n\n/**\n * Determine if value is a Date.\n *\n * @param {*} val\n * @private\n */\n\nfunction isDate (val) {\n  return __toString.call(val) === '[object Date]';\n}\n\n/**\n * Try decoding a string using a decoding function.\n *\n * @param {string} str\n * @param {function} decode\n * @private\n */\n\nfunction tryDecode(str, decode) {\n  try {\n    return decode(str);\n  } catch (e) {\n    return str;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;AAEA;;;CAGC,GAED,QAAQ,KAAK,GAAG;AAChB,QAAQ,SAAS,GAAG;AAEpB;;;CAGC,GAED,IAAI,aAAa,OAAO,SAAS,CAAC,QAAQ;AAE1C;;;;;;;;;;CAUC,GAED,IAAI,mBAAmB;AAEvB;;;;;;;;CAQC,GAED,IAAI,oBAAoB;AAExB;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,IAAI,oBAAoB;AAExB;;;;;;CAMC,GAED,IAAI,kBAAkB;AAEtB;;;;;;;;;;CAUC,GAED,SAAS,MAAM,GAAG,EAAE,GAAG;IACrB,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,MAAM,CAAC;IACX,IAAI,MAAM,IAAI,MAAM;IACpB,iGAAiG;IACjG,IAAI,MAAM,GAAG,OAAO;IAEpB,IAAI,MAAM,AAAC,OAAO,IAAI,MAAM,IAAK;IACjC,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,SAAS;IAEb,GAAG;QACD,QAAQ,IAAI,OAAO,CAAC,KAAK;QACzB,IAAI,UAAU,CAAC,GAAG,OAAO,wBAAwB;QAEjD,SAAS,IAAI,OAAO,CAAC,KAAK;QAE1B,IAAI,WAAW,CAAC,GAAG;YACjB,SAAS;QACX,OAAO,IAAI,QAAQ,QAAQ;YACzB,+BAA+B;YAC/B,QAAQ,IAAI,WAAW,CAAC,KAAK,QAAQ,KAAK;YAC1C;QACF;QAEA,IAAI,cAAc,WAAW,KAAK,OAAO;QACzC,IAAI,YAAY,SAAS,KAAK,OAAO;QACrC,IAAI,MAAM,IAAI,KAAK,CAAC,aAAa;QAEjC,mBAAmB;QACnB,IAAI,CAAC,IAAI,cAAc,CAAC,MAAM;YAC5B,IAAI,cAAc,WAAW,KAAK,QAAQ,GAAG;YAC7C,IAAI,YAAY,SAAS,KAAK,QAAQ;YAEtC,IAAI,IAAI,UAAU,CAAC,iBAAiB,KAAK,KAAK,OAAM,IAAI,UAAU,CAAC,YAAY,OAAO,KAAK,KAAK,KAAI;gBAClG;gBACA;YACF;YAEA,IAAI,MAAM,IAAI,KAAK,CAAC,aAAa;YACjC,GAAG,CAAC,IAAI,GAAG,UAAU,KAAK;QAC5B;QAEA,QAAQ,SAAS;IACnB,QAAS,QAAQ,IAAK;IAEtB,OAAO;AACT;AAEA,SAAS,WAAW,GAAG,EAAE,KAAK,EAAE,GAAG;IACjC,GAAG;QACD,IAAI,OAAO,IAAI,UAAU,CAAC;QAC1B,IAAI,SAAS,KAAK,KAAK,OAAM,SAAS,KAAK,MAAM,KAAI,OAAO;IAC9D,QAAS,EAAE,QAAQ,IAAK;IACxB,OAAO;AACT;AAEA,SAAS,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG;IAC/B,MAAO,QAAQ,IAAK;QAClB,IAAI,OAAO,IAAI,UAAU,CAAC,EAAE;QAC5B,IAAI,SAAS,KAAK,KAAK,OAAM,SAAS,KAAK,MAAM,KAAI,OAAO,QAAQ;IACtE;IACA,OAAO;AACT;AAEA;;;;;;;;;;;;;;CAcC,GAED,SAAS,UAAU,IAAI,EAAE,GAAG,EAAE,GAAG;IAC/B,IAAI,MAAM,AAAC,OAAO,IAAI,MAAM,IAAK;IAEjC,IAAI,OAAO,QAAQ,YAAY;QAC7B,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO;QAChC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,QAAQ,IAAI;IAEhB,IAAI,CAAC,kBAAkB,IAAI,CAAC,QAAQ;QAClC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,MAAM,OAAO,MAAM;IACvB,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI,QAAQ,IAAI,MAAM,EAAE;QACtB,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI,MAAM;QAElC,IAAI,CAAC,SAAS,SAAS;YACrB,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,eAAe;IACxB;IAEA,IAAI,IAAI,MAAM,EAAE;QACd,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,MAAM,GAAG;YACvC,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,cAAc,IAAI,MAAM;IACjC;IAEA,IAAI,IAAI,IAAI,EAAE;QACZ,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,IAAI,GAAG;YACnC,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,YAAY,IAAI,IAAI;IAC7B;IAEA,IAAI,IAAI,OAAO,EAAE;QACf,IAAI,UAAU,IAAI,OAAO;QAEzB,IAAI,CAAC,OAAO,YAAY,MAAM,QAAQ,OAAO,KAAK;YAChD,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,eAAe,QAAQ,WAAW;IAC3C;IAEA,IAAI,IAAI,QAAQ,EAAE;QAChB,OAAO;IACT;IAEA,IAAI,IAAI,MAAM,EAAE;QACd,OAAO;IACT;IAEA,IAAI,IAAI,WAAW,EAAE;QACnB,OAAO;IACT;IAEA,IAAI,IAAI,QAAQ,EAAE;QAChB,IAAI,WAAW,OAAO,IAAI,QAAQ,KAAK,WACnC,IAAI,QAAQ,CAAC,WAAW,KAAK,IAAI,QAAQ;QAE7C,OAAQ;YACN,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF;gBACE,MAAM,IAAI,UAAU;QACxB;IACF;IAEA,IAAI,IAAI,QAAQ,EAAE;QAChB,IAAI,WAAW,OAAO,IAAI,QAAQ,KAAK,WACnC,IAAI,QAAQ,CAAC,WAAW,KAAK,IAAI,QAAQ;QAE7C,OAAQ;YACN,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF;gBACE,MAAM,IAAI,UAAU;QACxB;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GAED,SAAS,OAAQ,GAAG;IAClB,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,IACzB,mBAAmB,OACnB;AACN;AAEA;;;;;CAKC,GAED,SAAS,OAAQ,GAAG;IAClB,OAAO,WAAW,IAAI,CAAC,SAAS;AAClC;AAEA;;;;;;CAMC,GAED,SAAS,UAAU,GAAG,EAAE,MAAM;IAC5B,IAAI;QACF,OAAO,OAAO;IAChB,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 922, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-auth/jwt.js"], "sourcesContent": ["/**\n * :::warning Not recommended\n * In NextAuth.js v5 or newer, we recommend other authentication methods server-side. Read more at: https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n * :::\n *\n * @module jwt\n */\nexport * from \"@auth/core/jwt\";\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;AACD", "ignoreList": [0]}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/ai-sdk-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/api-call-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/empty-response-body-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/get-error-message.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/invalid-argument-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/invalid-prompt-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/invalid-response-data-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/json-parse-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/load-api-key-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/load-setting-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/no-content-generated-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/no-such-model-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/too-many-embedding-values-for-call-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/type-validation-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/errors/unsupported-functionality-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider/src/json-value/is-json.ts"], "sourcesContent": ["/**\n * Symbol used for identifying AI SDK Error instances.\n * Enables checking if an error is an instance of AISDKError across package versions.\n */\nconst marker = 'vercel.ai.error';\nconst symbol = Symbol.for(marker);\n\n/**\n * Custom error class for AI SDK related errors.\n * @extends Error\n */\nexport class AISDKError extends Error {\n  private readonly [symbol] = true; // used in isInstance\n\n  /**\n   * The underlying cause of the error, if any.\n   */\n  readonly cause?: unknown;\n\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name,\n    message,\n    cause,\n  }: {\n    name: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super(message);\n\n    this.name = name;\n    this.cause = cause;\n  }\n\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error: unknown): error is AISDKError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  protected static hasMarker(error: unknown, marker: string): boolean {\n    const markerSymbol = Symbol.for(marker);\n    return (\n      error != null &&\n      typeof error === 'object' &&\n      markerSymbol in error &&\n      typeof error[markerSymbol] === 'boolean' &&\n      error[markerSymbol] === true\n    );\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_APICallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class APICallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly url: string;\n  readonly requestBodyValues: unknown;\n  readonly statusCode?: number;\n\n  readonly responseHeaders?: Record<string, string>;\n  readonly responseBody?: string;\n\n  readonly isRetryable: boolean;\n  readonly data?: unknown;\n\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null &&\n      (statusCode === 408 || // request timeout\n        statusCode === 409 || // conflict\n        statusCode === 429 || // too many requests\n        statusCode >= 500), // server error\n    data,\n  }: {\n    message: string;\n    url: string;\n    requestBodyValues: unknown;\n    statusCode?: number;\n    responseHeaders?: Record<string, string>;\n    responseBody?: string;\n    cause?: unknown;\n    isRetryable?: boolean;\n    data?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is APICallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_EmptyResponseBodyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class EmptyResponseBodyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message = 'Empty response body' }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is EmptyResponseBodyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidArgumentError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A function argument is invalid.\n */\nexport class InvalidArgumentError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly argument: string;\n\n  constructor({\n    message,\n    cause,\n    argument,\n  }: {\n    argument: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n\n    this.argument = argument;\n  }\n\n  static isInstance(error: unknown): error is InvalidArgumentError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidPromptError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * A prompt is invalid. This error should be thrown by providers when they cannot\n * process a prompt.\n */\nexport class InvalidPromptError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly prompt: unknown;\n\n  constructor({\n    prompt,\n    message,\n    cause,\n  }: {\n    prompt: unknown;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message: `Invalid prompt: ${message}`, cause });\n\n    this.prompt = prompt;\n  }\n\n  static isInstance(error: unknown): error is InvalidPromptError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_InvalidResponseDataError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * Server returned a response with invalid data content.\n * This should be thrown by providers when they cannot parse the response from the API.\n */\nexport class InvalidResponseDataError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly data: unknown;\n\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`,\n  }: {\n    data: unknown;\n    message?: string;\n  }) {\n    super({ name, message });\n\n    this.data = data;\n  }\n\n  static isInstance(error: unknown): error is InvalidResponseDataError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_JSONParseError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n// TODO v5: rename to ParseError\nexport class JSONParseError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly text: string;\n\n  constructor({ text, cause }: { text: string; cause: unknown }) {\n    super({\n      name,\n      message:\n        `JSON parsing failed: ` +\n        `Text: ${text}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.text = text;\n  }\n\n  static isInstance(error: unknown): error is JSONParseError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadAPIKeyError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadAPIKeyError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadAPIKeyError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_LoadSettingError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class LoadSettingError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({ message }: { message: string }) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is LoadSettingError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoContentGeneratedError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\nThrown when the AI provider fails to generate any content.\n */\nexport class NoContentGeneratedError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  constructor({\n    message = 'No content generated.',\n  }: { message?: string } = {}) {\n    super({ name, message });\n  }\n\n  static isInstance(error: unknown): error is NoContentGeneratedError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_NoSuchModelError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class NoSuchModelError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly modelId: string;\n  readonly modelType:\n    | 'languageModel'\n    | 'textEmbeddingModel'\n    | 'imageModel'\n    | 'transcriptionModel'\n    | 'speechModel';\n\n  constructor({\n    errorName = name,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`,\n  }: {\n    errorName?: string;\n    modelId: string;\n    modelType:\n      | 'languageModel'\n      | 'textEmbeddingModel'\n      | 'imageModel'\n      | 'transcriptionModel'\n      | 'speechModel';\n    message?: string;\n  }) {\n    super({ name: errorName, message });\n\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n\n  static isInstance(error: unknown): error is NoSuchModelError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_TooManyEmbeddingValuesForCallError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TooManyEmbeddingValuesForCallError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly provider: string;\n  readonly modelId: string;\n  readonly maxEmbeddingsPerCall: number;\n  readonly values: Array<unknown>;\n\n  constructor(options: {\n    provider: string;\n    modelId: string;\n    maxEmbeddingsPerCall: number;\n    values: Array<unknown>;\n  }) {\n    super({\n      name,\n      message:\n        `Too many values for a single embedding call. ` +\n        `The ${options.provider} model \"${options.modelId}\" can only embed up to ` +\n        `${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`,\n    });\n\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n\n  static isInstance(\n    error: unknown,\n  ): error is TooManyEmbeddingValuesForCallError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\nimport { getErrorMessage } from './get-error-message';\n\nconst name = 'AI_TypeValidationError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class TypeValidationError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly value: unknown;\n\n  constructor({ value, cause }: { value: unknown; cause: unknown }) {\n    super({\n      name,\n      message:\n        `Type validation failed: ` +\n        `Value: ${JSON.stringify(value)}.\\n` +\n        `Error message: ${getErrorMessage(cause)}`,\n      cause,\n    });\n\n    this.value = value;\n  }\n\n  static isInstance(error: unknown): error is TypeValidationError {\n    return AISDKError.hasMarker(error, marker);\n  }\n\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause,\n  }: {\n    value: unknown;\n    cause: unknown;\n  }): TypeValidationError {\n    return TypeValidationError.isInstance(cause) && cause.value === value\n      ? cause\n      : new TypeValidationError({ value, cause });\n  }\n}\n", "import { AISDKError } from './ai-sdk-error';\n\nconst name = 'AI_UnsupportedFunctionalityError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\nexport class UnsupportedFunctionalityError extends AISDKError {\n  private readonly [symbol] = true; // used in isInstance\n\n  readonly functionality: string;\n\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`,\n  }: {\n    functionality: string;\n    message?: string;\n  }) {\n    super({ name, message });\n    this.functionality = functionality;\n  }\n\n  static isInstance(error: unknown): error is UnsupportedFunctionalityError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { JSONArray, JSONObject, JSONValue } from './json-value';\n\nexport function isJSONValue(value: unknown): value is JSONValue {\n  if (\n    value === null ||\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean'\n  ) {\n    return true;\n  }\n\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n\n  if (typeof value === 'object') {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    );\n  }\n\n  return false;\n}\n\nexport function isJSONArray(value: unknown): value is JSONArray {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\n\nexport function isJSONObject(value: unknown): value is JSONObject {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    Object.entries(value).every(\n      ([key, val]) => typeof key === 'string' && isJSONValue(val),\n    )\n  );\n}\n"], "names": ["name", "marker", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a", "name", "marker", "symbol", "_a"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,SAAS;AACf,IAAM,SAAS,OAAO,GAAA,CAAI,MAAM;AALhC,IAAA;AAWO,IAAM,cAAN,MAAM,oBAAmB,MAAM;IAAA;;;;;;;GAAA,GAgBpC,YAAY,EACV,MAAAA,MAAAA,EACA,OAAA,EACA,KAAA,EACF,CAIG;QACD,KAAA,CAAM,OAAO;QAxBf,IAAA,CAAkB,GAAA,GAAU;QA0B1B,IAAA,CAAK,IAAA,GAAOA;QACZ,IAAA,CAAK,KAAA,GAAQ;IACf;IAAA;;;;GAAA,GAOA,OAAO,WAAW,KAAA,EAAqC;QACrD,OAAO,YAAW,SAAA,CAAU,OAAO,MAAM;IAC3C;IAEA,OAAiB,UAAU,KAAA,EAAgBC,QAAAA,EAAyB;QAClE,MAAM,eAAe,OAAO,GAAA,CAAIA,QAAM;QACtC,OACE,SAAS,QACT,OAAO,UAAU,YACjB,gBAAgB,SAChB,OAAO,KAAA,CAAM,YAAY,CAAA,KAAM,aAC/B,KAAA,CAAM,YAAY,CAAA,KAAM;IAE5B;AACF;AAjDoB,KAAA;AADb,IAAM,aAAN;;ACTP,IAAM,OAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmB,IAAI,EAAA;AACtC,IAAMC,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,eAAN,cAA2B,WAAW;IAa3C,YAAY,EACV,OAAA,EACA,GAAA,EACA,iBAAA,EACA,UAAA,EACA,eAAA,EACA,YAAA,EACA,KAAA,EACA,cAAc,cAAc,QAAA,CACzB,eAAe,OAAA,kBAAA;IACd,eAAe,OAAA,WAAA;IACf,eAAe,OAAA,oBAAA;IACf,cAAc,GAAA,CAAA,EAAA,eAAA;IAClB,IAAA,EACF,CAUG;QACD,KAAA,CAAM;YAAE;YAAM;YAAS;QAAM,CAAC;QArChC,IAAA,CAAkBA,IAAAA,GAAU;QAuC1B,IAAA,CAAK,GAAA,GAAM;QACX,IAAA,CAAK,iBAAA,GAAoB;QACzB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAuC;QACvD,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAnDoBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,yBAAN,cAAqC,WAAW;IAAA,qBAAA;IAGrD,YAAY,EAAE,UAAU,qBAAA,CAAsB,CAAA,GAA0B,CAAC,CAAA,CAAG;QAC1E,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAAiD;QACjE,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACPb,SAAS,gBAAgB,KAAA,EAA4B;IAC1D,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAA;IACf;IAEA,OAAO,KAAK,SAAA,CAAU,KAAK;AAC7B;;ACZA,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AASO,IAAM,uBAAN,cAAmC,WAAW;IAKnD,YAAY,EACV,OAAA,EACA,KAAA,EACA,QAAA,EACF,CAIG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;YAAS;QAAM,CAAC;QAbhC,IAAA,CAAkBG,IAAAA,GAAU;QAe1B,IAAA,CAAK,QAAA,GAAW;IAClB;IAEA,OAAO,WAAW,KAAA,EAA+C;QAC/D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AArBoBE,MAAAD;;ACRpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,qBAAN,cAAiC,WAAW;IAKjD,YAAY,EACV,MAAA,EACA,OAAA,EACA,KAAA,EACF,CAIG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM,SAAS,CAAA,gBAAA,EAAmB,OAAO,EAAA;YAAI;QAAM,CAAC;QAb9D,IAAA,CAAkBG,IAAAA,GAAU;QAe1B,IAAA,CAAK,MAAA,GAAS;IAChB;IAEA,OAAO,WAAW,KAAA,EAA6C;QAC7D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AArBoBE,MAAAD;;ACTpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAUO,IAAM,2BAAN,cAAuC,WAAW;IAKvD,YAAY,EACV,IAAA,EACA,UAAU,CAAA,uBAAA,EAA0B,KAAK,SAAA,CAAU,IAAI,CAAC,CAAA,CAAA,CAAA,EAC1D,CAGG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAXzB,IAAA,CAAkBG,IAAAA,GAAU;QAa1B,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAmD;QACnE,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAnBoBE,MAAAD;;ACRpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AALhC,IAAAE;AAQO,IAAM,iBAAN,cAA6B,WAAW;IAK7C,YAAY,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,CAAqC;QAC7D,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,2BAAA,EACS,IAAI,CAAA;eAAA,EACK,gBAAgB,KAAK,CAAC,EAAA;YAC1C;QACF,CAAC;QAZH,IAAA,CAAkBG,IAAAA,GAAU;QAc1B,IAAA,CAAK,IAAA,GAAO;IACd;IAEA,OAAO,WAAW,KAAA,EAAyC;QACzD,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AApBoBE,MAAAD;;ACPpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,kBAAN,cAA8B,WAAW;IAAA,qBAAA;IAG9C,YAAY,EAAE,OAAA,CAAQ,CAAA,CAAwB;QAC5C,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAA0C;QAC1D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,UAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,UAAS,OAAO,GAAA,CAAID,OAAM;AAJhC,IAAAE;AAMO,IAAM,mBAAN,cAA+B,WAAW;IAAA,qBAAA;IAG/C,YAAY,EAAE,OAAA,CAAQ,CAAA,CAAwB;QAC5C,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAHzB,IAAA,CAAkBG,IAAAA,GAAU;IAI5B;IAEA,OAAO,WAAW,KAAA,EAA2C;QAC3D,OAAO,WAAW,SAAA,CAAU,OAAOF,OAAM;IAC3C;AACF;AAToBE,MAAAD;;ACLpB,IAAME,QAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,KAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AASO,IAAM,0BAAN,cAAsC,WAAW;IAAA,qBAAA;IAGtD,YAAY,EACV,UAAU,uBAAA,EACZ,GAA0B,CAAC,CAAA,CAAG;QAC5B,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QALzB,IAAA,CAAkBG,KAAAA,GAAU;IAM5B;IAEA,OAAO,WAAW,KAAA,EAAkD;QAClE,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAXoBE,OAAAD;;ACRpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,mBAAN,cAA+B,WAAW;IAW/C,YAAY,EACV,YAAYH,MAAAA,EACZ,OAAA,EACA,SAAA,EACA,UAAU,CAAA,QAAA,EAAW,SAAS,CAAA,EAAA,EAAK,OAAO,EAAA,EAC5C,CAUG;QACD,KAAA,CAAM;YAAE,MAAM;YAAW;QAAQ,CAAC;QA1BpC,IAAA,CAAkBG,KAAAA,GAAU;QA4B1B,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,SAAA,GAAY;IACnB;IAEA,OAAO,WAAW,KAAA,EAA2C;QAC3D,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAnCoBE,OAAAD;;ACLpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,qCAAN,cAAiD,WAAW;IAQjE,YAAY,OAAA,CAKT;QACD,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,iDAAA,EACO,QAAQ,QAAQ,CAAA,QAAA,EAAW,QAAQ,OAAO,CAAA,uBAAA,EAC9C,QAAQ,oBAAoB,CAAA,sBAAA,EAAyB,QAAQ,MAAA,CAAO,MAAM,CAAA,sBAAA,CAAA;QACjF,CAAC;QAnBH,IAAA,CAAkBG,KAAAA,GAAU;QAqB1B,IAAA,CAAK,QAAA,GAAW,QAAQ,QAAA;QACxB,IAAA,CAAK,OAAA,GAAU,QAAQ,OAAA;QACvB,IAAA,CAAK,oBAAA,GAAuB,QAAQ,oBAAA;QACpC,IAAA,CAAK,MAAA,GAAS,QAAQ,MAAA;IACxB;IAEA,OAAO,WACL,KAAA,EAC6C;QAC7C,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAhCoBE,OAAAD;;ACJpB,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AALhC,IAAAE;AAOO,IAAM,uBAAN,MAAM,6BAA4B,WAAW;IAKlD,YAAY,EAAE,KAAA,EAAO,KAAA,CAAM,CAAA,CAAuC;QAChE,KAAA,CAAM;YACJ,MAAAH;YACA,SACE,CAAA,+BAAA,EACU,KAAK,SAAA,CAAU,KAAK,CAAC,CAAA;eAAA,EACb,gBAAgB,KAAK,CAAC,EAAA;YAC1C;QACF,CAAC;QAZH,IAAA,CAAkBG,KAAAA,GAAU;QAc1B,IAAA,CAAK,KAAA,GAAQ;IACf;IAEA,OAAO,WAAW,KAAA,EAA8C;QAC9D,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;IAAA;;;;;;;;;GAAA,GAYA,OAAO,KAAK,EACV,KAAA,EACA,KAAA,EACF,EAGwB;QACtB,OAAO,qBAAoB,UAAA,CAAW,KAAK,KAAK,MAAM,KAAA,KAAU,QAC5D,QACA,IAAI,qBAAoB;YAAE;YAAO;QAAM,CAAC;IAC9C;AACF;AA1CoBE,OAAAD;AADb,IAAM,sBAAN;;ACLP,IAAME,SAAO;AACb,IAAMC,WAAS,CAAA,gBAAA,EAAmBD,MAAI,EAAA;AACtC,IAAME,WAAS,OAAO,GAAA,CAAID,QAAM;AAJhC,IAAAE;AAMO,IAAM,gCAAN,cAA4C,WAAW;IAK5D,YAAY,EACV,aAAA,EACA,UAAU,CAAA,CAAA,EAAI,aAAa,CAAA,8BAAA,CAAA,EAC7B,CAGG;QACD,KAAA,CAAM;YAAE,MAAAH;YAAM;QAAQ,CAAC;QAXzB,IAAA,CAAkBG,KAAAA,GAAU;QAY1B,IAAA,CAAK,aAAA,GAAgB;IACvB;IAEA,OAAO,WAAW,KAAA,EAAwD;QACxE,OAAO,WAAW,SAAA,CAAU,OAAOF,QAAM;IAC3C;AACF;AAlBoBE,OAAAD;;ACLb,SAAS,YAAY,KAAA,EAAoC;IAC9D,IACE,UAAU,QACV,OAAO,UAAU,YACjB,OAAO,UAAU,YACjB,OAAO,UAAU,WACjB;QACA,OAAO;IACT;IAEA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACxB,OAAO,MAAM,KAAA,CAAM,WAAW;IAChC;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,OAAO,OAAA,CAAQ,KAAK,EAAE,KAAA,CAC3B,CAAC,CAAC,KAAK,GAAG,CAAA,GAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;IAE9D;IAEA,OAAO;AACT;AAEO,SAAS,YAAY,KAAA,EAAoC;IAC9D,OAAO,MAAM,OAAA,CAAQ,KAAK,KAAK,MAAM,KAAA,CAAM,WAAW;AACxD;AAEO,SAAS,aAAa,KAAA,EAAqC;IAChE,OACE,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,OAAA,CAAQ,KAAK,EAAE,KAAA,CACpB,CAAC,CAAC,KAAK,GAAG,CAAA,GAAM,OAAO,QAAQ,YAAY,YAAY,GAAG;AAGhE", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["turbopack:///[project]/node_modules/eventsource-parser/src/errors.ts", "turbopack:///[project]/node_modules/eventsource-parser/src/parse.ts"], "sourcesContent": ["/**\n * The type of error that occurred.\n * @public\n */\nexport type ErrorType = 'invalid-retry' | 'unknown-field'\n\n/**\n * Error thrown when encountering an issue during parsing.\n *\n * @public\n */\nexport class ParseError extends Error {\n  /**\n   * The type of error that occurred.\n   */\n  type: ErrorType\n\n  /**\n   * In the case of an unknown field encountered in the stream, this will be the field name.\n   */\n  field?: string\n\n  /**\n   * In the case of an unknown field encountered in the stream, this will be the value of the field.\n   */\n  value?: string\n\n  /**\n   * The line that caused the error, if available.\n   */\n  line?: string\n\n  constructor(\n    message: string,\n    options: {type: ErrorType; field?: string; value?: string; line?: string},\n  ) {\n    super(message)\n    this.name = 'ParseError'\n    this.type = options.type\n    this.field = options.field\n    this.value = options.value\n    this.line = options.line\n  }\n}\n", "/**\n * EventSource/Server-Sent Events parser\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nimport {ParseError} from './errors.ts'\nimport type {EventSourceParser, ParserCallbacks} from './types.ts'\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction noop(_arg: unknown) {\n  // intentional noop\n}\n\n/**\n * Creates a new EventSource parser.\n *\n * @param callbacks - Callbacks to invoke on different parsing events:\n *   - `onEvent` when a new event is parsed\n *   - `onError` when an error occurs\n *   - `onRetry` when a new reconnection interval has been sent from the server\n *   - `onComment` when a comment is encountered in the stream\n *\n * @returns A new EventSource parser, with `parse` and `reset` methods.\n * @public\n */\nexport function createParser(callbacks: ParserCallbacks): EventSourceParser {\n  if (typeof callbacks === 'function') {\n    throw new TypeError(\n      '`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?',\n    )\n  }\n\n  const {onEvent = noop, onError = noop, onRetry = noop, onComment} = callbacks\n\n  let incompleteLine = ''\n\n  let isFirstChunk = true\n  let id: string | undefined\n  let data = ''\n  let eventType = ''\n\n  function feed(newChunk: string) {\n    // Strip any UTF8 byte order mark (BOM) at the start of the stream\n    const chunk = isFirstChunk ? newChunk.replace(/^\\xEF\\xBB\\xBF/, '') : newChunk\n\n    // If there was a previous incomplete line, append it to the new chunk,\n    // so we may process it together as a new (hopefully complete) chunk.\n    const [complete, incomplete] = splitLines(`${incompleteLine}${chunk}`)\n\n    for (const line of complete) {\n      parseLine(line)\n    }\n\n    incompleteLine = incomplete\n    isFirstChunk = false\n  }\n\n  function parseLine(line: string) {\n    // If the line is empty (a blank line), dispatch the event\n    if (line === '') {\n      dispatchEvent()\n      return\n    }\n\n    // If the line starts with a U+003A COLON character (:), ignore the line.\n    if (line.startsWith(':')) {\n      if (onComment) {\n        onComment(line.slice(line.startsWith(': ') ? 2 : 1))\n      }\n      return\n    }\n\n    // If the line contains a U+003A COLON character (:)\n    const fieldSeparatorIndex = line.indexOf(':')\n    if (fieldSeparatorIndex !== -1) {\n      // Collect the characters on the line before the first U+003A COLON character (:),\n      // and let `field` be that string.\n      const field = line.slice(0, fieldSeparatorIndex)\n\n      // Collect the characters on the line after the first U+003A COLON character (:),\n      // and let `value` be that string. If value starts with a U+0020 SPACE character,\n      // remove it from value.\n      const offset = line[fieldSeparatorIndex + 1] === ' ' ? 2 : 1\n      const value = line.slice(fieldSeparatorIndex + offset)\n\n      processField(field, value, line)\n      return\n    }\n\n    // Otherwise, the string is not empty but does not contain a U+003A COLON character (:)\n    // Process the field using the whole line as the field name, and an empty string as the field value.\n    // 👆 This is according to spec. That means that a line that has the value `data` will result in\n    // a newline being added to the current `data` buffer, for instance.\n    processField(line, '', line)\n  }\n\n  function processField(field: string, value: string, line: string) {\n    // Field names must be compared literally, with no case folding performed.\n    switch (field) {\n      case 'event':\n        // Set the `event type` buffer to field value\n        eventType = value\n        break\n      case 'data':\n        // Append the field value to the `data` buffer, then append a single U+000A LINE FEED(LF)\n        // character to the `data` buffer.\n        data = `${data}${value}\\n`\n        break\n      case 'id':\n        // If the field value does not contain U+0000 NULL, then set the `ID` buffer to\n        // the field value. Otherwise, ignore the field.\n        id = value.includes('\\0') ? undefined : value\n        break\n      case 'retry':\n        // If the field value consists of only ASCII digits, then interpret the field value as an\n        // integer in base ten, and set the event stream's reconnection time to that integer.\n        // Otherwise, ignore the field.\n        if (/^\\d+$/.test(value)) {\n          onRetry(parseInt(value, 10))\n        } else {\n          onError(\n            new ParseError(`Invalid \\`retry\\` value: \"${value}\"`, {\n              type: 'invalid-retry',\n              value,\n              line,\n            }),\n          )\n        }\n        break\n      default:\n        // Otherwise, the field is ignored.\n        onError(\n          new ParseError(\n            `Unknown field \"${field.length > 20 ? `${field.slice(0, 20)}…` : field}\"`,\n            {type: 'unknown-field', field, value, line},\n          ),\n        )\n        break\n    }\n  }\n\n  function dispatchEvent() {\n    const shouldDispatch = data.length > 0\n    if (shouldDispatch) {\n      onEvent({\n        id,\n        event: eventType || undefined,\n        // If the data buffer's last character is a U+000A LINE FEED (LF) character,\n        // then remove the last character from the data buffer.\n        data: data.endsWith('\\n') ? data.slice(0, -1) : data,\n      })\n    }\n\n    // Reset for the next event\n    id = undefined\n    data = ''\n    eventType = ''\n  }\n\n  function reset(options: {consume?: boolean} = {}) {\n    if (incompleteLine && options.consume) {\n      parseLine(incompleteLine)\n    }\n\n    isFirstChunk = true\n    id = undefined\n    data = ''\n    eventType = ''\n    incompleteLine = ''\n  }\n\n  return {feed, reset}\n}\n\n/**\n * For the given `chunk`, split it into lines according to spec, and return any remaining incomplete line.\n *\n * @param chunk - The chunk to split into lines\n * @returns A tuple containing an array of complete lines, and any remaining incomplete line\n * @internal\n */\nfunction splitLines(chunk: string): [complete: Array<string>, incomplete: string] {\n  /**\n   * According to the spec, a line is terminated by either:\n   * - U+000D CARRIAGE RETURN U+000A LINE FEED (CRLF) character pair\n   * - a single U+000A LINE FEED(LF) character not preceded by a U+000D CARRIAGE RETURN(CR) character\n   * - a single U+000D CARRIAGE RETURN(CR) character not followed by a U+000A LINE FEED(LF) character\n   */\n  const lines: Array<string> = []\n  let incompleteLine = ''\n  let searchIndex = 0\n\n  while (searchIndex < chunk.length) {\n    // Find next line terminator\n    const crIndex = chunk.indexOf('\\r', searchIndex)\n    const lfIndex = chunk.indexOf('\\n', searchIndex)\n\n    // Determine line end\n    let lineEnd = -1\n    if (crIndex !== -1 && lfIndex !== -1) {\n      // CRLF case\n      lineEnd = Math.min(crIndex, lfIndex)\n    } else if (crIndex !== -1) {\n      lineEnd = crIndex\n    } else if (lfIndex !== -1) {\n      lineEnd = lfIndex\n    }\n\n    // Extract line if terminator found\n    if (lineEnd === -1) {\n      // No terminator found, rest is incomplete\n      incompleteLine = chunk.slice(searchIndex)\n      break\n    } else {\n      const line = chunk.slice(searchIndex, lineEnd)\n      lines.push(line)\n\n      // Move past line terminator\n      searchIndex = lineEnd + 1\n      if (chunk[searchIndex - 1] === '\\r' && chunk[searchIndex] === '\\n') {\n        searchIndex++\n      }\n    }\n  }\n\n  return [lines, incompleteLine]\n}\n"], "names": [], "mappings": ";;;;AAWO,MAAM,mBAAmB,MAAM;IAqBpC,YACE,OAAA,EACA,OAAA,CACA;QACA,KAAA,CAAM,OAAO,GACb,IAAA,CAAK,IAAA,GAAO,cACZ,IAAA,CAAK,IAAA,GAAO,QAAQ,IAAA,EACpB,IAAA,CAAK,KAAA,GAAQ,QAAQ,KAAA,EACrB,IAAA,CAAK,KAAA,GAAQ,QAAQ,KAAA,EACrB,IAAA,CAAK,IAAA,GAAO,QAAQ,IAAA;IAAA;AAExB;ACnCA,SAAS,KAAK,IAAA,EAAe,CAE7B;AAcO,SAAS,aAAa,SAAA,EAA+C;IAC1E,IAAI,OAAO,aAAc,YACvB,MAAM,IAAI,UACR;IAIE,MAAA,EAAC,UAAU,IAAA,EAAM,UAAU,IAAA,EAAM,UAAU,IAAA,EAAM,SAAA,EAAA,GAAa;IAEpE,IAAI,iBAAiB,IAEjB,eAAe,CAAA,GACf,IACA,OAAO,IACP,YAAY;IAEhB,SAAS,KAAK,QAAA,EAAkB;QAE9B,MAAM,QAAQ,eAAe,SAAS,OAAA,CAAQ,iBAAiB,EAAE,IAAI,UAI/D,CAAC,UAAU,UAAU,CAAA,GAAI,WAAW,GAAG,cAAc,GAAG,KAAK,EAAE;QAErE,KAAA,MAAW,QAAQ,SACjB,UAAU,IAAI;QAGhB,iBAAiB,YACjB,eAAe,CAAA;IAAA;IAGjB,SAAS,UAAU,IAAA,EAAc;QAE/B,IAAI,SAAS,IAAI;YACD,cAAA;YACd;QAAA;QAIE,IAAA,KAAK,UAAA,CAAW,GAAG,GAAG;YACpB,aACF,UAAU,KAAK,KAAA,CAAM,KAAK,UAAA,CAAW,IAAI,IAAI,IAAI,CAAC,CAAC;YAErD;QAAA;QAII,MAAA,sBAAsB,KAAK,OAAA,CAAQ,GAAG;QAC5C,IAAI,wBAAwB,CAAA,GAAI;YAG9B,MAAM,QAAQ,KAAK,KAAA,CAAM,GAAG,mBAAmB,GAKzC,SAAS,IAAA,CAAK,sBAAsB,CAAC,CAAA,KAAM,MAAM,IAAI,GACrD,QAAQ,KAAK,KAAA,CAAM,sBAAsB,MAAM;YAExC,aAAA,OAAO,OAAO,IAAI;YAC/B;QAAA;QAOW,aAAA,MAAM,IAAI,IAAI;IAAA;IAGpB,SAAA,aAAa,KAAA,EAAe,KAAA,EAAe,IAAA,EAAc;QAEhE,OAAQ,OAAO;YACb,KAAK;gBAES,YAAA;gBACZ;YACF,KAAK;gBAGI,OAAA,GAAG,IAAI,GAAG,KAAK,CAAA;AAAA,CAAA;gBACtB;YACF,KAAK;gBAGH,KAAK,MAAM,QAAA,CAAS,IAAI,IAAI,KAAA,IAAY;gBACxC;YACF,KAAK;gBAIC,QAAQ,IAAA,CAAK,KAAK,IACpB,QAAQ,SAAS,OAAO,EAAE,CAAC,IAE3B,QACE,IAAI,WAAW,CAAA,0BAAA,EAA6B,KAAK,CAAA,CAAA,CAAA,EAAK;oBACpD,MAAM;oBACN;oBACA;gBACD,CAAA;gBAGL;YACF;gBAEE,QACE,IAAI,WACF,CAAA,eAAA,EAAkB,MAAM,MAAA,GAAS,KAAK,GAAG,MAAM,KAAA,CAAM,GAAG,EAAE,CAAC,CAAA,MAAA,CAAA,GAAM,KAAK,CAAA,CAAA,CAAA,EACtE;oBAAC,MAAM;oBAAiB;oBAAO;oBAAO;gBAAI;gBAG9C;QAAA;IACJ;IAGF,SAAS,gBAAgB;QACA,KAAK,MAAA,GAAS,KAEnC,QAAQ;YACN;YACA,OAAO,aAAa,KAAA;YAAA,4EAAA;YAAA,uDAAA;YAGpB,MAAM,KAAK,QAAA,CAAS,CAAA;AAAA,CAAI,IAAI,KAAK,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI;QAAA,CACjD,GAIH,KAAK,KAAA,GACL,OAAO,IACP,YAAY;IAAA;IAGL,SAAA,MAAM,UAA+B,CAAA,CAAA,EAAI;QAC5C,kBAAkB,QAAQ,OAAA,IAC5B,UAAU,cAAc,GAG1B,eAAe,CAAA,GACf,KAAK,KAAA,GACL,OAAO,IACP,YAAY,IACZ,iBAAiB;IAAA;IAGZ,OAAA;QAAC;QAAM;IAAK;AACrB;AASA,SAAS,WAAW,KAAA,EAA8D;IAOhF,MAAM,QAAuB,CAAC,CAAA;IAC1B,IAAA,iBAAiB,IACjB,cAAc;IAEX,MAAA,cAAc,MAAM,MAAA,EAAQ;QAE3B,MAAA,UAAU,MAAM,OAAA,CAAQ,MAAM,WAAW,GACzC,UAAU,MAAM,OAAA,CAAQ,CAAA;AAAA,CAAA,EAAM,WAAW;QAG/C,IAAI,UAAU,CAAA;QAWd,IAVI,YAAY,CAAA,KAAM,YAAY,CAAA,IAEhC,UAAU,KAAK,GAAA,CAAI,SAAS,OAAO,IAC1B,YAAY,CAAA,IACrB,UAAU,UACD,YAAY,CAAA,KAAA,CACrB,UAAU,OAAA,GAIR,YAAY,CAAA,GAAI;YAED,iBAAA,MAAM,KAAA,CAAM,WAAW;YACxC;QAAA,OACK;YACL,MAAM,OAAO,MAAM,KAAA,CAAM,aAAa,OAAO;YAC7C,MAAM,IAAA,CAAK,IAAI,GAGf,cAAc,UAAU,GACpB,KAAA,CAAM,cAAc,CAAC,CAAA,KAAM,QAAQ,KAAA,CAAM,WAAW,CAAA,KAAM,CAAA;AAAA,CAAA,IAC5D;QAAA;IAEJ;IAGK,OAAA;QAAC;QAAO,cAAc;KAAA;AAC/B", "ignoreList": [0, 1]}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "file": "stream.js", "sources": ["turbopack:///[project]/node_modules/eventsource-parser/src/stream.ts"], "sourcesContent": ["import {createParser} from './parse.ts'\nimport type {EventSourceMessage, EventSourceParser} from './types.ts'\n\n/**\n * Options for the EventSourceParserStream.\n *\n * @public\n */\nexport interface StreamOptions {\n  /**\n   * Behavior when a parsing error occurs.\n   *\n   * - A custom function can be provided to handle the error.\n   * - `'terminate'` will error the stream and stop parsing.\n   * - Any other value will ignore the error and continue parsing.\n   *\n   * @defaultValue `undefined`\n   */\n  onError?: 'terminate' | ((error: Error) => void)\n\n  /**\n   * Callback for when a reconnection interval is sent from the server.\n   *\n   * @param retry - The number of milliseconds to wait before reconnecting.\n   */\n  onRetry?: (retry: number) => void\n\n  /**\n   * Callback for when a comment is encountered in the stream.\n   *\n   * @param comment - The comment encountered in the stream.\n   */\n  onComment?: (comment: string) => void\n}\n\n/**\n * A TransformStream that ingests a stream of strings and produces a stream of `EventSourceMessage`.\n *\n * @example Basic usage\n * ```\n * const eventStream =\n *   response.body\n *     .pipeThrough(new TextDecoderStream())\n *     .pipeThrough(new EventSourceParserStream())\n * ```\n *\n * @example Terminate stream on parsing errors\n * ```\n * const eventStream =\n *  response.body\n *   .pipeThrough(new TextDecoderStream())\n *   .pipeThrough(new EventSourceParserStream({terminateOnError: true}))\n * ```\n *\n * @public\n */\nexport class EventSourceParserStream extends TransformStream<string, EventSourceMessage> {\n  constructor({onError, onRetry, onComment}: StreamOptions = {}) {\n    let parser!: EventSourceParser\n\n    super({\n      start(controller) {\n        parser = createParser({\n          onEvent: (event) => {\n            controller.enqueue(event)\n          },\n          onError(error) {\n            if (onError === 'terminate') {\n              controller.error(error)\n            } else if (typeof onError === 'function') {\n              onError(error)\n            }\n\n            // Ignore by default\n          },\n          onRetry,\n          onComment,\n        })\n      },\n      transform(chunk) {\n        parser.feed(chunk)\n      },\n    })\n  }\n}\n\nexport {type ErrorType, ParseError} from './errors.ts'\nexport type {EventSourceMessage} from './types.ts'\n"], "names": [], "mappings": ";;;;;;AAwDO,MAAM,gCAAgC,gBAA4C;IACvF,YAAY,EAAC,OAAA,EAAS,OAAA,EAAS,SAAA,CAAS,CAAA,GAAmB,CAAA,CAAA,CAAI;QACzD,IAAA;QAEE,KAAA,CAAA;YACJ,OAAM,UAAA,EAAY;gBAChB,4KAAS,eAAA,EAAa;oBACpB,SAAS,CAAC,UAAU;wBAClB,WAAW,OAAA,CAAQ,KAAK;oBAC1B;oBACA,SAAQ,KAAA,EAAO;wBACT,YAAY,cACd,WAAW,KAAA,CAAM,KAAK,IACb,OAAO,WAAY,cAC5B,QAAQ,KAAK;oBAIjB;oBACA;oBACA;gBAAA,CACD;YACH;YACA,WAAU,KAAA,EAAO;gBACf,OAAO,IAAA,CAAK,KAAK;YAAA;QACnB,CACD;IAAA;AAEL", "ignoreList": [0]}}, {"offset": {"line": 1470, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/combine-headers.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/convert-async-iterator-to-readable-stream.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/delay.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/extract-response-headers.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/generate-id.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/get-error-message.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/get-from-api.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/handle-fetch-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/is-abort-error.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/remove-undefined-entries.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/is-url-supported.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/load-api-key.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/load-optional-setting.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/load-setting.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/parse-json.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/secure-json-parse.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/validate-types.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/validator.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/parse-json-event-stream.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/parse-provider-options.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/post-to-api.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/types/tool.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/provider-defined-tool-factory.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/resolve.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/response-handler.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/zod-schema.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/schema.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/uint8-utils.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/without-trailing-slash.ts", "turbopack:///[project]/node_modules/@ai-sdk/provider-utils/src/index.ts"], "sourcesContent": ["export function combineHeaders(\n  ...headers: Array<Record<string, string | undefined> | undefined>\n): Record<string, string | undefined> {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...(currentHeaders ?? {}),\n    }),\n    {},\n  ) as Record<string, string | undefined>;\n}\n", "/**\n * Converts an AsyncIterator to a ReadableStream.\n *\n * @template T - The type of elements produced by the AsyncIterator.\n * @param { <T>} iterator - The AsyncIterator to convert.\n * @returns {ReadableStream<T>} - A ReadableStream that provides the same data as the AsyncIterator.\n */\nexport function convertAsyncIteratorToReadableStream<T>(\n  iterator: AsyncIterator<T>,\n): ReadableStream<T> {\n  return new ReadableStream<T>({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {},\n  });\n}\n", "/**\n * Creates a Promise that resolves after a specified delay\n * @param delayInMs - The delay duration in milliseconds. If null or undefined, resolves immediately.\n * @returns A Promise that resolves after the specified delay\n */\nexport async function delay(delayInMs?: number | null): Promise<void> {\n  return delayInMs == null\n    ? Promise.resolve()\n    : new Promise(resolve => setTimeout(resolve, delayInMs));\n}\n", "/**\nExtracts the headers from a response object and returns them as a key-value object.\n\n@param response - The response object to extract headers from.\n@returns The headers as a key-value object.\n*/\nexport function extractResponseHeaders(response: Response) {\n  return Object.fromEntries<string>([...response.headers]);\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\n\n/**\nCreates an ID generator.\nThe total length of the ID is the sum of the prefix, separator, and random part length.\nNot cryptographically secure.\n\n@param alphabet - The alphabet to use for the ID. Default: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.\n@param prefix - The prefix of the ID to generate. Optional.\n@param separator - The separator between the prefix and the random part of the ID. Default: '-'.\n@param size - The size of the random part of the ID to generate. Default: 16.\n */\nexport const createIdGenerator = ({\n  prefix,\n  size = 16,\n  alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  separator = '-',\n}: {\n  prefix?: string;\n  separator?: string;\n  size?: number;\n  alphabet?: string;\n} = {}): IdGenerator => {\n  const generator = () => {\n    const alphabetLength = alphabet.length;\n    const chars = new Array(size);\n    for (let i = 0; i < size; i++) {\n      chars[i] = alphabet[(Math.random() * alphabetLength) | 0];\n    }\n    return chars.join('');\n  };\n\n  if (prefix == null) {\n    return generator;\n  }\n\n  // check that the prefix is not part of the alphabet (otherwise prefix checking can fail randomly)\n  if (alphabet.includes(separator)) {\n    throw new InvalidArgumentError({\n      argument: 'separator',\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`,\n    });\n  }\n\n  return () => `${prefix}${separator}${generator()}`;\n};\n\n/**\nA function that generates an ID.\n */\nexport type IdGenerator = () => string;\n\n/**\nGenerates a 16-character random string to use for IDs.\nNot cryptographically secure.\n */\nexport const generateId = createIdGenerator();\n", "export function getErrorMessage(error: unknown | undefined) {\n  if (error == null) {\n    return 'unknown error';\n  }\n\n  if (typeof error === 'string') {\n    return error;\n  }\n\n  if (error instanceof Error) {\n    return error.message;\n  }\n\n  return JSON.stringify(error);\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { handleFetchError } from './handle-fetch-error';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const getFromApi = async <T>({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {},\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {},\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {},\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {},\n      });\n    }\n  } catch (error) {\n    throw handleFetchError({ error, url, requestBodyValues: {} });\n  }\n};\n", "import { APICallError } from '@ai-sdk/provider';\nimport { isAbortError } from './is-abort-error';\n\nconst FETCH_FAILED_ERROR_MESSAGES = ['fetch failed', 'failed to fetch'];\n\nexport function handleFetchError({\n  error,\n  url,\n  requestBodyValues,\n}: {\n  error: unknown;\n  url: string;\n  requestBodyValues: unknown;\n}) {\n  if (isAbortError(error)) {\n    return error;\n  }\n\n  // unwrap original error when fetch failed (for easier debugging):\n  if (\n    error instanceof TypeError &&\n    FETCH_FAILED_ERROR_MESSAGES.includes(error.message.toLowerCase())\n  ) {\n    const cause = (error as any).cause;\n\n    if (cause != null) {\n      // Failed to connect to server:\n      return new APICallError({\n        message: `Cannot connect to API: ${cause.message}`,\n        cause,\n        url,\n        requestBodyValues,\n        isRetryable: true, // retry when network error\n      });\n    }\n  }\n\n  return error;\n}\n", "export function isAbortError(error: unknown): error is Error {\n  return (\n    error instanceof Error &&\n    (error.name === 'AbortError' || error.name === 'TimeoutError')\n  );\n}\n", "/**\n * Removes entries from a record where the value is null or undefined.\n * @param record - The input object whose entries may be null or undefined.\n * @returns A new object containing only entries with non-null and non-undefined values.\n */\nexport function removeUndefinedEntries<T>(\n  record: Record<string, T | undefined>,\n): Record<string, T> {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null),\n  ) as Record<string, T>;\n}\n", "/**\n * Checks if the given URL is supported natively by the model.\n *\n * @param mediaType - The media type of the URL. Case-sensitive.\n * @param url - The URL to check.\n * @param supportedUrls - A record where keys are case-sensitive media types (or '*')\n *                        and values are arrays of RegExp patterns for URLs.\n *\n * @returns `true` if the URL matches a pattern under the specific media type\n *          or the wildcard '*', `false` otherwise.\n */\nexport function isUrlSupported({\n  mediaType,\n  url,\n  supportedUrls,\n}: {\n  mediaType: string;\n  url: string;\n  supportedUrls: Record<string, RegExp[]>;\n}): boolean {\n  // standardize media type and url to lower case\n  url = url.toLowerCase();\n  mediaType = mediaType.toLowerCase();\n\n  return (\n    Object.entries(supportedUrls)\n      // standardize supported url map into lowercase prefixes:\n      .map(([key, value]) => {\n        const mediaType = key.toLowerCase();\n        return mediaType === '*' || mediaType === '*/*'\n          ? { mediaTypePrefix: '', regexes: value }\n          : { mediaTypePrefix: mediaType.replace(/\\*/, ''), regexes: value };\n      })\n      // gather all regexp pattern from matched media type prefixes:\n      .filter(({ mediaTypePrefix }) => mediaType.startsWith(mediaTypePrefix))\n      .flatMap(({ regexes }) => regexes)\n      // check if any pattern matches the url:\n      .some(pattern => pattern.test(url))\n  );\n}\n", "import { LoadAPIKeyError } from '@ai-sdk/provider';\n\nexport function loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = 'apiKey',\n  description,\n}: {\n  apiKey: string | undefined;\n  environmentVariableName: string;\n  apiKeyParameterName?: string;\n  description: string;\n}): string {\n  if (typeof apiKey === 'string') {\n    return apiKey;\n  }\n\n  if (apiKey != null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`,\n    });\n  }\n\n  apiKey = process.env[environmentVariableName];\n\n  if (apiKey == null) {\n    throw new LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof apiKey !== 'string') {\n    throw new LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return apiKey;\n}\n", "/**\n * Loads an optional `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @returns The setting value.\n */\nexport function loadOptionalSetting({\n  settingValue,\n  environmentVariableName,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n}): string | undefined {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null || typeof process === 'undefined') {\n    return undefined;\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null || typeof settingValue !== 'string') {\n    return undefined;\n  }\n\n  return settingValue;\n}\n", "import { LoadSettingError } from '@ai-sdk/provider';\n\n/**\n * Loads a `string` setting from the environment or a parameter.\n *\n * @param settingValue - The setting value.\n * @param environmentVariableName - The environment variable name.\n * @param settingName - The setting name.\n * @param description - The description of the setting.\n * @returns The setting value.\n */\nexport function loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description,\n}: {\n  settingValue: string | undefined;\n  environmentVariableName: string;\n  settingName: string;\n  description: string;\n}): string {\n  if (typeof settingValue === 'string') {\n    return settingValue;\n  }\n\n  if (settingValue != null) {\n    throw new LoadSettingError({\n      message: `${description} setting must be a string.`,\n    });\n  }\n\n  if (typeof process === 'undefined') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter. ` +\n        `Environment variables is not supported in this environment.`,\n    });\n  }\n\n  settingValue = process.env[environmentVariableName];\n\n  if (settingValue == null) {\n    throw new LoadSettingError({\n      message:\n        `${description} setting is missing. ` +\n        `Pass it using the '${settingName}' parameter ` +\n        `or the ${environmentVariableName} environment variable.`,\n    });\n  }\n\n  if (typeof settingValue !== 'string') {\n    throw new LoadSettingError({\n      message:\n        `${description} setting must be a string. ` +\n        `The value of the ${environmentVariableName} environment variable is not a string.`,\n    });\n  }\n\n  return settingValue;\n}\n", "import {\n  JSONParseError,\n  <PERSON>SO<PERSON><PERSON><PERSON><PERSON>,\n  TypeValidationError,\n} from '@ai-sdk/provider';\nimport { secureJsonParse } from './secure-json-parse';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport { safeValidateTypes, validateTypes } from './validate-types';\nimport { Validator } from './validator';\nimport { InferSchema } from './schema';\n\n/**\n * Parses a JSON string into an unknown object.\n *\n * @param text - The JSON string to parse.\n * @returns {JSONValue} - The parsed JSON object.\n */\nexport async function parseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): Promise<JSONValue>;\n/**\n * Parses a JSON string into a strongly-typed object using the provided schema.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns {Promise<T>} - The parsed object.\n */\nexport async function parseJSON<T>(options: {\n  text: string;\n  schema: z4.ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<T>;\nexport async function parseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: z4.ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<T> {\n  try {\n    const value = secureJsonParse(text);\n\n    if (schema == null) {\n      return value;\n    }\n\n    return validateTypes<T>({ value, schema });\n  } catch (error) {\n    if (\n      JSONParseError.isInstance(error) ||\n      TypeValidationError.isInstance(error)\n    ) {\n      throw error;\n    }\n\n    throw new JSONParseError({ text, cause: error });\n  }\n}\n\nexport type ParseResult<T> =\n  | { success: true; value: T; rawValue: unknown }\n  | {\n      success: false;\n      error: JSONParseError | TypeValidationError;\n      rawValue: unknown;\n    };\n\n/**\n * Safely parses a JSON string and returns the result as an object of type `unknown`.\n *\n * @param text - The JSON string to parse.\n * @returns {Promise<object>} Either an object with `success: true` and the parsed data, or an object with `success: false` and the error that occurred.\n */\nexport async function safeParseJSON(options: {\n  text: string;\n  schema?: undefined;\n}): Promise<ParseResult<JSONValue>>;\n/**\n * Safely parses a JSON string into a strongly-typed object, using a provided schema to validate the object.\n *\n * @template T - The type of the object to parse the JSON into.\n * @param {string} text - The JSON string to parse.\n * @param {Validator<T>} schema - The schema to use for parsing the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport async function safeParseJSON<T>(options: {\n  text: string;\n  schema: z4.ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<ParseResult<T>>;\nexport async function safeParseJSON<T>({\n  text,\n  schema,\n}: {\n  text: string;\n  schema?: z4.ZodType<T> | z3.Schema<T> | Validator<T>;\n}): Promise<ParseResult<T>> {\n  try {\n    const value = secureJsonParse(text);\n\n    if (schema == null) {\n      return { success: true, value: value as T, rawValue: value };\n    }\n\n    return await safeValidateTypes<T>({ value, schema });\n  } catch (error) {\n    return {\n      success: false,\n      error: JSONParseError.isInstance(error)\n        ? error\n        : new JSONParseError({ text, cause: error }),\n      rawValue: undefined,\n    };\n  }\n}\n\nexport function isParsableJson(input: string): boolean {\n  try {\n    secureJsonParse(input);\n    return true;\n  } catch {\n    return false;\n  }\n}\n", "// Licensed under BSD-3-Clause (this file only)\n// Code adapted from https://github.com/fastify/secure-json-parse/blob/783fcb1b5434709466759847cec974381939673a/index.js\n//\n// Copyright (c) Vercel, Inc. (https://vercel.com)\n// Copyright (c) 2019 The Fastify Team\n// Copyright (c) 2019, Sideway Inc, and project contributors\n// All rights reserved.\n//\n// The complete list of contributors can be found at:\n// - https://github.com/hapijs/bourne/graphs/contributors\n// - https://github.com/fastify/secure-json-parse/graphs/contributors\n// - https://github.com/vercel/ai/commits/main/packages/provider-utils/src/secure-parse-json.ts\n//\n// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n//\n// 1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n//\n// 2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n//\n// 3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nconst suspectProtoRx = /\"__proto__\"\\s*:/;\nconst suspectConstructorRx = /\"constructor\"\\s*:/;\n\nfunction _parse(text: string) {\n  // Parse normally\n  const obj = JSON.parse(text);\n\n  // Ignore null and non-objects\n  if (obj === null || typeof obj !== 'object') {\n    return obj;\n  }\n\n  if (\n    suspectProtoRx.test(text) === false &&\n    suspectConstructorRx.test(text) === false\n  ) {\n    return obj;\n  }\n\n  // Scan result for proto keys\n  return filter(obj);\n}\n\nfunction filter(obj: any) {\n  let next = [obj];\n\n  while (next.length) {\n    const nodes = next;\n    next = [];\n\n    for (const node of nodes) {\n      if (Object.prototype.hasOwnProperty.call(node, '__proto__')) {\n        throw new SyntaxError('Object contains forbidden prototype property');\n      }\n\n      if (\n        Object.prototype.hasOwnProperty.call(node, 'constructor') &&\n        Object.prototype.hasOwnProperty.call(node.constructor, 'prototype')\n      ) {\n        throw new SyntaxError('Object contains forbidden prototype property');\n      }\n\n      for (const key in node) {\n        const value = node[key];\n        if (value && typeof value === 'object') {\n          next.push(value);\n        }\n      }\n    }\n  }\n  return obj;\n}\n\nexport function secureJsonParse(text: string) {\n  // Performance optimization, see https://github.com/fastify/secure-json-parse/pull/90\n  const { stackTraceLimit } = Error;\n  Error.stackTraceLimit = 0;\n  try {\n    return _parse(text);\n  } finally {\n    Error.stackTraceLimit = stackTraceLimit;\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport type { StandardSchemaV1 } from '@standard-schema/spec';\nimport { Validator, asValidator } from './validator';\n\n/**\n * Validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns {Promise<T>} - The typed object.\n */\nexport async function validateTypes<OBJECT>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: StandardSchemaV1<unknown, OBJECT> | Validator<OBJECT>;\n}): Promise<OBJECT> {\n  const result = await safeValidateTypes({ value, schema });\n\n  if (!result.success) {\n    throw TypeValidationError.wrap({ value, cause: result.error });\n  }\n\n  return result.value;\n}\n\n/**\n * Safely validates the types of an unknown object using a schema and\n * return a strongly-typed object.\n *\n * @template T - The type of the object to validate.\n * @param {string} options.value - The JSON object to validate.\n * @param {Validator<T>} options.schema - The schema to use for validating the JSON.\n * @returns An object with either a `success` flag and the parsed and typed data, or a `success` flag and an error object.\n */\nexport async function safeValidateTypes<OBJECT>({\n  value,\n  schema,\n}: {\n  value: unknown;\n  schema: StandardSchemaV1<unknown, OBJECT> | Validator<OBJECT>;\n}): Promise<\n  | {\n      success: true;\n      value: OBJECT;\n      rawValue: unknown;\n    }\n  | {\n      success: false;\n      error: TypeValidationError;\n      rawValue: unknown;\n    }\n> {\n  const validator = asValidator(schema);\n\n  try {\n    if (validator.validate == null) {\n      return { success: true, value: value as OBJECT, rawValue: value };\n    }\n\n    const result = await validator.validate(value);\n\n    if (result.success) {\n      return { success: true, value: result.value, rawValue: value };\n    }\n\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: result.error }),\n      rawValue: value,\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: TypeValidationError.wrap({ value, cause: error }),\n      rawValue: value,\n    };\n  }\n}\n", "import { TypeValidationError } from '@ai-sdk/provider';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\n\n/**\n * Used to mark validator functions so we can support both Zod and custom schemas.\n */\nexport const validatorSymbol = Symbol.for('vercel.ai.validator');\n\nexport type ValidationResult<OBJECT> =\n  | { success: true; value: OBJECT }\n  | { success: false; error: Error };\n\nexport type Validator<OBJECT = unknown> = {\n  /**\n   * Used to mark validator functions so we can support both Zod and custom schemas.\n   */\n  [validatorSymbol]: true;\n\n  /**\n   * Optional. Validates that the structure of a value matches this schema,\n   * and returns a typed version of the value if it does.\n   */\n  readonly validate?: (\n    value: unknown,\n  ) => ValidationResult<OBJECT> | PromiseLike<ValidationResult<OBJECT>>;\n};\n\n/**\n * Create a validator.\n *\n * @param validate A validation function for the schema.\n */\nexport function validator<OBJECT>(\n  validate?:\n    | undefined\n    | ((\n        value: unknown,\n      ) => ValidationResult<OBJECT> | PromiseLike<ValidationResult<OBJECT>>),\n): Validator<OBJECT> {\n  return { [validatorSymbol]: true, validate };\n}\n\nexport function isValidator(value: unknown): value is Validator {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    validatorSymbol in value &&\n    value[validatorSymbol] === true &&\n    'validate' in value\n  );\n}\n\nexport function asValidator<OBJECT>(\n  value: Validator<OBJECT> | StandardSchemaV1<unknown, OBJECT>,\n): Validator<OBJECT> {\n  return isValidator(value) ? value : standardSchemaValidator(value);\n}\n\nexport function standardSchemaValidator<OBJECT>(\n  standardSchema: StandardSchemaV1<unknown, OBJECT>,\n): Validator<OBJECT> {\n  return validator(async value => {\n    const result = await standardSchema['~standard'].validate(value);\n\n    return result.issues == null\n      ? { success: true, value: result.value }\n      : {\n          success: false,\n          error: new TypeValidationError({\n            value,\n            cause: result.issues,\n          }),\n        };\n  });\n}\n", "import {\n  EventSourceMessage,\n  EventSourceParserStream,\n} from 'eventsource-parser/stream';\nimport { ZodType } from 'zod/v4';\nimport { ParseResult, safeParseJSON } from './parse-json';\n\n/**\n * Parses a JSON event stream into a stream of parsed JSON objects.\n */\nexport function parseJsonEventStream<T>({\n  stream,\n  schema,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  schema: ZodType<T>;\n}): ReadableStream<ParseResult<T>> {\n  return stream\n    .pipeThrough(new TextDecoderStream())\n    .pipeThrough(new EventSourceParserStream())\n    .pipeThrough(\n      new TransformStream<EventSourceMessage, ParseResult<T>>({\n        async transform({ data }, controller) {\n          // ignore the 'DONE' event that e.g. OpenAI sends:\n          if (data === '[DONE]') {\n            return;\n          }\n\n          controller.enqueue(await safeParseJSON({ text: data, schema }));\n        },\n      }),\n    );\n}\n", "import { InvalidArgumentError } from '@ai-sdk/provider';\nimport { safeValidateTypes } from './validate-types';\nimport { z } from 'zod/v4';\n\nexport async function parseProviderOptions<T>({\n  provider,\n  providerOptions,\n  schema,\n}: {\n  provider: string;\n  providerOptions: Record<string, unknown> | undefined;\n  schema: z.ZodType<T, T>;\n}): Promise<T | undefined> {\n  if (providerOptions?.[provider] == null) {\n    return undefined;\n  }\n\n  const parsedProviderOptions = await safeValidateTypes<T | undefined>({\n    value: providerOptions[provider],\n    schema,\n  });\n\n  if (!parsedProviderOptions.success) {\n    throw new InvalidArgumentError({\n      argument: 'providerOptions',\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error,\n    });\n  }\n\n  return parsedProviderOptions.value;\n}\n", "import { APICallError } from '@ai-sdk/provider';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { FetchFunction } from './fetch-function';\nimport { handleFetchError } from './handle-fetch-error';\nimport { isAbortError } from './is-abort-error';\nimport { removeUndefinedEntries } from './remove-undefined-entries';\nimport { ResponseHandler } from './response-handler';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => globalThis.fetch;\n\nexport const postJsonToApi = async <T>({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: unknown;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    body: {\n      content: JSON.stringify(body),\n      values: body,\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postFormDataToApi = async <T>({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch,\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  formData: FormData;\n  failedResponseHandler: ResponseHandler<APICallError>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) =>\n  postToApi({\n    url,\n    headers,\n    body: {\n      content: formData,\n      values: Object.fromEntries((formData as any).entries()),\n    },\n    failedResponseHandler,\n    successfulResponseHandler,\n    abortSignal,\n    fetch,\n  });\n\nexport const postToApi = async <T>({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch(),\n}: {\n  url: string;\n  headers?: Record<string, string | undefined>;\n  body: {\n    content: string | FormData | Uint8Array;\n    values: unknown;\n  };\n  failedResponseHandler: ResponseHandler<Error>;\n  successfulResponseHandler: ResponseHandler<T>;\n  abortSignal?: AbortSignal;\n  fetch?: FetchFunction;\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.ok) {\n      let errorInformation: {\n        value: Error;\n        responseHeaders?: Record<string, string> | undefined;\n      };\n\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values,\n        });\n      } catch (error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n\n        throw new APICallError({\n          message: 'Failed to process error response',\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values,\n        });\n      }\n\n      throw errorInformation.value;\n    }\n\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values,\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n\n      throw new APICallError({\n        message: 'Failed to process successful response',\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values,\n      });\n    }\n  } catch (error) {\n    throw handleFetchError({ error, url, requestBodyValues: body.values });\n  }\n};\n", "import { JSONValue, LanguageModelV2ToolResultPart } from '@ai-sdk/provider';\nimport { FlexibleSchema } from '../schema';\nimport { ModelMessage } from './model-message';\nimport { ProviderOptions } from './provider-options';\n\nexport interface ToolCallOptions {\n  /**\n   * The ID of the tool call. You can use it e.g. when sending tool-call related information with stream data.\n   */\n  toolCallId: string;\n\n  /**\n   * Messages that were sent to the language model to initiate the response that contained the tool call.\n   * The messages **do not** include the system prompt nor the assistant response that contained the tool call.\n   */\n  messages: ModelMessage[];\n\n  /**\n   * An optional abort signal that indicates that the overall operation should be aborted.\n   */\n  abortSignal?: AbortSignal;\n}\n\nexport type ToolExecuteFunction<INPUT, OUTPUT> = (\n  input: INPUT,\n  options: ToolCallOptions,\n) => PromiseLike<OUTPUT> | OUTPUT;\n\n// 0 extends 1 & N checks for any\n// [N] extends [never] checks for never\ntype NeverOptional<N, T> = 0 extends 1 & N\n  ? Partial<T>\n  : [N] extends [never]\n    ? Partial<Record<keyof T, undefined>>\n    : T;\n\n/**\nA tool contains the description and the schema of the input that the tool expects.\nThis enables the language model to generate the input.\n\nThe tool can also contain an optional execute function for the actual execution function of the tool.\n */\nexport type Tool<\n  INPUT extends JSONValue | unknown | never = any,\n  OUTPUT extends JSONValue | unknown | never = any,\n> = {\n  /**\nAn optional description of what the tool does.\nWill be used by the language model to decide whether to use the tool.\nNot used for provider-defined tools.\n   */\n  description?: string;\n\n  /**\nAdditional provider-specific metadata. They are passed through\nto the provider from the AI SDK and enable provider-specific\nfunctionality that can be fully encapsulated in the provider.\n   */\n  providerOptions?: ProviderOptions;\n} & NeverOptional<\n  INPUT,\n  {\n    /**\nThe schema of the input that the tool expects. The language model will use this to generate the input.\nIt is also used to validate the output of the language model.\nUse descriptions to make the input understandable for the language model.\n   */\n    inputSchema: FlexibleSchema<INPUT>;\n\n    /**\n     * Optional function that is called when the argument streaming starts.\n     * Only called when the tool is used in a streaming context.\n     */\n    onInputStart?: (options: ToolCallOptions) => void | PromiseLike<void>;\n\n    /**\n     * Optional function that is called when an argument streaming delta is available.\n     * Only called when the tool is used in a streaming context.\n     */\n    onInputDelta?: (\n      options: { inputTextDelta: string } & ToolCallOptions,\n    ) => void | PromiseLike<void>;\n\n    /**\n     * Optional function that is called when a tool call can be started,\n     * even if the execute function is not provided.\n     */\n    onInputAvailable?: (\n      options: {\n        input: [INPUT] extends [never] ? undefined : INPUT;\n      } & ToolCallOptions,\n    ) => void | PromiseLike<void>;\n  }\n> &\n  NeverOptional<\n    OUTPUT,\n    {\n      /**\nOptional conversion function that maps the tool result to an output that can be used by the language model.\n\nIf not provided, the tool result will be sent as a JSON object.\n      */\n      toModelOutput?: (\n        output: OUTPUT,\n      ) => LanguageModelV2ToolResultPart['output'];\n    } & (\n      | {\n          /**\nAn async function that is called with the arguments from the tool call and produces a result.\nIf not provided, the tool will not be executed automatically.\n\n@args is the input of the tool call.\********************* is a signal that can be used to abort the tool call.\n      */\n          execute: ToolExecuteFunction<INPUT, OUTPUT>;\n\n          outputSchema?: FlexibleSchema<OUTPUT>;\n        }\n      | {\n          outputSchema: FlexibleSchema<OUTPUT>;\n\n          execute?: never;\n        }\n    )\n  > &\n  (\n    | {\n        /**\nFunction tool.\n     */\n        type?: undefined | 'function';\n      }\n    | {\n        /**\nProvider-defined tool.\n     */\n        type: 'provider-defined';\n\n        /**\nThe ID of the tool. Should follow the format `<provider-name>.<unique-tool-name>`.\n   */\n        id: `${string}.${string}`;\n\n        /**\nThe name of the tool that the user must use in the tool set.\n */\n        name: string;\n\n        /**\nThe arguments for configuring the tool. Must match the expected arguments defined by the provider for this tool.\n     */\n        args: Record<string, unknown>;\n      }\n  );\n\n/**\n * Infer the input type of a tool.\n */\nexport type InferToolInput<TOOL extends Tool> =\n  TOOL extends Tool<infer INPUT, any> ? INPUT : never;\n\n/**\n * Infer the output type of a tool.\n */\nexport type InferToolOutput<TOOL extends Tool> =\n  TOOL extends Tool<any, infer OUTPUT> ? OUTPUT : never;\n\n/**\nHelper function for inferring the execute args of a tool.\n */\n// Note: overload order is important for auto-completion\nexport function tool<INPUT, OUTPUT>(\n  tool: Tool<INPUT, OUTPUT>,\n): Tool<INPUT, OUTPUT>;\nexport function tool<INPUT>(tool: Tool<INPUT, never>): Tool<INPUT, never>;\nexport function tool<OUTPUT>(tool: Tool<never, OUTPUT>): Tool<never, OUTPUT>;\nexport function tool(tool: Tool<never, never>): Tool<never, never>;\nexport function tool(tool: any): any {\n  return tool;\n}\n", "import { tool, Tool, ToolExecuteFunction } from './types/tool';\nimport { FlexibleSchema } from './schema';\n\nexport type ProviderDefinedToolFactory<INPUT, ARGS extends object> = <OUTPUT>(\n  options: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  },\n) => Tool<INPUT, OUTPUT>;\n\nexport function createProviderDefinedToolFactory<INPUT, ARGS extends object>({\n  id,\n  name,\n  inputSchema,\n}: {\n  id: `${string}.${string}`;\n  name: string;\n  inputSchema: FlexibleSchema<INPUT>;\n}): ProviderDefinedToolFactory<INPUT, ARGS> {\n  return <OUTPUT>({\n    execute,\n    outputSchema,\n    toModelOutput,\n    onInputStart,\n    onInputDelta,\n    onInputAvailable,\n    ...args\n  }: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    outputSchema?: FlexibleSchema<OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  }): Tool<INPUT, OUTPUT> =>\n    tool({\n      type: 'provider-defined',\n      id,\n      name,\n      args,\n      inputSchema,\n      outputSchema,\n      execute,\n      toModelOutput,\n      onInputStart,\n      onInputDelta,\n      onInputAvailable,\n    });\n}\n\nexport type ProviderDefinedToolFactoryWithOutputSchema<\n  INPUT,\n  OUTPUT,\n  ARGS extends object,\n> = (\n  options: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  },\n) => Tool<INPUT, OUTPUT>;\n\nexport function createProviderDefinedToolFactoryWithOutputSchema<\n  INPUT,\n  OUTPUT,\n  ARGS extends object,\n>({\n  id,\n  name,\n  inputSchema,\n  outputSchema,\n}: {\n  id: `${string}.${string}`;\n  name: string;\n  inputSchema: FlexibleSchema<INPUT>;\n  outputSchema: FlexibleSchema<OUTPUT>;\n}): ProviderDefinedToolFactoryWithOutputSchema<INPUT, OUTPUT, ARGS> {\n  return ({\n    execute,\n    toModelOutput,\n    onInputStart,\n    onInputDelta,\n    onInputAvailable,\n    ...args\n  }: ARGS & {\n    execute?: ToolExecuteFunction<INPUT, OUTPUT>;\n    toModelOutput?: Tool<INPUT, OUTPUT>['toModelOutput'];\n    onInputStart?: Tool<INPUT, OUTPUT>['onInputStart'];\n    onInputDelta?: Tool<INPUT, OUTPUT>['onInputDelta'];\n    onInputAvailable?: Tool<INPUT, OUTPUT>['onInputAvailable'];\n  }): Tool<INPUT, OUTPUT> =>\n    tool({\n      type: 'provider-defined',\n      id,\n      name,\n      args,\n      inputSchema,\n      outputSchema,\n      execute,\n      toModelOutput,\n      onInputStart,\n      onInputDelta,\n      onInputAvailable,\n    });\n}\n", "export type Resolvable<T> =\n  | T // Raw value\n  | Promise<T> // Promise of value\n  | (() => T) // Function returning value\n  | (() => Promise<T>); // Function returning promise of value\n\n/**\n * Resolves a value that could be a raw value, a Promise, a function returning a value,\n * or a function returning a Promise.\n */\nexport async function resolve<T>(value: Resolvable<T>): Promise<T> {\n  // If it's a function, call it to get the value/promise\n  if (typeof value === 'function') {\n    value = (value as Function)();\n  }\n\n  // Otherwise just resolve whatever we got (value or promise)\n  return Promise.resolve(value as T);\n}\n", "import { APICallError, EmptyResponseBodyError } from '@ai-sdk/provider';\nimport { ZodType } from 'zod/v4';\nimport { extractResponseHeaders } from './extract-response-headers';\nimport { parseJSON, ParseResult, safeParseJSON } from './parse-json';\nimport { parseJsonEventStream } from './parse-json-event-stream';\n\nexport type ResponseHandler<RETURN_TYPE> = (options: {\n  url: string;\n  requestBodyValues: unknown;\n  response: Response;\n}) => PromiseLike<{\n  value: RETURN_TYPE;\n  rawValue?: unknown;\n  responseHeaders?: Record<string, string>;\n}>;\n\nexport const createJsonErrorResponseHandler =\n  <T>({\n    errorSchema,\n    errorToMessage,\n    isRetryable,\n  }: {\n    errorSchema: ZodType<T>;\n    errorToMessage: (error: T) => string;\n    isRetryable?: (response: Response, error?: T) => boolean;\n  }): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n    const responseHeaders = extractResponseHeaders(response);\n\n    // Some providers return an empty response body for some errors:\n    if (responseBody.trim() === '') {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n\n    // resilient parsing in case the response is not JSON or does not match the schema:\n    try {\n      const parsedError = await parseJSON({\n        text: responseBody,\n        schema: errorSchema,\n      });\n\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: errorToMessage(parsedError),\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          data: parsedError,\n          isRetryable: isRetryable?.(response, parsedError),\n        }),\n      };\n    } catch (parseError) {\n      return {\n        responseHeaders,\n        value: new APICallError({\n          message: response.statusText,\n          url,\n          requestBodyValues,\n          statusCode: response.status,\n          responseHeaders,\n          responseBody,\n          isRetryable: isRetryable?.(response),\n        }),\n      };\n    }\n  };\n\nexport const createEventSourceResponseHandler =\n  <T>(\n    chunkSchema: ZodType<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    return {\n      responseHeaders,\n      value: parseJsonEventStream({\n        stream: response.body,\n        schema: chunkSchema,\n      }),\n    };\n  };\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodType<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    let buffer = '';\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n        new TransformStream<string, ParseResult<T>>({\n          async transform(chunkText, controller) {\n            if (chunkText.endsWith('\\n')) {\n              controller.enqueue(\n                await safeParseJSON({\n                  text: buffer + chunkText,\n                  schema: chunkSchema,\n                }),\n              );\n              buffer = '';\n            } else {\n              buffer += chunkText;\n            }\n          },\n        }),\n      ),\n    };\n  };\n\nexport const createJsonResponseHandler =\n  <T>(responseSchema: ZodType<T>): ResponseHandler<T> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseBody = await response.text();\n\n    const parsedResult = await safeParseJSON({\n      text: responseBody,\n      schema: responseSchema,\n    });\n\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!parsedResult.success) {\n      throw new APICallError({\n        message: 'Invalid JSON response',\n        cause: parsedResult.error,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        url,\n        requestBodyValues,\n      });\n    }\n\n    return {\n      responseHeaders,\n      value: parsedResult.value,\n      rawValue: parsedResult.rawValue,\n    };\n  };\n\nexport const createBinaryResponseHandler =\n  (): ResponseHandler<Uint8Array> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (!response.body) {\n      throw new APICallError({\n        message: 'Response body is empty',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n      });\n    }\n\n    try {\n      const buffer = await response.arrayBuffer();\n      return {\n        responseHeaders,\n        value: new Uint8Array(buffer),\n      };\n    } catch (error) {\n      throw new APICallError({\n        message: 'Failed to read response as array buffer',\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody: undefined,\n        cause: error,\n      });\n    }\n  };\n\nexport const createStatusCodeErrorResponseHandler =\n  (): ResponseHandler<APICallError> =>\n  async ({ response, url, requestBodyValues }) => {\n    const responseHeaders = extractResponseHeaders(response);\n    const responseBody = await response.text();\n\n    return {\n      responseHeaders,\n      value: new APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues: requestBodyValues as Record<string, unknown>,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n      }),\n    };\n  };\n", "import { JSONSchema7 } from '@ai-sdk/provider';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { jsonSchema, Schema } from './schema';\n\nexport function zod3Schema<OBJECT>(\n  zodSchema: z3.Schema<OBJECT, z3.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  return jsonSchema(\n    zodToJsonSchema(zodSchema, {\n      $refStrategy: useReferences ? 'root' : 'none',\n      target: 'jsonSchema7', // note: openai mode breaks various gemini conversions\n    }) as JSONSchema7,\n    {\n      validate: value => {\n        const result = zodSchema.safeParse(value);\n        return result.success\n          ? { success: true, value: result.data }\n          : { success: false, error: result.error };\n      },\n    },\n  );\n}\n\nexport function zod4Schema<OBJECT>(\n  zodSchema: z4.ZodType<OBJECT, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  const z4JSONSchema = z4.toJSONSchema(zodSchema, {\n    target: 'draft-7',\n    io: 'output',\n    reused: useReferences ? 'ref' : 'inline',\n  }) as JSONSchema7;\n\n  return jsonSchema(z4JSONSchema, {\n    validate: value => {\n      const result = z4.safeParse(zodSchema, value);\n      return result.success\n        ? { success: true, value: result.data }\n        : { success: false, error: result.error };\n    },\n  });\n}\n\nexport function isZod4Schema(\n  zodSchema: z4.ZodType<any, any> | z3.Schema<any, z3.ZodTypeDef, any>,\n): zodSchema is z4.ZodType<any, any> {\n  // https://zod.dev/library-authors?id=how-to-support-zod-3-and-zod-4-simultaneously\n  return '_zod' in zodSchema;\n}\n\nexport function zodSchema<OBJECT>(\n  zodSchema: z4.ZodType<OBJECT, any> | z3.Schema<OBJECT, z3.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  if (isZod4Schema(zodSchema)) {\n    return zod4Schema(zodSchema, options);\n  } else {\n    return zod3Schema(zodSchema, options);\n  }\n}\n", "import { Validator, validatorSymbol } from './validator';\nimport { JSONSchema7 } from '@ai-sdk/provider';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4';\nimport { zodSchema } from './zod-schema';\n\n/**\n * Used to mark schemas so we can support both Zod and custom schemas.\n */\nconst schemaSymbol = Symbol.for('vercel.ai.schema');\n\nexport type Schema<OBJECT = unknown> = Validator<OBJECT> & {\n  /**\n   * Used to mark schemas so we can support both Zod and custom schemas.\n   */\n  [schemaSymbol]: true;\n\n  /**\n   * Schema type for inference.\n   */\n  _type: OBJECT;\n\n  /**\n   * The JSON Schema for the schema. It is passed to the providers.\n   */\n  readonly jsonSchema: JSONSchema7;\n};\n\nexport type FlexibleSchema<T> = z4.ZodType<T> | z3.Schema<T> | Schema<T>;\n\nexport type InferSchema<SCHEMA> = SCHEMA extends z3.Schema\n  ? z3.infer<SCHEMA>\n  : SCHEMA extends z4.ZodType\n    ? z4.infer<SCHEMA>\n    : SCHEMA extends Schema<infer T>\n      ? T\n      : never;\n\n/**\n * Create a schema using a JSON Schema.\n *\n * @param jsonSchema The JSON Schema for the schema.\n * @param options.validate Optional. A validation function for the schema.\n */\nexport function jsonSchema<OBJECT = unknown>(\n  jsonSchema: JSONSchema7,\n  {\n    validate,\n  }: {\n    validate?: (\n      value: unknown,\n    ) => { success: true; value: OBJECT } | { success: false; error: Error };\n  } = {},\n): Schema<OBJECT> {\n  return {\n    [schemaSymbol]: true,\n    _type: undefined as OBJECT, // should never be used directly\n    [validatorSymbol]: true,\n    jsonSchema,\n    validate,\n  };\n}\n\nfunction isSchema(value: unknown): value is Schema {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    schemaSymbol in value &&\n    value[schemaSymbol] === true &&\n    'jsonSchema' in value &&\n    'validate' in value\n  );\n}\n\nexport function asSchema<OBJECT>(\n  schema:\n    | z4.ZodType<OBJECT, any>\n    | z3.Schema<OBJECT, z3.ZodTypeDef, any>\n    | Schema<OBJECT>\n    | undefined,\n): Schema<OBJECT> {\n  return schema == null\n    ? jsonSchema({\n        properties: {},\n        additionalProperties: false,\n      })\n    : isSchema(schema)\n      ? schema\n      : zodSchema(schema);\n}\n", "// btoa and atob need to be invoked as a function call, not as a method call.\n// Otherwise <PERSON><PERSON><PERSON><PERSON> will throw a\n// \"TypeError: Illegal invocation: function called with incorrect this reference\"\nconst { btoa, atob } = globalThis;\n\nexport function convertBase64ToUint8Array(base64String: string) {\n  const base64Url = base64String.replace(/-/g, '+').replace(/_/g, '/');\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, byte => byte.codePointAt(0)!);\n}\n\nexport function convertUint8ArrayToBase64(array: Uint8Array): string {\n  let latin1string = '';\n\n  // Note: regular for loop to support older JavaScript versions that\n  // do not support for..of on Uint8Array\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n\n  return btoa(latin1string);\n}\n\nexport function convertToBase64(value: string | Uint8Array): string {\n  return value instanceof Uint8Array ? convertUint8ArrayToBase64(value) : value;\n}\n", "export function withoutTrailingSlash(url: string | undefined) {\n  return url?.replace(/\\/$/, '');\n}\n", "export * from './combine-headers';\nexport { convertAsyncIteratorToReadableStream } from './convert-async-iterator-to-readable-stream';\nexport * from './delay';\nexport * from './extract-response-headers';\nexport * from './fetch-function';\nexport { createIdGenerator, generateId, type IdGenerator } from './generate-id';\nexport * from './get-error-message';\nexport * from './get-from-api';\nexport * from './is-abort-error';\nexport { isUrlSupported } from './is-url-supported';\nexport * from './load-api-key';\nexport { loadOptionalSetting } from './load-optional-setting';\nexport { loadSetting } from './load-setting';\nexport * from './parse-json';\nexport { parseJsonEventStream } from './parse-json-event-stream';\nexport { parseProviderOptions } from './parse-provider-options';\nexport * from './post-to-api';\nexport {\n  createProviderDefinedToolFactory,\n  type ProviderDefinedToolFactory,\n  createProviderDefinedToolFactoryWithOutputSchema,\n  type ProviderDefinedToolFactoryWithOutputSchema,\n} from './provider-defined-tool-factory';\nexport * from './remove-undefined-entries';\nexport * from './resolve';\nexport * from './response-handler';\nexport {\n  asSchema,\n  jsonSchema,\n  type FlexibleSchema,\n  type InferSchema,\n  type Schema,\n} from './schema';\nexport * from './uint8-utils';\nexport * from './validate-types';\nexport * from './validator';\nexport * from './without-trailing-slash';\nexport { zodSchema } from './zod-schema';\n\n// folder re-exports\nexport * from './types';\n\n// external re-exports\nexport * from '@standard-schema/spec';\nexport {\n  EventSourceParserStream,\n  type EventSourceMessage,\n} from 'eventsource-parser/stream';\n"], "names": ["resolve", "APICallError", "APICallError", "mediaType", "TypeValidationError", "TypeValidationError", "TypeValidationError", "validator", "TypeValidationError", "InvalidArgumentError", "InvalidArgumentError", "APICallError", "getOriginalFetch", "APICallError", "tool", "APICallError", "APICallError", "zodSchema", "jsonSchema", "EventSourceParserStream"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AIAA,SAAS,4BAA4B;;AcArC;;AOEA,YAAY,QAAQ;;AACpB,OAAO,qBAAqB;;AzBHrB,SAAS,eAAA,GACX,OAAA,EACiC;IACpC,OAAO,QAAQ,MAAA,CACb,CAAC,iBAAiB,iBAAA,CAAoB;YACpC,GAAG,eAAA;YACH,GAAI,kBAAA,OAAA,iBAAkB,CAAC,CAAA;QACzB,CAAA,GACA,CAAC;AAEL;;ACHO,SAAS,qCACd,QAAA,EACmB;IACnB,OAAO,IAAI,eAAkB;QAAA;;;;;KAAA,GAO3B,MAAM,MAAK,UAAA,EAAY;YACrB,IAAI;gBACF,MAAM,EAAE,KAAA,EAAO,IAAA,CAAK,CAAA,GAAI,MAAM,SAAS,IAAA,CAAK;gBAC5C,IAAI,MAAM;oBACR,WAAW,KAAA,CAAM;gBACnB,OAAO;oBACL,WAAW,OAAA,CAAQ,KAAK;gBAC1B;YACF,EAAA,OAAS,OAAO;gBACd,WAAW,KAAA,CAAM,KAAK;YACxB;QACF;QAAA;;KAAA,GAIA,SAAS,EAAC;IACZ,CAAC;AACH;;AC7BA,eAAsB,MAAM,SAAA,EAA0C;IACpE,OAAO,aAAa,OAChB,QAAQ,OAAA,CAAQ,IAChB,IAAI,QAAQ,CAAAA,WAAW,WAAWA,UAAS,SAAS,CAAC;AAC3D;;ACHO,SAAS,uBAAuB,QAAA,EAAoB;IACzD,OAAO,OAAO,WAAA,CAAoB,CAAC;WAAG,SAAS,OAAO;KAAC;AACzD;;ACIO,IAAM,oBAAoB,CAAC,EAChC,MAAA,EACA,OAAO,EAAA,EACP,WAAW,gEAAA,EACX,YAAY,GAAA,EACd,GAKI,CAAC,CAAA,KAAmB;IACtB,MAAM,YAAY,MAAM;QACtB,MAAM,iBAAiB,SAAS,MAAA;QAChC,MAAM,QAAQ,IAAI,MAAM,IAAI;QAC5B,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,IAAK;YAC7B,KAAA,CAAM,CAAC,CAAA,GAAI,QAAA,CAAU,KAAK,MAAA,CAAO,IAAI,iBAAkB,CAAC,CAAA;QAC1D;QACA,OAAO,MAAM,IAAA,CAAK,EAAE;IACtB;IAEA,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IAGA,IAAI,SAAS,QAAA,CAAS,SAAS,GAAG;QAChC,MAAM,sKAAI,uBAAA,CAAqB;YAC7B,UAAU;YACV,SAAS,CAAA,eAAA,EAAkB,SAAS,CAAA,oCAAA,EAAuC,QAAQ,CAAA,EAAA,CAAA;QACrF,CAAC;IACH;IAEA,OAAO,IAAM,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC,EAAA;AAClD;AAWO,IAAM,aAAa,kBAAkB;;ACxDrC,SAAS,gBAAgB,KAAA,EAA4B;IAC1D,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAA;IACf;IAEA,OAAO,KAAK,SAAA,CAAU,KAAK;AAC7B;;;;AGdO,SAAS,aAAa,KAAA,EAAgC;IAC3D,OACE,iBAAiB,SAAA,CAChB,MAAM,IAAA,KAAS,gBAAgB,MAAM,IAAA,KAAS,cAAA;AAEnD;;ADFA,IAAM,8BAA8B;IAAC;IAAgB,iBAAiB;CAAA;AAE/D,SAAS,iBAAiB,EAC/B,KAAA,EACA,GAAA,EACA,iBAAA,EACF,EAIG;IACD,IAAI,aAAa,KAAK,GAAG;QACvB,OAAO;IACT;IAGA,IACE,iBAAiB,aACjB,4BAA4B,QAAA,CAAS,MAAM,OAAA,CAAQ,WAAA,CAAY,CAAC,GAChE;QACA,MAAM,QAAS,MAAc,KAAA;QAE7B,IAAI,SAAS,MAAM;YAEjB,OAAO,sKAAI,eAAA,CAAa;gBACtB,SAAS,CAAA,uBAAA,EAA0B,MAAM,OAAO,EAAA;gBAChD;gBACA;gBACA;gBACA,aAAa;YACf,CAAC;QACH;IACF;IAEA,OAAO;AACT;;AEjCO,SAAS,uBACd,MAAA,EACmB;IACnB,OAAO,OAAO,WAAA,CACZ,OAAO,OAAA,CAAQ,MAAM,EAAE,MAAA,CAAO,CAAC,CAAC,MAAM,KAAK,CAAA,GAAM,SAAS,IAAI;AAElE;;AHFA,IAAM,mBAAmB,IAAM,WAAW,KAAA;AAEnC,IAAM,aAAa,OAAU,EAClC,GAAA,EACA,UAAU,CAAC,CAAA,EACX,yBAAA,EACA,qBAAA,EACA,WAAA,EACA,QAAQ,iBAAiB,CAAA,EAC3B,KAOM;IACJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS,uBAAuB,OAAO;YACvC,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,IAAI;YAKJ,IAAI;gBACF,mBAAmB,MAAM,sBAAsB;oBAC7C;oBACA;oBACA,mBAAmB,CAAC;gBACtB,CAAC;YACH,EAAA,OAAS,OAAO;gBACd,IAAI,aAAa,KAAK,uKAAKE,eAAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;gBAEA,MAAM,sKAAIA,eAAAA,CAAa;oBACrB,SAAS;oBACT,OAAO;oBACP,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,mBAAmB,CAAC;gBACtB,CAAC;YACH;YAEA,MAAM,iBAAiB,KAAA;QACzB;QAEA,IAAI;YACF,OAAO,MAAM,0BAA0B;gBACrC;gBACA;gBACA,mBAAmB,CAAC;YACtB,CAAC;QACH,EAAA,OAAS,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,aAAa,KAAK,KAAKA,iLAAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;YACF;YAEA,MAAM,IAAIA,iLAAAA,CAAa;gBACrB,SAAS;gBACT,OAAO;gBACP,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA,mBAAmB,CAAC;YACtB,CAAC;QACH;IACF,EAAA,OAAS,OAAO;QACd,MAAM,iBAAiB;YAAE;YAAO;YAAK,mBAAmB,CAAC;QAAE,CAAC;IAC9D;AACF;;AI/EO,SAAS,eAAe,EAC7B,SAAA,EACA,GAAA,EACA,aAAA,EACF,EAIY;IAEV,MAAM,IAAI,WAAA,CAAY;IACtB,YAAY,UAAU,WAAA,CAAY;IAElC,OACE,OAAO,OAAA,CAAQ,aAAa,EAEzB,GAAA,CAAI,CAAC,CAAC,KAAK,KAAK,CAAA,KAAM;QACrB,MAAMC,aAAY,IAAI,WAAA,CAAY;QAClC,OAAOA,eAAc,OAAOA,eAAc,QACtC;YAAE,iBAAiB;YAAI,SAAS;QAAM,IACtC;YAAE,iBAAiBA,WAAU,OAAA,CAAQ,MAAM,EAAE;YAAG,SAAS;QAAM;IACrE,CAAC,EAEA,MAAA,CAAO,CAAC,EAAE,eAAA,CAAgB,CAAA,GAAM,UAAU,UAAA,CAAW,eAAe,CAAC,EACrE,OAAA,CAAQ,CAAC,EAAE,OAAA,CAAQ,CAAA,GAAM,OAAO,EAEhC,IAAA,CAAK,CAAA,UAAW,QAAQ,IAAA,CAAK,GAAG,CAAC;AAExC;;ACrCO,SAAS,WAAW,EACzB,MAAA,EACA,uBAAA,EACA,sBAAsB,QAAA,EACtB,WAAA,EACF,EAKW;IACT,IAAI,OAAO,WAAW,UAAU;QAC9B,OAAO;IACT;IAEA,IAAI,UAAU,MAAM;QAClB,MAAM,sKAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,0BAAA,CAAA;QACzB,CAAC;IACH;IAEA,IAAI,OAAO,YAAY,aAAa;QAClC,MAAM,sKAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,wCAAA,EAA2C,mBAAmB,CAAA,wEAAA,CAAA;QACvF,CAAC;IACH;IAEA,SAAS,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAE5C,IAAI,UAAU,MAAM;QAClB,MAAM,sKAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,wCAAA,EAA2C,mBAAmB,CAAA,mBAAA,EAAsB,uBAAuB,CAAA,sBAAA,CAAA;QACpI,CAAC;IACH;IAEA,IAAI,OAAO,WAAW,UAAU;QAC9B,MAAM,sKAAI,kBAAA,CAAgB;YACxB,SAAS,GAAG,WAAW,CAAA,4CAAA,EAA+C,uBAAuB,CAAA,sCAAA,CAAA;QAC/F,CAAC;IACH;IAEA,OAAO;AACT;;ACrCO,SAAS,oBAAoB,EAClC,YAAA,EACA,uBAAA,EACF,EAGuB;IACrB,IAAI,OAAO,iBAAiB,UAAU;QACpC,OAAO;IACT;IAEA,IAAI,gBAAgB,QAAQ,OAAO,YAAY,aAAa;QAC1D,OAAO,KAAA;IACT;IAEA,eAAe,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAElD,IAAI,gBAAgB,QAAQ,OAAO,iBAAiB,UAAU;QAC5D,OAAO,KAAA;IACT;IAEA,OAAO;AACT;;AClBO,SAAS,YAAY,EAC1B,YAAA,EACA,uBAAA,EACA,WAAA,EACA,WAAA,EACF,EAKW;IACT,IAAI,OAAO,iBAAiB,UAAU;QACpC,OAAO;IACT;IAEA,IAAI,gBAAgB,MAAM;QACxB,MAAM,sKAAI,mBAAA,CAAiB;YACzB,SAAS,GAAG,WAAW,CAAA,0BAAA,CAAA;QACzB,CAAC;IACH;IAEA,IAAI,OAAO,YAAY,aAAa;QAClC,MAAM,sKAAI,mBAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,wCAAA,EACQ,WAAW,CAAA,wEAAA,CAAA;QAErC,CAAC;IACH;IAEA,eAAe,QAAQ,GAAA,CAAI,uBAAuB,CAAA;IAElD,IAAI,gBAAgB,MAAM;QACxB,MAAM,sKAAI,mBAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,wCAAA,EACQ,WAAW,CAAA,mBAAA,EACvB,uBAAuB,CAAA,sBAAA,CAAA;QACrC,CAAC;IACH;IAEA,IAAI,OAAO,iBAAiB,UAAU;QACpC,MAAM,qKAAI,oBAAA,CAAiB;YACzB,SACE,GAAG,WAAW,CAAA,4CAAA,EACM,uBAAuB,CAAA,sCAAA,CAAA;QAC/C,CAAC;IACH;IAEA,OAAO;AACT;;;AEtCA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAE7B,SAAS,OAAO,IAAA,EAAc;IAE5B,MAAM,MAAM,KAAK,KAAA,CAAM,IAAI;IAG3B,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;QAC3C,OAAO;IACT;IAEA,IACE,eAAe,IAAA,CAAK,IAAI,MAAM,SAC9B,qBAAqB,IAAA,CAAK,IAAI,MAAM,OACpC;QACA,OAAO;IACT;IAGA,OAAO,OAAO,GAAG;AACnB;AAEA,SAAS,OAAO,GAAA,EAAU;IACxB,IAAI,OAAO;QAAC,GAAG;KAAA;IAEf,MAAO,KAAK,MAAA,CAAQ;QAClB,MAAM,QAAQ;QACd,OAAO,CAAC,CAAA;QAER,KAAA,MAAW,QAAQ,MAAO;YACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,MAAM,WAAW,GAAG;gBAC3D,MAAM,IAAI,YAAY,8CAA8C;YACtE;YAEA,IACE,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,MAAM,aAAa,KACxD,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,KAAK,WAAA,EAAa,WAAW,GAClE;gBACA,MAAM,IAAI,YAAY,8CAA8C;YACtE;YAEA,IAAA,MAAW,OAAO,KAAM;gBACtB,MAAM,QAAQ,IAAA,CAAK,GAAG,CAAA;gBACtB,IAAI,SAAS,OAAO,UAAU,UAAU;oBACtC,KAAK,IAAA,CAAK,KAAK;gBACjB;YACF;QACF;IACF;IACA,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAA,EAAc;IAE5C,MAAM,EAAE,eAAA,CAAgB,CAAA,GAAI;IAC5B,MAAM,eAAA,GAAkB;IACxB,IAAI;QACF,OAAO,OAAO,IAAI;IACpB,SAAE;QACA,MAAM,eAAA,GAAkB;IAC1B;AACF;;;AE/EO,IAAM,kBAAkB,OAAO,GAAA,CAAI,qBAAqB;AA0BxD,SAAS,UACd,QAAA,EAKmB;IACnB,OAAO;QAAE,CAAC,eAAe,CAAA,EAAG;QAAM;IAAS;AAC7C;AAEO,SAAS,YAAY,KAAA,EAAoC;IAC9D,OACE,OAAO,UAAU,YACjB,UAAU,QACV,mBAAmB,SACnB,KAAA,CAAM,eAAe,CAAA,KAAM,QAC3B,cAAc;AAElB;AAEO,SAAS,YACd,KAAA,EACmB;IACnB,OAAO,YAAY,KAAK,IAAI,QAAQ,wBAAwB,KAAK;AACnE;AAEO,SAAS,wBACd,cAAA,EACmB;IACnB,OAAO,UAAU,OAAM,UAAS;QAC9B,MAAM,SAAS,MAAM,cAAA,CAAe,WAAW,CAAA,CAAE,QAAA,CAAS,KAAK;QAE/D,OAAO,OAAO,MAAA,IAAU,OACpB;YAAE,SAAS;YAAM,OAAO,OAAO,KAAA;QAAM,IACrC;YACE,SAAS;YACT,OAAO,sKAAI,sBAAA,CAAoB;gBAC7B;gBACA,OAAO,OAAO,MAAA;YAChB,CAAC;QACH;IACN,CAAC;AACH;;AD7DA,eAAsB,cAAsB,EAC1C,KAAA,EACA,MAAA,EACF,EAGoB;IAClB,MAAM,SAAS,MAAM,kBAAkB;QAAE;QAAO;IAAO,CAAC;IAExD,IAAI,CAAC,OAAO,OAAA,EAAS;QACnB,MAAMG,wLAAAA,CAAoB,IAAA,CAAK;YAAE;YAAO,OAAO,OAAO,KAAA;QAAM,CAAC;IAC/D;IAEA,OAAO,OAAO,KAAA;AAChB;AAWA,eAAsB,kBAA0B,EAC9C,KAAA,EACA,MAAA,EACF,EAcE;IACA,MAAMC,aAAY,YAAY,MAAM;IAEpC,IAAI;QACF,IAAIA,WAAU,QAAA,IAAY,MAAM;YAC9B,OAAO;gBAAE,SAAS;gBAAM;gBAAwB,UAAU;YAAM;QAClE;QAEA,MAAM,SAAS,MAAMA,WAAU,QAAA,CAAS,KAAK;QAE7C,IAAI,OAAO,OAAA,EAAS;YAClB,OAAO;gBAAE,SAAS;gBAAM,OAAO,OAAO,KAAA;gBAAO,UAAU;YAAM;QAC/D;QAEA,OAAO;YACL,SAAS;YACT,wKAAOD,uBAAAA,CAAoB,IAAA,CAAK;gBAAE;gBAAO,OAAO,OAAO,KAAA;YAAM,CAAC;YAC9D,UAAU;QACZ;IACF,EAAA,OAAS,OAAO;QACd,OAAO;YACL,SAAS;YACT,yKAAOA,sBAAAA,CAAoB,IAAA,CAAK;gBAAE;gBAAO,OAAO;YAAM,CAAC;YACvD,UAAU;QACZ;IACF;AACF;;AF/CA,eAAsB,UAAa,EACjC,IAAA,EACA,MAAA,EACF,EAGe;IACb,IAAI;QACF,MAAM,QAAQ,gBAAgB,IAAI;QAElC,IAAI,UAAU,MAAM;YAClB,OAAO;QACT;QAEA,OAAO,cAAiB;YAAE;YAAO;QAAO,CAAC;IAC3C,EAAA,OAAS,OAAO;QACd,sKACE,iBAAA,CAAe,UAAA,CAAW,KAAK,uKAC/BE,sBAAAA,CAAoB,UAAA,CAAW,KAAK,GACpC;YACA,MAAM;QACR;QAEA,MAAM,sKAAI,iBAAA,CAAe;YAAE;YAAM,OAAO;QAAM,CAAC;IACjD;AACF;AAgCA,eAAsB,cAAiB,EACrC,IAAA,EACA,MAAA,EACF,EAG4B;IAC1B,IAAI;QACF,MAAM,QAAQ,gBAAgB,IAAI;QAElC,IAAI,UAAU,MAAM;YAClB,OAAO;gBAAE,SAAS;gBAAM;gBAAmB,UAAU;YAAM;QAC7D;QAEA,OAAO,MAAM,kBAAqB;YAAE;YAAO;QAAO,CAAC;IACrD,EAAA,OAAS,OAAO;QACd,OAAO;YACL,SAAS;YACT,yKAAO,iBAAA,CAAe,UAAA,CAAW,KAAK,IAClC,QACA,sKAAI,iBAAA,CAAe;gBAAE;gBAAM,OAAO;YAAM,CAAC;YAC7C,UAAU,KAAA;QACZ;IACF;AACF;AAEO,SAAS,eAAe,KAAA,EAAwB;IACrD,IAAI;QACF,gBAAgB,KAAK;QACrB,OAAO;IACT,EAAA,OAAQ,GAAA;QACN,OAAO;IACT;AACF;;AIlHO,SAAS,qBAAwB,EACtC,MAAA,EACA,MAAA,EACF,EAGmC;IACjC,OAAO,OACJ,WAAA,CAAY,IAAI,kBAAkB,CAAC,EACnC,WAAA,CAAY,oLAAI,0BAAA,CAAwB,CAAC,EACzC,WAAA,CACC,IAAI,gBAAoD;QACtD,MAAM,WAAU,EAAE,IAAA,CAAK,CAAA,EAAG,UAAA,EAAY;YAEpC,IAAI,SAAS,UAAU;gBACrB;YACF;YAEA,WAAW,OAAA,CAAQ,MAAM,cAAc;gBAAE,MAAM;gBAAM;YAAO,CAAC,CAAC;QAChE;IACF,CAAC;AAEP;;AC5BA,eAAsB,qBAAwB,EAC5C,QAAA,EACA,eAAA,EACA,MAAA,EACF,EAI2B;IACzB,IAAA,CAAI,mBAAA,OAAA,KAAA,IAAA,eAAA,CAAkB,SAAA,KAAa,MAAM;QACvC,OAAO,KAAA;IACT;IAEA,MAAM,wBAAwB,MAAM,kBAAiC;QACnE,OAAO,eAAA,CAAgB,QAAQ,CAAA;QAC/B;IACF,CAAC;IAED,IAAI,CAAC,sBAAsB,OAAA,EAAS;QAClC,MAAM,sKAAIE,uBAAAA,CAAqB;YAC7B,UAAU;YACV,SAAS,CAAA,QAAA,EAAW,QAAQ,CAAA,iBAAA,CAAA;YAC5B,OAAO,sBAAsB,KAAA;QAC/B,CAAC;IACH;IAEA,OAAO,sBAAsB,KAAA;AAC/B;;ACtBA,IAAME,oBAAmB,IAAM,WAAW,KAAA;AAEnC,IAAM,gBAAgB,OAAU,EACrC,GAAA,EACA,OAAA,EACA,IAAA,EACA,qBAAA,EACA,yBAAA,EACA,WAAA,EACA,KAAA,EACF,GASE,UAAU;QACR;QACA,SAAS;YACP,gBAAgB;YAChB,GAAG,OAAA;QACL;QACA,MAAM;YACJ,SAAS,KAAK,SAAA,CAAU,IAAI;YAC5B,QAAQ;QACV;QACA;QACA;QACA;QACA;IACF,CAAC;AAEI,IAAM,oBAAoB,OAAU,EACzC,GAAA,EACA,OAAA,EACA,QAAA,EACA,qBAAA,EACA,yBAAA,EACA,WAAA,EACA,KAAA,EACF,GASE,UAAU;QACR;QACA;QACA,MAAM;YACJ,SAAS;YACT,QAAQ,OAAO,WAAA,CAAa,SAAiB,OAAA,CAAQ,CAAC;QACxD;QACA;QACA;QACA;QACA;IACF,CAAC;AAEI,IAAM,YAAY,OAAU,EACjC,GAAA,EACA,UAAU,CAAC,CAAA,EACX,IAAA,EACA,yBAAA,EACA,qBAAA,EACA,WAAA,EACA,QAAQA,kBAAiB,CAAA,EAC3B,KAWM;IACJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS,uBAAuB,OAAO;YACvC,MAAM,KAAK,OAAA;YACX,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,EAAA,EAAI;YAChB,IAAI;YAKJ,IAAI;gBACF,mBAAmB,MAAM,sBAAsB;oBAC7C;oBACA;oBACA,mBAAmB,KAAK,MAAA;gBAC1B,CAAC;YACH,EAAA,OAAS,OAAO;gBACd,IAAI,aAAa,KAAK,uKAAKC,eAAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;gBAEA,MAAM,sKAAIA,eAAAA,CAAa;oBACrB,SAAS;oBACT,OAAO;oBACP,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,mBAAmB,KAAK,MAAA;gBAC1B,CAAC;YACH;YAEA,MAAM,iBAAiB,KAAA;QACzB;QAEA,IAAI;YACF,OAAO,MAAM,0BAA0B;gBACrC;gBACA;gBACA,mBAAmB,KAAK,MAAA;YAC1B,CAAC;QACH,EAAA,OAAS,OAAO;YACd,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,aAAa,KAAK,uKAAKA,eAAAA,CAAa,UAAA,CAAW,KAAK,GAAG;oBACzD,MAAM;gBACR;YACF;YAEA,MAAM,sKAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT,OAAO;gBACP,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA,mBAAmB,KAAK,MAAA;YAC1B,CAAC;QACH;IACF,EAAA,OAAS,OAAO;QACd,MAAM,iBAAiB;YAAE;YAAO;YAAK,mBAAmB,KAAK,MAAA;QAAO,CAAC;IACvE;AACF;;ACkBO,SAAS,KAAKC,KAAAA,EAAgB;IACnC,OAAOA;AACT;;ACtKO,SAAS,iCAA6D,EAC3E,EAAA,EACA,IAAA,EACA,WAAA,EACF,EAI4C;IAC1C,OAAO,CAAS,EACd,OAAA,EACA,YAAA,EACA,aAAA,EACA,YAAA,EACA,YAAA,EACA,gBAAA,EACA,GAAG,MACL,GAQE,KAAK;YACH,MAAM;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC;AACL;AAgBO,SAAS,iDAId,EACA,EAAA,EACA,IAAA,EACA,WAAA,EACA,YAAA,EACF,EAKoE;IAClE,OAAO,CAAC,EACN,OAAA,EACA,aAAA,EACA,YAAA,EACA,YAAA,EACA,gBAAA,EACA,GAAG,MACL,GAOE,KAAK;YACH,MAAM;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC;AACL;;ACnGA,eAAsB,QAAW,KAAA,EAAkC;IAEjE,IAAI,OAAO,UAAU,YAAY;QAC/B,QAAS,MAAmB;IAC9B;IAGA,OAAO,QAAQ,OAAA,CAAQ,KAAU;AACnC;;ACFO,IAAM,iCACX,CAAI,EACF,WAAA,EACA,cAAA,EACA,WAAA,EACF,GAKA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QACzC,MAAM,kBAAkB,uBAAuB,QAAQ;QAGvD,IAAI,aAAa,IAAA,CAAK,MAAM,IAAI;YAC9B,OAAO;gBACL;gBACA,OAAO,sKAAIE,eAAAA,CAAa;oBACtB,SAAS,SAAS,UAAA;oBAClB;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc;gBAC7B,CAAC;YACH;QACF;QAGA,IAAI;YACF,MAAM,cAAc,MAAM,UAAU;gBAClC,MAAM;gBACN,QAAQ;YACV,CAAC;YAED,OAAO;gBACL;gBACA,OAAO,qKAAIA,gBAAAA,CAAa;oBACtB,SAAS,eAAe,WAAW;oBACnC;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,MAAM;oBACN,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc,UAAU;gBACvC,CAAC;YACH;QACF,EAAA,OAAS,YAAY;YACnB,OAAO;gBACL;gBACA,OAAO,sKAAIA,eAAAA,CAAa;oBACtB,SAAS,SAAS,UAAA;oBAClB;oBACA;oBACA,YAAY,SAAS,MAAA;oBACrB;oBACA;oBACA,aAAa,eAAA,OAAA,KAAA,IAAA,YAAc;gBAC7B,CAAC;YACH;QACF;IACF;AAEK,IAAM,mCACX,CACE,cAEF,OAAO,EAAE,QAAA,CAAS,CAAA,KAA8B;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,SAAS,IAAA,IAAQ,MAAM;YACzB,MAAM,sKAAI,yBAAA,CAAuB,CAAC,CAAC;QACrC;QAEA,OAAO;YACL;YACA,OAAO,qBAAqB;gBAC1B,QAAQ,SAAS,IAAA;gBACjB,QAAQ;YACV,CAAC;QACH;IACF;AAEK,IAAM,kCACX,CACE,cAEF,OAAO,EAAE,QAAA,CAAS,CAAA,KAA8B;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,SAAS,IAAA,IAAQ,MAAM;YACzB,MAAM,sKAAI,yBAAA,CAAuB,CAAC,CAAC;QACrC;QAEA,IAAI,SAAS;QAEb,OAAO;YACL;YACA,OAAO,SAAS,IAAA,CAAK,WAAA,CAAY,IAAI,kBAAkB,CAAC,EAAE,WAAA,CACxD,IAAI,gBAAwC;gBAC1C,MAAM,WAAU,SAAA,EAAW,UAAA,EAAY;oBACrC,IAAI,UAAU,QAAA,CAAS,IAAI,GAAG;wBAC5B,WAAW,OAAA,CACT,MAAM,cAAc;4BAClB,MAAM,SAAS;4BACf,QAAQ;wBACV,CAAC;wBAEH,SAAS;oBACX,OAAO;wBACL,UAAU;oBACZ;gBACF;YACF,CAAC;QAEL;IACF;AAEK,IAAM,4BACX,CAAI,iBACJ,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QAEzC,MAAM,eAAe,MAAM,cAAc;YACvC,MAAM;YACN,QAAQ;QACV,CAAC;QAED,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,aAAa,OAAA,EAAS;YACzB,MAAM,sKAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT,OAAO,aAAa,KAAA;gBACpB,YAAY,SAAS,MAAA;gBACrB;gBACA;gBACA;gBACA;YACF,CAAC;QACH;QAEA,OAAO;YACL;YACA,OAAO,aAAa,KAAA;YACpB,UAAU,aAAa,QAAA;QACzB;IACF;AAEK,IAAM,8BACX,IACA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QAEvD,IAAI,CAAC,SAAS,IAAA,EAAM;YAClB,MAAM,sKAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA,cAAc,KAAA;YAChB,CAAC;QACH;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,SAAS,WAAA,CAAY;YAC1C,OAAO;gBACL;gBACA,OAAO,IAAI,WAAW,MAAM;YAC9B;QACF,EAAA,OAAS,OAAO;YACd,MAAM,sKAAIA,eAAAA,CAAa;gBACrB,SAAS;gBACT;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA,cAAc,KAAA;gBACd,OAAO;YACT,CAAC;QACH;IACF;AAEK,IAAM,uCACX,IACA,OAAO,EAAE,QAAA,EAAU,GAAA,EAAK,iBAAA,CAAkB,CAAA,KAAM;QAC9C,MAAM,kBAAkB,uBAAuB,QAAQ;QACvD,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;QAEzC,OAAO;YACL;YACA,OAAO,sKAAIA,eAAAA,CAAa;gBACtB,SAAS,SAAS,UAAA;gBAClB;gBACA;gBACA,YAAY,SAAS,MAAA;gBACrB;gBACA;YACF,CAAC;QACH;IACF;;;ACtNK,SAAS,WACdC,UAAAA,EACA,OAAA,EASgB;IAjBlB,IAAA;IAmBE,MAAM,gBAAA,CAAgB,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,aAAA,KAAT,OAAA,KAA0B;IAEhD,OAAO,eACL,sMAAA,EAAgBA,YAAW;QACzB,cAAc,gBAAgB,SAAS;QACvC,QAAQ;IACV,CAAC,GACD;QACE,UAAU,CAAA,UAAS;YACjB,MAAM,SAASA,WAAU,SAAA,CAAU,KAAK;YACxC,OAAO,OAAO,OAAA,GACV;gBAAE,SAAS;gBAAM,OAAO,OAAO,IAAA;YAAK,IACpC;gBAAE,SAAS;gBAAO,OAAO,OAAO,KAAA;YAAM;QAC5C;IACF;AAEJ;AAEO,SAAS,WACdA,UAAAA,EACA,OAAA,EASgB;IAhDlB,IAAA;IAkDE,MAAM,gBAAA,CAAgB,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,aAAA,KAAT,OAAA,KAA0B;IAEhD,MAAM,gBAAkB,oLAAA,EAAaA,YAAW;QAC9C,QAAQ;QACR,IAAI;QACJ,QAAQ,gBAAgB,QAAQ;IAClC,CAAC;IAED,OAAO,WAAW,cAAc;QAC9B,UAAU,CAAA,UAAS;YACjB,MAAM,UAAY,qKAAA,EAAUA,YAAW,KAAK;YAC5C,OAAO,OAAO,OAAA,GACV;gBAAE,SAAS;gBAAM,OAAO,OAAO,IAAA;YAAK,IACpC;gBAAE,SAAS;gBAAO,OAAO,OAAO,KAAA;YAAM;QAC5C;IACF,CAAC;AACH;AAEO,SAAS,aACdA,UAAAA,EACmC;IAEnC,OAAO,UAAUA;AACnB;AAEO,SAAS,UACdA,UAAAA,EACA,OAAA,EASgB;IAChB,IAAI,aAAaA,UAAS,GAAG;QAC3B,OAAO,WAAWA,YAAW,OAAO;IACtC,OAAO;QACL,OAAO,WAAWA,YAAW,OAAO;IACtC;AACF;;ACnFA,IAAM,eAAe,OAAO,GAAA,CAAI,kBAAkB;AAmC3C,SAAS,WACdC,WAAAA,EACA,EACE,QAAA,EACF,GAII,CAAC,CAAA,EACW;IAChB,OAAO;QACL,CAAC,YAAY,CAAA,EAAG;QAChB,OAAO,KAAA;QAAA,gCAAA;QACP,CAAC,eAAe,CAAA,EAAG;QACnB,YAAAA;QACA;IACF;AACF;AAEA,SAAS,SAAS,KAAA,EAAiC;IACjD,OACE,OAAO,UAAU,YACjB,UAAU,QACV,gBAAgB,SAChB,KAAA,CAAM,YAAY,CAAA,KAAM,QACxB,gBAAgB,SAChB,cAAc;AAElB;AAEO,SAAS,SACd,MAAA,EAKgB;IAChB,OAAO,UAAU,OACb,WAAW;QACT,YAAY,CAAC;QACb,sBAAsB;IACxB,CAAC,IACD,SAAS,MAAM,IACb,SACA,UAAU,MAAM;AACxB;;ACtFA,IAAM,EAAE,IAAA,EAAM,IAAA,CAAK,CAAA,GAAI;AAEhB,SAAS,0BAA0B,YAAA,EAAsB;IAC9D,MAAM,YAAY,aAAa,OAAA,CAAQ,MAAM,GAAG,EAAE,OAAA,CAAQ,MAAM,GAAG;IACnE,MAAM,eAAe,KAAK,SAAS;IACnC,OAAO,WAAW,IAAA,CAAK,cAAc,CAAA,OAAQ,KAAK,WAAA,CAAY,CAAC,CAAE;AACnE;AAEO,SAAS,0BAA0B,KAAA,EAA2B;IACnE,IAAI,eAAe;IAInB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;QACrC,gBAAgB,OAAO,aAAA,CAAc,KAAA,CAAM,CAAC,CAAC;IAC/C;IAEA,OAAO,KAAK,YAAY;AAC1B;AAEO,SAAS,gBAAgB,KAAA,EAAoC;IAClE,OAAO,iBAAiB,aAAa,0BAA0B,KAAK,IAAI;AAC1E;;ACzBO,SAAS,qBAAqB,GAAA,EAAyB;IAC5D,OAAO,OAAA,OAAA,KAAA,IAAA,IAAK,OAAA,CAAQ,OAAO;AAC7B", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]}}, {"offset": {"line": 2390, "column": 0}, "map": {"version": 3, "file": "browser.mjs", "sources": ["turbopack:///[project]/node_modules/bcrypt-ts/src/constant.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/base64.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/utfx.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/utils.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/crypt.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/random/browser.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/salt.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/hash.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/compare.ts", "turbopack:///[project]/node_modules/bcrypt-ts/src/helpers.ts"], "sourcesContent": ["export const BCRYPT_SALT_LEN = 16;\n\nexport const GENERATE_SALT_DEFAULT_LOG2_ROUNDS = 10;\n\nexport const BLOWFISH_NUM_ROUNDS = 16;\n\nexport const MAX_EXECUTION_TIME = 100;\n\nexport const BASE64_CODE =\n  \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n\nconst naturalNumber = Array.from({ length: 64 }, (_, i) => i);\nconst fillNegative1 = (length: number): number[] =>\n  Array<number>(length).fill(-1);\n\nexport const BASE64_INDEX = [\n  ...fillNegative1(46),\n  0,\n  1,\n  ...naturalNumber.slice(54, 64),\n  ...fillNegative1(7),\n  ...naturalNumber.slice(2, 28),\n  ...fillNegative1(6),\n  ...naturalNumber.slice(28, 54),\n  ...fillNegative1(5),\n];\n\nexport const P_ORIG = [\n  0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n  0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n  0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n];\n\nexport const S_ORIG = [\n  0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n  0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n  0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n  0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n  0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n  0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n  0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n  0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n  0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n  0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n  0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n  0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n  0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n  0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n  0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n  0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n  0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n  0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n  0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n  0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n  0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n  0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n  0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n  0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n  0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n  0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n  0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n  0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n  0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n  0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n  0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n  0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n  0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n  0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n  0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n  0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n  0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n  0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n  0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n  0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n  0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n  0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n  0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n  0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n  0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n  0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n  0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n  0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n  0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n  0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n  0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n  0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n  0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n  0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n  0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n  0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n  0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n  0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n  0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n  0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n  0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n  0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n  0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n  0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n  0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n  0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n  0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n  0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n  0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n  0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n  0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n  0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n  0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n  0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n  0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n  0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n  0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n  0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n  0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n  0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n  0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n  0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n  0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n  0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n  0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n  0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n  0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n  0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n  0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n  0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n  0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n  0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n  0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n  0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n  0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n  0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n  0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n  0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n  0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n  0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n  0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n  0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n  0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n  0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n  0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n  0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n  0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n  0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n  0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n  0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n  0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n  0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n  0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n  0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n  0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n  0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n  0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n  0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n  0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n  0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n  0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n  0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n  0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n  0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n  0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n  0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n  0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n  0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n  0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n  0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n  0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n  0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n  0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n  0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n  0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n  0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n  0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n  0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n  0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n  0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n  0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n  0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n  0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n  0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n  0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n  0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n  0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n  0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n  0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n  0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n  0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n  0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n  0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n  0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n  0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n  0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n  0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n  0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n  0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n  0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n  0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n  0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n  0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n  0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n  0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n  0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n  0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n  0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n  0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n  0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n  0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n];\n\nexport const C_ORIG = [\n  0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n];\n", "import { BASE64_CODE, BASE64_INDEX } from \"./constant.js\";\n\n/**\n * Encodes a byte array to base64 with up to length bytes of input, using the custom bcrypt alphabet.\n *\n * @param byteArray Byte array\n * @param length Maximum input length\n */\nexport const encodeBase64 = (\n  byteArray: number[] | Buffer,\n  length: number,\n): string => {\n  if (length <= 0 || length > byteArray.length)\n    throw Error(`Illegal len: ${length}`);\n\n  let off = 0;\n  let c1: number;\n  let c2: number;\n  const result: string[] = [];\n\n  while (off < length) {\n    c1 = byteArray[off++] & 0xff;\n    result.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n    c1 = (c1 & 0x03) << 4;\n    if (off >= length) {\n      result.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = byteArray[off++] & 0xff;\n    c1 |= (c2 >> 4) & 0x0f;\n    result.push(BASE64_CODE[c1 & 0x3f]);\n    c1 = (c2 & 0x0f) << 2;\n    if (off >= length) {\n      result.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = byteArray[off++] & 0xff;\n    c1 |= (c2 >> 6) & 0x03;\n    result.push(BASE64_CODE[c1 & 0x3f]);\n    result.push(BASE64_CODE[c2 & 0x3f]);\n  }\n\n  return result.join(\"\");\n};\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n *\n * @param contentString String to decode\n * @param length Maximum output length\n */\nexport const decodeBase64 = (\n  contentString: string,\n  length: number,\n): number[] => {\n  if (length <= 0) throw Error(`Illegal len: ${length}`);\n\n  const stringLength = contentString.length;\n  let off = 0;\n  let olen = 0;\n  let c1: number;\n  let c2: number;\n  let c3: number;\n  let c4: number;\n  let o: number;\n  let code: number;\n  const result: string[] = [];\n\n  while (off < stringLength - 1 && olen < length) {\n    code = contentString.charCodeAt(off++);\n    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    code = contentString.charCodeAt(off++);\n    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n\n    if (c1 == -1 || c2 == -1) break;\n\n    o = (c1 << 2) >>> 0;\n    o |= (c2 & 0x30) >> 4;\n    result.push(String.fromCharCode(o));\n\n    if (++olen >= length || off >= stringLength) break;\n\n    code = contentString.charCodeAt(off++);\n    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c3 == -1) break;\n    o = ((c2 & 0x0f) << 4) >>> 0;\n    o |= (c3 & 0x3c) >> 2;\n    result.push(String.fromCharCode(o));\n\n    if (++olen >= length || off >= stringLength) break;\n\n    code = contentString.charCodeAt(off++);\n    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    o = ((c3 & 0x03) << 6) >>> 0;\n    o |= c4;\n    result.push(String.fromCharCode(o));\n\n    ++olen;\n  }\n\n  return result.map((item) => item.charCodeAt(0));\n};\n", "/**\n * utfx-embeddable (c) 2014 <PERSON> <<EMAIL>>\n * Released under the Apache License, Version 2.0\n * see: https://github.com/dcodeIO/utfx for details\n */\n\nexport const MAX_CODEPOINT = 0x10ffff;\n\n/**\n * Encodes UTF8 code points to UTF8 bytes.\n * @param nextByte Code points source, either as a function returning the next code point\n *  respectively `null` if there are no more code points left or a single numeric code point.\n * @param destination Bytes destination as a function successively called with the next byte\n */\nexport const encodeUTF8 = (\n  nextByte: number | (() => number | null),\n  destination: (byte: number) => void,\n): void => {\n  let cp = null;\n\n  if (typeof nextByte === \"number\") {\n    cp = nextByte;\n    nextByte = (): null => null;\n  }\n\n  while (cp !== null || (cp = nextByte()) !== null) {\n    if (cp < 0x80) destination(cp & 0x7f);\n    else if (cp < 0x800) {\n      destination(((cp >> 6) & 0x1f) | 0xc0);\n      destination((cp & 0x3f) | 0x80);\n    } else if (cp < 0x10000) {\n      destination(((cp >> 12) & 0x0f) | 0xe0);\n      destination(((cp >> 6) & 0x3f) | 0x80);\n      destination((cp & 0x3f) | 0x80);\n    } else {\n      destination(((cp >> 18) & 0x07) | 0xf0);\n      destination(((cp >> 12) & 0x3f) | 0x80);\n      destination(((cp >> 6) & 0x3f) | 0x80);\n      destination((cp & 0x3f) | 0x80);\n    }\n    cp = null;\n  }\n};\n\nexport class TruncatedError extends Error {\n  bytes: number[];\n  constructor(...args: number[]) {\n    super(args.toString());\n    this.bytes = args;\n    this.name = \"TruncatedError\";\n  }\n}\n\n/**\n * Decodes UTF8 bytes to UTF8 code points.\n *\n * @param nextByte Bytes source as a function returning the next byte respectively `null` if there\n *  are no more bytes left.\n * @param destination Code points destination as a function successively called with each decoded code point.\n * @throws {RangeError} If a starting byte is invalid in UTF8\n * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the\n *  remaining bytes.\n */\nexport const decodeUTF8 = (\n  nextByte: () => number | null,\n  destination: (byte: number) => void,\n): void => {\n  let firstByte = nextByte();\n  let secondByte: number | null;\n  let thirdByte: number | null;\n  let fourthByte: number | null;\n\n  while (firstByte !== null) {\n    if ((firstByte & 0x80) === 0) {\n      destination(firstByte);\n    } else if ((firstByte & 0xe0) === 0xc0) {\n      secondByte = nextByte();\n      if (secondByte === null) throw new TruncatedError(firstByte);\n      destination(((firstByte & 0x1f) << 6) | (secondByte & 0x3f));\n    } else if ((firstByte & 0xf0) === 0xe0) {\n      secondByte = nextByte();\n      if (secondByte === null) throw new TruncatedError(firstByte);\n      thirdByte = nextByte();\n      if (thirdByte === null) throw new TruncatedError(firstByte, secondByte);\n      destination(\n        ((firstByte & 0x0f) << 12) |\n          ((secondByte & 0x3f) << 6) |\n          (thirdByte & 0x3f),\n      );\n    } else if ((firstByte & 0xf8) === 0xf0) {\n      secondByte = nextByte();\n      if (secondByte === null) throw new TruncatedError(firstByte);\n      thirdByte = nextByte();\n      if (thirdByte === null) throw new TruncatedError(firstByte, secondByte);\n      fourthByte = nextByte();\n      if (fourthByte === null)\n        throw new TruncatedError(firstByte, secondByte, thirdByte);\n      destination(\n        ((firstByte & 0x07) << 18) |\n          ((secondByte & 0x3f) << 12) |\n          ((thirdByte & 0x3f) << 6) |\n          (fourthByte & 0x3f),\n      );\n    } else throw RangeError(`Illegal starting byte: ${firstByte}`);\n\n    firstByte = nextByte();\n  }\n};\n\n/**\n * Converts UTF16 characters to UTF8 code points.\n * @param nextByte Characters source as a function returning the next char code respectively\n *  `null` if there are no more characters left.\n * @param destination Code points destination as a function successively called with each converted code\n *  point.\n */\nexport const UTF16toUTF8 = (\n  nextByte: () => number | null,\n  destination: (byte: number) => void,\n): void => {\n  let c1: number | null;\n  let c2 = null;\n\n  while (true) {\n    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n    if ((c1 = c2 !== null ? c2 : nextByte()) === null) break;\n    if (c1 >= 0xd800 && c1 <= 0xdfff) {\n      if ((c2 = nextByte()) !== null) {\n        if (c2 >= 0xdc00 && c2 <= 0xdfff) {\n          destination((c1 - 0xd800) * 0x400 + c2 - 0xdc00 + 0x10000);\n          c2 = null;\n          continue;\n        }\n      }\n    }\n    destination(c1);\n  }\n  if (c2 !== null) destination(c2);\n};\n\n/**\n * Converts UTF8 code points to UTF16 characters.\n *\n * @param nextByte Code points source, either as a function returning the next code point\n *  respectively `null` if there are no more code points left or a single numeric code point.\n * @param destination Characters destination as a function successively called with each converted char code.\n * @throws {RangeError} If a code point is out of range\n */\nexport const UTF8toUTF16 = (\n  nextByte: (() => number | null) | number,\n  destination: (byte: number) => void,\n): void => {\n  let codePoint = null;\n\n  if (typeof nextByte === \"number\") {\n    codePoint = nextByte;\n    nextByte = (): null => null;\n  }\n\n  while (codePoint !== null || (codePoint = nextByte()) !== null) {\n    if (codePoint <= 0xffff) destination(codePoint);\n    else {\n      codePoint -= 0x10000;\n      destination((codePoint >> 10) + 0xd800);\n      destination((codePoint % 0x400) + 0xdc00);\n    }\n    codePoint = null;\n  }\n};\n\n/**\n * Converts and encodes UTF16 characters to UTF8 bytes.\n * @param nextByte Characters source as a function returning the next char code respectively `null`\n *  if there are no more characters left.\n * @param destination Bytes destination as a function successively called with the next byte.\n */\nexport const encodeUTF16toUTF8 = (\n  nextByte: () => number | null,\n  destination: (byte: number) => void,\n): void =>\n  UTF16toUTF8(nextByte, (codePoint) => {\n    encodeUTF8(codePoint, destination);\n  });\n\n/**\n * Decodes and converts UTF8 bytes to UTF16 characters.\n * @param nextByte Bytes source as a function returning the next byte respectively `null` if there\n *  are no more bytes left.\n * @param destination Characters destination as a function successively called with each converted char code.\n * @throws {RangeError} If a starting byte is invalid in UTF8\n * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the remaining bytes.\n */\nexport const decodeUTF8toUTF16 = (\n  nextByte: () => number | null,\n  destination: (byte: number) => void,\n): void =>\n  decodeUTF8(nextByte, (codePoint) => {\n    UTF8toUTF16(codePoint, destination);\n  });\n\n/**\n * Calculates the byte length of an UTF8 code point.\n *\n * @param codePoint UTF8 code point\n * @returns Byte length\n */\nexport const calculateCodePoint = (codePoint: number): number =>\n  codePoint < 0x80 ? 1 : codePoint < 0x800 ? 2 : codePoint < 0x10000 ? 3 : 4;\n\n/**\n * Calculates the number of UTF8 bytes required to store UTF8 code points.\n * @param src Code points source as a function returning the next code point respectively\n *  `null` if there are no more code points left.\n * @returns The number of UTF8 bytes required\n */\nexport const calculateUTF8 = (src: () => number | null): number => {\n  let codePoint: number | null;\n  let length = 0;\n\n  while ((codePoint = src()) !== null) length += calculateCodePoint(codePoint);\n\n  return length;\n};\n\n/**\n * Calculates the number of UTF8 code points respectively UTF8 bytes required to store UTF16 char codes.\n * @param nextCharCode Characters source as a function returning the next char code respectively\n *  `null` if there are no more characters left.\n * @returns The number of UTF8 code points at index 0 and the number of UTF8 bytes required at index 1.\n */\nexport const calculateUTF16asUTF8 = (\n  nextCharCode: () => number | null,\n): number[] => {\n  let charIndexStart = 0,\n    charIndexEnd = 0;\n\n  UTF16toUTF8(nextCharCode, (codePoint) => {\n    ++charIndexStart;\n    charIndexEnd += calculateCodePoint(codePoint);\n  });\n\n  return [charIndexStart, charIndexEnd];\n};\n", "import { encodeUTF16toUTF8 } from \"./utfx.js\";\n\n/**\n * @private\n *\n * Continues with the callback on the next tick.\n */\nexport const nextTick =\n  typeof process === \"object\" && process.env.NEXT_RUNTIME === \"edge\"\n    ? setTimeout\n    : typeof setImmediate === \"function\"\n      ? setImmediate\n      : typeof process === \"object\" && typeof process.nextTick === \"function\"\n        ? // eslint-disable-next-line @typescript-eslint/unbound-method\n          process.nextTick\n        : setTimeout;\n\n/**\n * @private\n *\n * Converts a JavaScript string to UTF8 bytes.\n *\n * @param str String\n * @returns UTF8 bytes\n */\nexport const stringToBytes = (str: string): number[] => {\n  let index = 0;\n  const bytes: number[] = [];\n\n  encodeUTF16toUTF8(\n    () => (index < str.length ? str.charCodeAt(index++) : null),\n    (byte) => {\n      bytes.push(byte);\n    },\n  );\n\n  return bytes;\n};\n", "import {\n  BCRYPT_SALT_LEN,\n  BLOWFISH_NUM_ROUNDS,\n  C_ORIG,\n  MAX_EXECUTION_TIME,\n  P_ORIG,\n  S_ORIG,\n} from \"./constant.js\";\nimport { nextTick } from \"./utils.js\";\n\n// A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\nconst encipher = (\n  lr: number[],\n  off: number,\n  P: Int32Array | number[],\n  S: Int32Array | number[],\n): number[] => {\n  // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n  let n: number;\n  let l = lr[off];\n  let r = lr[off + 1];\n\n  l ^= P[0];\n\n  //The following is an unrolled version of the above loop.\n  //Iteration 0\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[1];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[2];\n  //Iteration 1\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[3];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[4];\n  //Iteration 2\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[5];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[6];\n  //Iteration 3\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[7];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[8];\n  //Iteration 4\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[9];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[10];\n  //Iteration 5\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[11];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[12];\n  //Iteration 6\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[13];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[14];\n  //Iteration 7\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[15];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[16];\n\n  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n  lr[off + 1] = l;\n\n  return lr;\n};\n\nconst streamToWord = (\n  data: number[],\n  offp: number,\n): { key: number; offp: number } => {\n  let word = 0;\n\n  for (let i = 0; i < 4; ++i) {\n    word = (word << 8) | (data[offp] & 0xff);\n    offp = (offp + 1) % data.length;\n  }\n\n  return { key: word, offp };\n};\n\nconst key = (\n  key: number[],\n  P: Int32Array | number[],\n  S: Int32Array | number[],\n): void => {\n  const pLength = P.length;\n  const sLength = S.length;\n  let offp = 0;\n  let lr = [0, 0];\n  let sw: {\n    key: number;\n    offp: number;\n  };\n\n  for (let i = 0; i < pLength; i++) {\n    sw = streamToWord(key, offp);\n    offp = sw.offp;\n    P[i] = P[i] ^ sw.key;\n  }\n\n  for (let i = 0; i < pLength; i += 2) {\n    lr = encipher(lr, 0, P, S);\n    P[i] = lr[0];\n    P[i + 1] = lr[1];\n  }\n\n  for (let i = 0; i < sLength; i += 2) {\n    lr = encipher(lr, 0, P, S);\n    S[i] = lr[0];\n    S[i + 1] = lr[1];\n  }\n};\n\n/**\n * Expensive key schedule Blowfish.\n */\nconst expensiveKeyScheduleBlowFish = (\n  data: number[],\n  key: number[],\n  P: Int32Array | number[],\n  S: Int32Array | number[],\n): void => {\n  const pLength = P.length;\n  const sLength = S.length;\n  let offp = 0;\n  let lr = [0, 0];\n  let sw: {\n    key: number;\n    offp: number;\n  };\n\n  for (let i = 0; i < pLength; i++) {\n    sw = streamToWord(key, offp);\n    offp = sw.offp;\n    P[i] = P[i] ^ sw.key;\n  }\n\n  offp = 0;\n\n  for (let i = 0; i < pLength; i += 2) {\n    sw = streamToWord(data, offp);\n    offp = sw.offp;\n    lr[0] ^= sw.key;\n    sw = streamToWord(data, offp);\n    offp = sw.offp;\n    lr[1] ^= sw.key;\n    lr = encipher(lr, 0, P, S);\n    P[i] = lr[0];\n    P[i + 1] = lr[1];\n  }\n\n  for (let i = 0; i < sLength; i += 2) {\n    sw = streamToWord(data, offp);\n    offp = sw.offp;\n    lr[0] ^= sw.key;\n    sw = streamToWord(data, offp);\n    offp = sw.offp;\n    lr[1] ^= sw.key;\n    lr = encipher(lr, 0, P, S);\n    S[i] = lr[0];\n    S[i + 1] = lr[1];\n  }\n};\n\n/**\n * Internally crypts a string.\n *\n * @param bytes Bytes to crypt\n * @param salt Salt bytes to use\n * @param rounds Number of rounds\n * @param progressCallback Callback called with the current progress\n */\nexport const crypt = (\n  bytes: number[],\n  salt: number[],\n  rounds: number,\n  sync: boolean,\n  progressCallback?: (progress: number) => void,\n): Promise<number[]> | number[] => {\n  const cdata = C_ORIG.slice();\n  const cLength = cdata.length;\n\n  // Validate\n  if (rounds < 4 || rounds > 31) {\n    const err = new Error(`Illegal number of rounds (4-31): ${rounds}`);\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  if (salt.length !== BCRYPT_SALT_LEN) {\n    const err = new Error(\n      `Illegal salt length: ${salt.length} != ${BCRYPT_SALT_LEN}`,\n    );\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  rounds = (1 << rounds) >>> 0;\n\n  let P: Int32Array | number[];\n  let S: Int32Array | number[];\n  let i = 0;\n  let j: number;\n\n  //Use typed arrays when available - huge speedup!\n  if (Int32Array) {\n    P = new Int32Array(P_ORIG);\n    S = new Int32Array(S_ORIG);\n  } else {\n    P = P_ORIG.slice();\n    S = S_ORIG.slice();\n  }\n\n  expensiveKeyScheduleBlowFish(salt, bytes, P, S);\n\n  /**\n   * Calculates the next round.\n   */\n  const next = (): Promise<number[] | undefined> | number[] | void => {\n    if (progressCallback) progressCallback(i / rounds);\n\n    if (i < rounds) {\n      const start = Date.now();\n\n      for (; i < rounds; ) {\n        i = i + 1;\n        key(bytes, P, S);\n        key(salt, P, S);\n        if (Date.now() - start > MAX_EXECUTION_TIME) break;\n      }\n    } else {\n      for (i = 0; i < 64; i++)\n        for (j = 0; j < cLength >> 1; j++) encipher(cdata, j << 1, P, S);\n      const result: number[] = [];\n\n      for (i = 0; i < cLength; i++) {\n        result.push(((cdata[i] >> 24) & 0xff) >>> 0);\n        result.push(((cdata[i] >> 16) & 0xff) >>> 0);\n        result.push(((cdata[i] >> 8) & 0xff) >>> 0);\n        result.push((cdata[i] & 0xff) >>> 0);\n      }\n\n      if (sync === false) return Promise.resolve(result);\n\n      return result;\n    }\n\n    if (sync === false)\n      return new Promise((resolve) =>\n        nextTick(() => {\n          void (next() as Promise<number[] | undefined>).then(resolve);\n        }),\n      );\n  };\n\n  if (sync === false) return next() as Promise<number[]>;\n  else {\n    let res;\n\n    while (true)\n      if (typeof (res = next()) !== \"undefined\") return (res as number[]) || [];\n  }\n};\n", "declare global {\n  interface Window {\n    msCrypto?: Crypto;\n  }\n}\n\n/**\n * @private\n *\n * Generates cryptographically secure random bytes.\n *\n * @param length Bytes length\n * @returns Random bytes\n * @throws {Error} If no random implementation is available\n */\nexport const random = (length: number): number[] => {\n  try {\n    let _crypto: Crypto | undefined;\n\n    if (typeof window !== \"undefined\") {\n      _crypto = window.crypto ?? window.msCrypto;\n    } else {\n      _crypto = globalThis.crypto;\n    }\n\n    const array = new Uint32Array(length);\n\n    _crypto?.getRandomValues(array);\n\n    return Array.from(array);\n  } catch {\n    throw Error(\"WebCryptoAPI is not available\");\n  }\n};\n", "// eslint-disable-next-line import-x/no-unresolved\nimport { random } from \"random\";\n\nimport { encodeBase64 } from \"./base64.js\";\nimport {\n  BCRYPT_SALT_LEN,\n  GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n} from \"./constant.js\";\nimport { nextTick } from \"./utils.js\";\n\n/**\n * Synchronously generates a salt.\n *\n * @param rounds Number of rounds to use, defaults to 10 if omitted\n * @returns Resulting salt\n * @throws {Error} If a random fallback is required but not set\n */\nexport const genSaltSync = (\n  rounds = GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n): string => {\n  if (typeof rounds !== \"number\")\n    throw Error(\"Illegal arguments: \" + typeof rounds);\n  if (rounds < 4) rounds = 4;\n  else if (rounds > 31) rounds = 31;\n\n  const salt = [];\n\n  salt.push(\"$2a$\");\n  if (rounds < 10) salt.push(\"0\");\n  salt.push(rounds.toString());\n  salt.push(\"$\");\n  salt.push(encodeBase64(random(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n\n  return salt.join(\"\");\n};\n\n/**\n * Asynchronously generates a salt.\n *\n * @param rounds Number of rounds to use, defaults to 10 if omitted\n */\nexport const genSalt = (\n  rounds = GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n): Promise<string> => {\n  if (typeof rounds !== \"number\")\n    throw Error(\"illegal arguments: \" + typeof rounds);\n\n  return new Promise((resolve, reject) =>\n    nextTick(() => {\n      // Pretty thin, but salting is fast enough\n      try {\n        resolve(genSaltSync(rounds));\n      } catch (err) {\n        reject(err as Error);\n      }\n    }),\n  );\n};\n", "import { decodeBase64, encodeBase64 } from \"./base64.js\";\nimport {\n  BCRYPT_SALT_LEN,\n  C_ORIG,\n  GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n} from \"./constant.js\";\nimport { crypt } from \"./crypt.js\";\nimport { genSalt, genSaltSync } from \"./salt.js\";\nimport { stringToBytes } from \"./utils.js\";\n\n/**\n * Internally hashes a string.\n *\n * @private\n * @param contentString String to hash\n * @param salt Salt to use, actually never null\n * @param progressCallback Callback called with the current progress\n */\nfunction _hash(\n  contentString: string,\n  salt: string,\n  sync: boolean,\n  progressCallback?: (progress: number) => void,\n): Promise<string> | string {\n  if (typeof contentString !== \"string\" || typeof salt !== \"string\") {\n    const err = new Error(\"Invalid string / salt: Not a string\");\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  // Validate the salt\n  let minor: string;\n  let offset: number;\n\n  if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n    const err = new Error(\"Invalid salt version: \" + salt.substring(0, 2));\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  if (salt.charAt(2) === \"$\") {\n    minor = String.fromCharCode(0);\n    offset = 3;\n  } else {\n    minor = salt.charAt(2);\n    if (\n      (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n      salt.charAt(3) !== \"$\"\n    ) {\n      const err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n\n      if (sync === false) return Promise.reject(err);\n\n      throw err;\n    }\n    offset = 4;\n  }\n\n  // Extract number of rounds\n  if (salt.charAt(offset + 2) > \"$\") {\n    const err = new Error(\"Missing salt rounds\");\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  const r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n    r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n    rounds = r1 + r2,\n    realSalt = salt.substring(offset + 3, offset + 25);\n\n  contentString += minor >= \"a\" ? \"\\x00\" : \"\";\n\n  const passwordBytes = stringToBytes(contentString),\n    saltBytes = decodeBase64(realSalt, BCRYPT_SALT_LEN);\n\n  /**\n   * Finishes hashing.\n   * @param bytes Byte array\n   */\n  const finish = (bytes: number[]): string => {\n    const res = [];\n\n    res.push(\"$2\");\n    if (minor >= \"a\") res.push(minor);\n    res.push(\"$\");\n    if (rounds < 10) res.push(\"0\");\n    res.push(rounds.toString());\n    res.push(\"$\");\n    res.push(encodeBase64(saltBytes, saltBytes.length));\n    res.push(encodeBase64(bytes, C_ORIG.length * 4 - 1));\n\n    return res.join(\"\");\n  };\n\n  // Sync\n  if (sync === false)\n    return (\n      crypt(\n        passwordBytes,\n        saltBytes,\n        rounds,\n        false,\n        progressCallback,\n      ) as Promise<number[]>\n    ).then((bytes) => finish(bytes));\n\n  return finish(\n    crypt(passwordBytes, saltBytes, rounds, true, progressCallback) as number[],\n  );\n}\n\n/**\n * Synchronously generates a hash for the given string.\n *\n * @param contentString String to hash\n * @param salt Salt length to generate or salt to use, default to 10\n * @returns Resulting hash\n */\nexport const hashSync = (\n  contentString: string,\n  salt: string | number = GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n): string => {\n  if (typeof salt === \"number\") salt = genSaltSync(salt);\n  if (typeof contentString !== \"string\" || typeof salt !== \"string\")\n    throw Error(\n      \"Illegal arguments: \" + typeof contentString + \", \" + typeof salt,\n    );\n\n  return _hash(contentString, salt, true) as string;\n};\n\n/**\n * Asynchronously generates a hash for the given string.\n *\n * @param contentString String to hash\n * @param salt Salt length to generate or salt to use\n * @param progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n */\nexport const hash = function (\n  contentString: string,\n  salt: number | string,\n  progressCallback?: (progress: number) => void,\n): Promise<string> {\n  if (typeof contentString === \"string\" && typeof salt === \"number\")\n    return genSalt(salt).then(\n      (salt) =>\n        _hash(contentString, salt, false, progressCallback) as Promise<string>,\n    );\n\n  if (typeof contentString === \"string\" && typeof salt === \"string\")\n    return _hash(\n      contentString,\n      salt,\n      false,\n      progressCallback,\n    ) as Promise<string>;\n\n  return Promise.reject(\n    new Error(`Illegal arguments: ${typeof contentString}, ${typeof salt}`),\n  );\n};\n", "import { hash as hashAsync, hashSync } from \"./hash.js\";\nimport { nextTick } from \"./utils.js\";\n\n/**\n * Synchronously tests a string against a hash.\n *\n * @param content String to compare\n * @param hash Hash to test against\n */\nexport const compareSync = (content: string, hash: string): boolean => {\n  if (typeof content !== \"string\" || typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof content + \", \" + typeof hash);\n  if (hash.length !== 60) return false;\n\n  return hashSync(content, hash.substring(0, hash.length - 31)) === hash;\n};\n\n/**\n * Asynchronously compares the given data against the given hash.\n *\n * @param content Data to compare\n * @param hash Data to be compared to\n * @param progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n */\nexport const compare = (\n  content: string,\n  hash: string,\n  progressCallback?: (percent: number) => void,\n): Promise<boolean> =>\n  new Promise((resolve, reject) => {\n    if (typeof content !== \"string\" || typeof hash !== \"string\") {\n      nextTick(() =>\n        reject(\n          new Error(`Illegal arguments: ${typeof content}, ${typeof hash}`),\n        ),\n      );\n\n      return;\n    }\n\n    if (hash.length !== 60) {\n      nextTick(() =>\n        reject(new Error(\"Illegal hash: hash length should be 60\")),\n      );\n\n      return;\n    }\n\n    hashAsync(content, hash.substring(0, 29), progressCallback)\n      .then((comp) => resolve(comp === hash))\n      .catch((err: Error) => reject(err));\n  });\n", "/**\n * Gets the number of rounds used to encrypt the specified hash.\n *\n * @param hash Hash to extract the used number of rounds from\n * @returns Number of rounds used\n * @throws {Error} If `hash` is not a string\n */\nexport const getRounds = (hash: string): number => {\n  if (typeof hash !== \"string\")\n    throw new Error(`Illegal arguments: ${typeof hash}`);\n\n  return parseInt(hash.split(\"$\")[2], 10);\n};\n\n/**\n * Gets the salt portion from a hash. Does not validate the hash.\n *\n * @param hash Hash to extract the salt from\n * @returns Extracted salt part\n * @throws {Error} If `hash` is not a string or otherwise invalid\n */\nexport const getSalt = (hash: string): string => {\n  if (typeof hash !== \"string\")\n    throw new Error(`Illegal arguments: ${typeof hash}`);\n\n  if (hash.length !== 60)\n    throw new Error(`Illegal hash length: ${hash.length} != 60`);\n\n  return hash.substring(0, 29);\n};\n"], "names": ["BCRYPT_SALT_LEN", "GENERATE_SALT_DEFAULT_LOG2_ROUNDS", "BLOWFISH_NUM_ROUNDS", "MAX_EXECUTION_TIME", "BASE64_CODE", "naturalNumber", "_", "i", "fillNegative1", "length", "BASE64_INDEX", "P_ORIG", "S_ORIG", "C_ORIG", "encodeBase64", "byteArray", "off", "c1", "c2", "result", "decodeBase64", "contentString", "stringLength", "olen", "c3", "c4", "o", "code", "item", "encodeUTF8", "nextByte", "destination", "cp", "UTF16toUTF8", "encodeUTF16toUTF8", "codePoint", "nextTick", "stringToBytes", "str", "index", "bytes", "byte", "encipher", "lr", "P", "S", "n", "l", "r", "streamToWord", "data", "offp", "word", "key", "p<PERSON><PERSON>th", "s<PERSON><PERSON>th", "sw", "expensiveKeyScheduleBlowFish", "crypt", "salt", "rounds", "sync", "progressCallback", "cdata", "c<PERSON><PERSON>th", "err", "j", "next", "start", "resolve", "res", "random", "_crypto", "array", "genSaltSync", "genSalt", "reject", "_hash", "minor", "offset", "r1", "r2", "realSalt", "passwordBytes", "saltBytes", "finish", "hashSync", "hash", "compareSync", "content", "compare", "hashAsync", "comp", "getRounds", "getSalt"], "mappings": ";;;;;;;;;;AAAO,MAAMA,IAAkB,IAElBC,IAAoC,IAEpCC,IAAsB,IAEtBC,IAAqB,KAErBC,IACX,mEAAmE,KAAA,CAAM,EAAE,GAEvEC,IAAgB,MAAM,IAAA,CAAK;IAAE,QAAQ;AAAG,GAAG,CAACC,GAAGC,IAAMA,CAAC,GACtDC,KAAiBC,IACrB,MAAcA,CAAM,EAAE,IAAA,CAAK,CAAA,CAAE,GAElBC,IAAe,CAC1B;OAAGF,EAAc,EAAE;IACnB;IACA,EACA;OAAGH,EAAc,KAAA,CAAM,IAAI,EAAE,EAC7B;OAAGG,EAAc,CAAC,EAClB;OAAGH,EAAc,KAAA,CAAM,GAAG,EAAE,EAC5B;OAAGG,EAAc,CAAC,EAClB;OAAGH,EAAc,KAAA,CAAM,IAAI,EAAE,EAC7B;OAAGG,EAAc,CAAC,CACpB;CAAA,EAEaG,IAAS;IACpB;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY,UAC9D;CAAA,EAEaC,IAAS;IACpB;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY,SACtC;CAAA,EAEaC,IAAS;IACpB;IAAY;IAAY;IAAY;IAAY;IAAY,UAC9D;CAAA,ECzMaC,IAAe,CAC1BC,GACAN,IACW;IACX,IAAIA,KAAU,KAAKA,IAASM,EAAU,MAAA,EACpC,MAAM,MAAM,CAAA,aAAA,EAAgBN,CAAM,EAAE;IAEtC,IAAIO,IAAM,GACNC,GACAC;IACJ,MAAMC,IAAmB,CAAC,CAAA;IAE1B,MAAOH,IAAMP,GAAQ;QAInB,IAHAQ,IAAKF,CAAAA,CAAUC,GAAK,CAAA,GAAI,KACxBG,EAAO,IAAA,CAAKf,CAAAA,CAAaa,KAAM,IAAK,EAAI,CAAC,GACzCA,IAAAA,CAAMA,IAAK,CAAA,KAAS,GAChBD,KAAOP,GAAQ;YACjBU,EAAO,IAAA,CAAKf,CAAAA,CAAYa,IAAK,EAAI,CAAC;YAClC;QACF;QAKA,IAJAC,IAAKH,CAAAA,CAAUC,GAAK,CAAA,GAAI,KACxBC,KAAOC,KAAM,IAAK,IAClBC,EAAO,IAAA,CAAKf,CAAAA,CAAYa,IAAK,EAAI,CAAC,GAClCA,IAAAA,CAAMC,IAAK,EAAA,KAAS,GAChBF,KAAOP,GAAQ;YACjBU,EAAO,IAAA,CAAKf,CAAAA,CAAYa,IAAK,EAAI,CAAC;YAClC;QACF;QACAC,IAAKH,CAAAA,CAAUC,GAAK,CAAA,GAAI,KACxBC,KAAOC,KAAM,IAAK,GAClBC,EAAO,IAAA,CAAKf,CAAAA,CAAYa,IAAK,EAAI,CAAC,GAClCE,EAAO,IAAA,CAAKf,CAAAA,CAAYc,IAAK,EAAI,CAAC;IACpC;IAEA,OAAOC,EAAO,IAAA,CAAK,EAAE;AACvB,GAQaC,IAAe,CAC1BC,GACAZ,IACa;IAGb,MAAMa,IAAeD,EAAc,MAAA;IACnC,IAAIL,IAAM,GACNO,IAAO,GACPN,GACAC,GACAM,GACAC,GACAC,GACAC;IACJ,MAAMR,IAAmB,CAAA,CAAA;IAEzB,MAAOH,IAAMM,IAAe,KAAKC,IAAOd,KAAAA,CACtCkB,IAAON,EAAc,UAAA,CAAWL,GAAK,GACrCC,IAAKU,IAAOjB,EAAa,MAAA,GAASA,CAAAA,CAAaiB,CAAI,CAAA,GAAI,CAAA,GACvDA,IAAON,EAAc,UAAA,CAAWL,GAAK,GACrCE,IAAKS,IAAOjB,EAAa,MAAA,GAASA,CAAAA,CAAaiB,CAAI,CAAA,GAAI,CAAA,GAEnD,CAAA,CAAAV,KAAM,CAAA,KAAMC,KAAM,CAAA,KAAA,CAEtBQ,IAAKT,KAAM,MAAO,GAClBS,KAAAA,CAAMR,IAAK,EAAA,KAAS,GACpBC,EAAO,IAAA,CAAK,OAAO,YAAA,CAAaO,CAAC,CAAC,GAE9B,EAAEH,KAAQd,KAAUO,KAAOM,CAAAA,KAAAA,CAE/BK,IAAON,EAAc,UAAA,CAAWL,GAAK,GACrCQ,IAAKG,IAAOjB,EAAa,MAAA,GAASA,CAAAA,CAAaiB,CAAI,CAAA,GAAI,CAAA,GACnDH,KAAM,CAAA,CAAA,KAAA,CACVE,IAAAA,CAAMR,IAAK,EAAA,KAAS,MAAO,GAC3BQ,KAAAA,CAAMF,IAAK,EAAA,KAAS,GACpBL,EAAO,IAAA,CAAK,OAAO,YAAA,CAAaO,CAAC,CAAC,GAE9B,EAAEH,KAAQd,KAAUO,KAAOM,CAAAA,CAAAA,CAAAA,GAE/BK,IAAON,EAAc,UAAA,CAAWL,GAAK,GACrCS,IAAKE,IAAOjB,EAAa,MAAA,GAASA,CAAAA,CAAaiB,CAAI,CAAA,GAAI,CAAA,GACvDD,IAAAA,CAAMF,IAAK,CAAA,KAAS,MAAO,GAC3BE,KAAKD,GACLN,EAAO,IAAA,CAAK,OAAO,YAAA,CAAaO,CAAC,CAAC,GAElC,EAAEH;IAGJ,OAAOJ,EAAO,GAAA,EAAKS,IAASA,EAAK,UAAA,CAAW,CAAC,CAAC;AAChD,GCvFaC,IAAa,CACxBC,GACAC,IACS;IACT,IAAIC,IAAK;IAOT,IALI,OAAOF,KAAa,YAAA,CACtBE,IAAKF,GACLA,IAAW,IAAY,IAAA,GAGlBE,MAAO,QAAA,CAASA,IAAKF,EAAAA,CAAAA,MAAgB,MACtCE,IAAK,MAAMD,EAAYC,IAAK,GAAI,IAC3BA,IAAK,OAAA,CACZD,EAAcC,KAAM,IAAK,KAAQ,GAAI,GACrCD,EAAaC,IAAK,KAAQ,GAAI,CAAA,IACrBA,IAAK,QAAA,CACdD,EAAcC,KAAM,KAAM,KAAQ,GAAI,GACtCD,EAAcC,KAAM,IAAK,KAAQ,GAAI,GACrCD,EAAaC,IAAK,KAAQ,GAAI,CAAA,IAAA,CAE9BD,EAAcC,KAAM,KAAM,IAAQ,GAAI,GACtCD,EAAcC,KAAM,KAAM,KAAQ,GAAI,GACtCD,EAAcC,KAAM,IAAK,KAAQ,GAAI,GACrCD,EAAaC,IAAK,KAAQ,GAAI,CAAA,GAEhCA,IAAK;AAET,GA0EaC,IAAc,CACzBH,GACAC,IACS;IACT,IAAId,GACAC,IAAK;IAET,MAAA,CAEOD,IAAKC,MAAO,OAAOA,IAAKY,GAAAA,MAAgB,MAFlC;QAGX,IAAIb,KAAM,SAAUA,KAAM,SAAA,CACnBC,IAAKY,GAAAA,MAAgB,QACpBZ,KAAM,SAAUA,KAAM,OAAQ;YAChCa,EAAAA,CAAad,IAAK,KAAA,IAAU,OAAQC,IAAK,QAAS,KAAO,GACzDA,IAAK;YACL;QACF;QAGJa,EAAYd,CAAE;IAChB;IACIC,MAAO,QAAMa,EAAYb,CAAE;AACjC,GAsCagB,IAAoB,CAC/BJ,GACAC,IAEAE,EAAYH,IAAWK,GAAc;QACnCN,EAAWM,GAAWJ,CAAW;IACnC,CAAC,GC/KUK,IACX,OAAO,WAAY,YAAY,QAAQ,IAAI,iCAAiB,SACxD,aACA,OAAO,gBAAiB,aACtB,eACA,OAAO,WAAY,YAAY,OAAO,QAAQ,QAAA,IAAa,aAEzD,QAAQ,QAAA,GACR,YAUGC,KAAiBC,GAA0B;IACtD,IAAIC,IAAQ;IACZ,MAAMC,IAAkB,CAAA,CAAA;IAExB,OAAAN,EACE,IAAOK,IAAQD,EAAI,MAAA,GAASA,EAAI,UAAA,CAAWC,GAAO,IAAI,OACrDE,GAAS;QACRD,EAAM,IAAA,CAAKC,CAAI;IACjB,CACF,GAEOD;AACT,GCzBME,IAAW,CACfC,GACA3B,GACA4B,GACAC,IACa;IAEb,IAAIC,GACAC,IAAIJ,CAAAA,CAAG3B,CAAG,CAAA,EACVgC,IAAIL,CAAAA,CAAG3B,IAAM,CAAC,CAAA;IAElB,OAAA+B,KAAKH,CAAAA,CAAE,CAAC,CAAA,EAIRE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,CAAC,CAAA,EAEZE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,CAAC,CAAA,EAEZE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,CAAC,CAAA,EAEZE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,CAAC,CAAA,EAEZE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,EAAE,CAAA,EAEbE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,EAAE,CAAA,EACbE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,EAAE,CAAA,EAEbE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,EAAE,CAAA,EACbE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,EAAE,CAAA,EAEbE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,EAAE,CAAA,EACbE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,EAAE,CAAA,EAEbD,CAAAA,CAAG3B,CAAG,CAAA,GAAIgC,IAAIJ,CAAAA,CAAE1C,EAAuB,CAAA,EACvCyC,CAAAA,CAAG3B,IAAM,CAAC,CAAA,GAAI+B,GAEPJ;AACT,GAEMM,IAAe,CACnBC,GACAC,IACkC;IAClC,IAAIC,IAAO;IAEX,IAAA,IAAS7C,IAAI,GAAGA,IAAI,GAAG,EAAEA,EACvB6C,IAAQA,KAAQ,IAAMF,CAAAA,CAAKC,CAAI,CAAA,GAAI,KACnCA,IAAAA,CAAQA,IAAO,CAAA,IAAKD,EAAK,MAAA;IAG3B,OAAO;QAAE,KAAKE;QAAM,MAAAD;IAAK;AAC3B,GAEME,IAAM,CACVA,GACAT,GACAC,IACS;IACT,MAAMS,IAAUV,EAAE,MAAA,EACZW,IAAUV,EAAE,MAAA;IAClB,IAAIM,IAAO,GACPR,IAAK;QAAC;QAAG,CAAC;KAAA,EACVa;IAKJ,IAAA,IAASjD,IAAI,GAAGA,IAAI+C,GAAS/C,IAC3BiD,IAAKP,EAAaI,GAAKF,CAAI,GAC3BA,IAAOK,EAAG,IAAA,EACVZ,CAAAA,CAAErC,CAAC,CAAA,GAAIqC,CAAAA,CAAErC,CAAC,CAAA,GAAIiD,EAAG,GAAA;IAGnB,IAAA,IAASjD,IAAI,GAAGA,IAAI+C,GAAS/C,KAAK,EAChCoC,IAAKD,EAASC,GAAI,GAAGC,GAAGC,CAAC,GACzBD,CAAAA,CAAErC,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA,EACXC,CAAAA,CAAErC,IAAI,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA;IAGjB,IAAA,IAASpC,IAAI,GAAGA,IAAIgD,GAAShD,KAAK,EAChCoC,IAAKD,EAASC,GAAI,GAAGC,GAAGC,CAAC,GACzBA,CAAAA,CAAEtC,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA,EACXE,CAAAA,CAAEtC,IAAI,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC;AAEnB,GAKMc,IAA+B,CACnCP,GACAG,GACAT,GACAC,IACS;IACT,MAAMS,IAAUV,EAAE,MAAA,EACZW,IAAUV,EAAE,MAAA;IAClB,IAAIM,IAAO,GACPR,IAAK;QAAC;QAAG,CAAC;KAAA,EACVa;IAKJ,IAAA,IAASjD,IAAI,GAAGA,IAAI+C,GAAS/C,IAC3BiD,IAAKP,EAAaI,GAAKF,CAAI,GAC3BA,IAAOK,EAAG,IAAA,EACVZ,CAAAA,CAAErC,CAAC,CAAA,GAAIqC,CAAAA,CAAErC,CAAC,CAAA,GAAIiD,EAAG,GAAA;IAGnBL,IAAO;IAEP,IAAA,IAAS5C,IAAI,GAAGA,IAAI+C,GAAS/C,KAAK,EAChCiD,IAAKP,EAAaC,GAAMC,CAAI,GAC5BA,IAAOK,EAAG,IAAA,EACVb,CAAAA,CAAG,CAAC,CAAA,IAAKa,EAAG,GAAA,EACZA,IAAKP,EAAaC,GAAMC,CAAI,GAC5BA,IAAOK,EAAG,IAAA,EACVb,CAAAA,CAAG,CAAC,CAAA,IAAKa,EAAG,GAAA,EACZb,IAAKD,EAASC,GAAI,GAAGC,GAAGC,CAAC,GACzBD,CAAAA,CAAErC,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA,EACXC,CAAAA,CAAErC,IAAI,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA;IAGjB,IAAA,IAASpC,IAAI,GAAGA,IAAIgD,GAAShD,KAAK,EAChCiD,IAAKP,EAAaC,GAAMC,CAAI,GAC5BA,IAAOK,EAAG,IAAA,EACVb,CAAAA,CAAG,CAAC,CAAA,IAAKa,EAAG,GAAA,EACZA,IAAKP,EAAaC,GAAMC,CAAI,GAC5BA,IAAOK,EAAG,IAAA,EACVb,CAAAA,CAAG,CAAC,CAAA,IAAKa,EAAG,GAAA,EACZb,IAAKD,EAASC,GAAI,GAAGC,GAAGC,CAAC,GACzBA,CAAAA,CAAEtC,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA,EACXE,CAAAA,CAAEtC,IAAI,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC;AAEnB,GAUae,IAAQ,CACnBlB,GACAmB,GACAC,GACAC,GACAC,IACiC;IACjC,MAAMC,IAAQlD,EAAO,KAAA,CAAM,GACrBmD,IAAUD,EAAM,MAAA;IAGtB,IAAIH,IAAS,KAAKA,IAAS,IAAI;QAC7B,MAAMK,IAAM,IAAI,MAAM,CAAA,iCAAA,EAAoCL,CAAM,EAAE;QAElE,IAAIC,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAEA,IAAIN,EAAK,MAAA,KAAW3D,IAAiB;QACnC,MAAMiE,IAAM,IAAI,MACd,CAAA,qBAAA,EAAwBN,EAAK,MAAM,CAAA,MAAA,CACrC;QAEA,IAAIE,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAEAL,IAAU,KAAKA,MAAY;IAE3B,IAAIhB,GACAC,GACAtC,IAAI,GACJ2D;IAGA,aAAA,CACFtB,IAAI,IAAI,WAAWjC,CAAM,GACzBkC,IAAI,IAAI,WAAWjC,CAAM,CAAA,IAAA,CAEzBgC,IAAIjC,EAAO,KAAA,CACXkC,GAAAA,IAAIjC,EAAO,KAAA,CAGb6C,CAAAA,GAAAA,EAA6BE,GAAMnB,GAAOI,GAAGC,CAAC;IAK9C,MAAMsB,IAAO,IAAuD;QAGlE,IAFIL,KAAkBA,EAAiBvD,IAAIqD,CAAM,GAE7CrD,IAAIqD,GAAQ;YACd,MAAMQ,IAAQ,KAAK,GAAA,CAAI;YAEvB,MAAO7D,IAAIqD,KAAAA,CACTrD,IAAIA,IAAI,GACR8C,EAAIb,GAAOI,GAAGC,CAAC,GACfQ,EAAIM,GAAMf,GAAGC,CAAC,GACV,CAAA,CAAA,KAAK,GAAA,CAAQuB,IAAAA,IAAQjE,GAAAA,CAAAA,GAAzB;QAEJ,OAAO;YACL,IAAKI,IAAI,GAAGA,IAAI,IAAIA,IAClB,IAAK2D,IAAI,GAAGA,IAAIF,KAAW,GAAGE,IAAKxB,EAASqB,GAAOG,KAAK,GAAGtB,GAAGC,CAAC;YACjE,MAAM1B,IAAmB,CAAA,CAAA;YAEzB,IAAKZ,IAAI,GAAGA,IAAIyD,GAASzD,IACvBY,EAAO,IAAA,CAAA,CAAO4C,CAAAA,CAAMxD,CAAC,CAAA,IAAK,KAAM,GAAA,MAAU,CAAC,GAC3CY,EAAO,IAAA,CAAA,CAAO4C,CAAAA,CAAMxD,CAAC,CAAA,IAAK,KAAM,GAAA,MAAU,CAAC,GAC3CY,EAAO,IAAA,CAAA,CAAO4C,CAAAA,CAAMxD,CAAC,CAAA,IAAK,IAAK,GAAA,MAAU,CAAC,GAC1CY,EAAO,IAAA,CAAA,CAAM4C,CAAAA,CAAMxD,CAAC,CAAA,GAAI,GAAA,MAAU,CAAC;YAGrC,OAAIsD,MAAS,CAAA,IAAc,QAAQ,OAAA,CAAQ1C,CAAM,IAE1CA;QACT;QAEA,IAAI0C,MAAS,CAAA,GACX,OAAO,IAAI,SAASQ,IAClBjC,EAAS,IAAM;gBACP+B,EAAAA,EAAyC,IAAA,CAAKE,CAAO;YAC7D,CAAC,CACH;IACJ;IAEA,IAAIR,MAAS,CAAA,GAAO,OAAOM,EAAAA;IACtB;QACH,IAAIG;QAEJ,OACE,IAAI,OAAA,CAAQA,IAAMH,EAAK,CAAA,IAAO,KAAa,OAAQG,KAAoB,CAC3E;IAAA;AACF,GClTaC,KAAU9D,GAA6B;IAClD,IAAI;QACF,IAAI+D;QAEA,OAAO,OAAW,MACpBA,IAAU,OAAO,MAAA,IAAU,OAAO,QAAA,GAElCA,IAAU,WAAW,MAAA;QAGvB,MAAMC,IAAQ,IAAI,YAAYhE,CAAM;QAEpC,OAAA+D,KAAA,QAAAA,EAAS,eAAA,CAAgBC,CAElB,GAAA,MAAM,IAAA,CAAKA,CAAK;IACzB,EAAA,OAAQ;QACN,MAAM,MAAM,+BAA+B;IAC7C;AACF,GChBaC,IAAc,CACzBd,IAAS3D,EAAAA,GACE;IACX,IAAI,OAAO2D,KAAW,UACpB,MAAM,MAAM,wBAAwB,OAAOA,CAAM;IAC/CA,IAAS,IAAGA,IAAS,IAChBA,IAAS,MAAA,CAAIA,IAAS,EAAA;IAE/B,MAAMD,IAAO,CAAA,CAAA;IAEb,OAAAA,EAAK,IAAA,CAAK,MAAM,GACZC,IAAS,MAAID,EAAK,IAAA,CAAK,GAAG,GAC9BA,EAAK,IAAA,CAAKC,EAAO,QAAA,EAAU,GAC3BD,EAAK,IAAA,CAAK,GAAG,GACbA,EAAK,IAAA,CAAK7C,EAAayD,EAAOvE,EAAe,GAAGA,EAAe,CAAC,GAEzD2D,EAAK,IAAA,CAAK,EAAE;AACrB,GAOagB,IAAU,CACrBf,IAAS3D,EAAAA,GACW;IACpB,IAAI,OAAO2D,KAAW,UACpB,MAAM,MAAM,wBAAwB,OAAOA,CAAM;IAEnD,OAAO,IAAI,QAAQ,CAACS,GAASO,IAC3BxC,EAAS,IAAM;YAEb,IAAI;gBACFiC,EAAQK,EAAYd,CAAM,CAAC;YAC7B,EAAA,OAASK,GAAK;gBACZW,EAAOX,CAAY;YACrB;QACF,CAAC,CACH;AACF;ACvCA,SAASY,EACPxD,CAAAA,EACAsC,CAAAA,EACAE,CAAAA,EACAC,CAAAA,CAC0B;IAC1B,IAAI,OAAOzC,KAAkB,YAAY,OAAOsC,KAAS,UAAU;QACjE,MAAMM,IAAM,IAAI,MAAM,qCAAqC;QAE3D,IAAIJ,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAGA,IAAIa,GACAC;IAEJ,IAAIpB,EAAK,MAAA,CAAO,CAAC,MAAM,OAAOA,EAAK,MAAA,CAAO,CAAC,MAAM,KAAK;QACpD,MAAMM,IAAM,IAAI,MAAM,2BAA2BN,EAAK,SAAA,CAAU,GAAG,CAAC,CAAC;QAErE,IAAIE,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAEA,IAAIN,EAAK,MAAA,CAAO,CAAC,MAAM,KACrBmB,IAAQ,MACRC,IAAS;SACJ;QAEL,IADAD,IAAQnB,EAAK,MAAA,CAAO,CAAC,GAElBmB,MAAU,OAAOA,MAAU,OAAOA,MAAU,OAC7CnB,EAAK,MAAA,CAAO,CAAC,MAAM,KACnB;YACA,MAAMM,IAAM,MAAM,4BAA4BN,EAAK,SAAA,CAAU,GAAG,CAAC,CAAC;YAElE,IAAIE,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;YAE7C,MAAMA;QACR;QACAc,IAAS;IACX;IAGA,IAAIpB,EAAK,MAAA,CAAOoB,IAAS,CAAC,IAAI,KAAK;QACjC,MAAMd,IAAM,IAAI,MAAM,qBAAqB;QAE3C,IAAIJ,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAEA,MAAMe,IAAK,SAASrB,EAAK,SAAA,CAAUoB,GAAQA,IAAS,CAAC,GAAG,EAAE,IAAI,IAC5DE,IAAK,SAAStB,EAAK,SAAA,CAAUoB,IAAS,GAAGA,IAAS,CAAC,GAAG,EAAE,GACxDnB,IAASoB,IAAKC,GACdC,IAAWvB,EAAK,SAAA,CAAUoB,IAAS,GAAGA,IAAS,EAAE;IAEnD1D,KAAiByD,KAAS,MAAM,OAAS;IAEzC,MAAMK,IAAgB9C,EAAchB,CAAa,GAC/C+D,IAAYhE,EAAa8D,GAAUlF,EAAe,GAM9CqF,KAAU7C,GAA4B;QAC1C,MAAM8B,IAAM,CAAA,CAAA;QAEZ,OAAAA,EAAI,IAAA,CAAK,IAAI,GACTQ,KAAS,OAAKR,EAAI,IAAA,CAAKQ,CAAK,GAChCR,EAAI,IAAA,CAAK,GAAG,GACRV,IAAS,MAAIU,EAAI,IAAA,CAAK,GAAG,GAC7BA,EAAI,IAAA,CAAKV,EAAO,QAAA,EAAU,GAC1BU,EAAI,IAAA,CAAK,GAAG,GACZA,EAAI,IAAA,CAAKxD,EAAasE,GAAWA,EAAU,MAAM,CAAC,GAClDd,EAAI,IAAA,CAAKxD,EAAa0B,GAAO3B,EAAO,MAAA,GAAS,IAAI,CAAC,CAAC,GAE5CyD,EAAI,IAAA,CAAK,EAAE;IACpB;IAGA,OAAIT,MAAS,CAAA,IAETH,EACEyB,GACAC,GACAxB,GACA,CAAA,GACAE,CACF,EACA,IAAA,EAAMtB,IAAU6C,EAAO7C,CAAK,CAAC,IAE1B6C,EACL3B,EAAMyB,GAAeC,GAAWxB,GAAQ,CAAA,GAAME,CAAgB,CAChE;AACF;AAAA,MASawB,IAAW,CACtBjE,GACAsC,IAAwB1D,EAAAA,GACb;IAEX,IADI,OAAO0D,KAAS,YAAA,CAAUA,IAAOe,EAAYf,CAAI,CAAA,GACjD,OAAOtC,KAAkB,YAAY,OAAOsC,KAAS,UACvD,MAAM,MACJ,wBAAwB,OAAOtC,IAAgB,OAAO,OAAOsC,CAC/D;IAEF,OAAOkB,EAAMxD,GAAesC,GAAM,CAAA,CAAI;AACxC,GAUa4B,IAAO,SAClBlE,CAAAA,EACAsC,CAAAA,EACAG,CAAAA,CACiB;IACjB,OAAI,OAAOzC,KAAkB,YAAY,OAAOsC,KAAS,WAChDgB,EAAQhB,CAAI,EAAE,IAAA,EAClBA,IACCkB,EAAMxD,GAAesC,GAAM,CAAA,GAAOG,CAAgB,CACtD,IAEE,OAAOzC,KAAkB,YAAY,OAAOsC,KAAS,WAChDkB,EACLxD,GACAsC,GACA,CAAA,GACAG,CACF,IAEK,QAAQ,MAAA,CACb,IAAI,MAAM,CAAA,mBAAA,EAAsB,OAAOzC,CAAa,CAAA,EAAA,EAAK,OAAOsC,CAAI,EAAE,CACxE;AACF,GC9Ja6B,IAAc,CAACC,GAAiBF,IAA0B;IACrE,IAAI,OAAOE,KAAY,YAAY,OAAOF,KAAS,UACjD,MAAM,MAAM,wBAAwB,OAAOE,IAAU,OAAO,OAAOF,CAAI;IACzE,OAAIA,EAAK,MAAA,KAAW,KAAW,CAAA,IAExBD,EAASG,GAASF,EAAK,SAAA,CAAU,GAAGA,EAAK,MAAA,GAAS,EAAE,CAAC,MAAMA;AACpE,GAUaG,IAAU,CACrBD,GACAF,GACAzB,IAEA,IAAI,QAAQ,CAACO,GAASO,IAAW;QAC/B,IAAI,OAAOa,KAAY,YAAY,OAAOF,KAAS,UAAU;YAC3DnD,EAAS,IACPwC,EACE,IAAI,MAAM,CAAA,mBAAA,EAAsB,OAAOa,CAAO,CAAA,EAAA,EAAK,OAAOF,CAAI,EAAE,CAClE,CACF;YAEA;QACF;QAEA,IAAIA,EAAK,MAAA,KAAW,IAAI;YACtBnD,EAAS,IACPwC,EAAO,IAAI,MAAM,wCAAwC,CAAC,CAC5D;YAEA;QACF;QAEAe,EAAUF,GAASF,EAAK,SAAA,CAAU,GAAG,EAAE,GAAGzB,CAAgB,EACvD,IAAA,EAAM8B,IAASvB,EAAQuB,MAASL,CAAI,CAAC,EACrC,KAAA,EAAOtB,IAAeW,EAAOX,CAAG,CAAC;IACtC,CAAC,GC7CU4B,KAAaN,GAAyB;IACjD,IAAI,OAAOA,KAAS,UAClB,MAAM,IAAI,MAAM,CAAA,mBAAA,EAAsB,OAAOA,CAAI,EAAE;IAErD,OAAO,SAASA,EAAK,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,EAAG,EAAE;AACxC,GASaO,KAAWP,GAAyB;IAC/C,IAAI,OAAOA,KAAS,UAClB,MAAM,IAAI,MAAM,CAAA,mBAAA,EAAsB,OAAOA,CAAI,EAAE;IAErD,IAAIA,EAAK,MAAA,KAAW,IAClB,MAAM,IAAI,MAAM,CAAA,qBAAA,EAAwBA,EAAK,MAAM,CAAA,MAAA,CAAQ;IAE7D,OAAOA,EAAK,SAAA,CAAU,GAAG,EAAE;AAC7B", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}}]}