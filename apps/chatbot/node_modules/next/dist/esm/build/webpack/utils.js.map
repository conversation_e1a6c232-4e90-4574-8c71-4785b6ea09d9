{"version": 3, "sources": ["../../../src/build/webpack/utils.ts"], "sourcesContent": ["import type {\n  Compilation,\n  Chunk,\n  ChunkGroup,\n  NormalModule,\n  Module,\n  ModuleGraph,\n  Compiler,\n} from 'webpack'\nimport type { ModuleGraphConnection } from 'webpack'\nimport { getAppLoader } from '../entries'\nimport { spans as webpackCompilationSpans } from './plugins/profiling-plugin'\nimport { compilationSpans as rspackCompilationSpans } from './plugins/rspack-profiling-plugin'\nimport type { Span } from '../../trace'\n\nexport function traverseModules(\n  compilation: Compilation,\n  callback: (\n    mod: any,\n    chunk: Chunk,\n    chunkGroup: (typeof compilation.chunkGroups)[0],\n    modId: string | null\n  ) => any,\n  filterChunkGroup?: (chunkGroup: ChunkGroup) => boolean\n) {\n  compilation.chunkGroups.forEach((chunkGroup) => {\n    if (filterChunkGroup && !filterChunkGroup(chunkGroup)) {\n      return\n    }\n    chunkGroup.chunks.forEach((chunk: Chunk) => {\n      const chunkModules = compilation.chunkGraph.getChunkModulesIterable(\n        chunk\n        // TODO: Update type so that it doesn't have to be cast.\n      ) as Iterable<NormalModule>\n      for (const mod of chunkModules) {\n        const modId = compilation.chunkGraph.getModuleId(mod)?.toString()\n        if (modId) callback(mod, chunk, chunkGroup, modId)\n        const anyModule = mod as any\n        if (anyModule.modules) {\n          for (const subMod of anyModule.modules)\n            if (modId) callback(subMod, chunk, chunkGroup, modId)\n        }\n      }\n    })\n  })\n}\n\n// Loop over all the entry modules.\nexport function forEachEntryModule(\n  compilation: any,\n  callback: ({ name, entryModule }: { name: string; entryModule: any }) => void\n) {\n  for (const [name, entry] of compilation.entries.entries()) {\n    // Skip for entries under pages/\n    if (name.startsWith('pages/')) {\n      continue\n    }\n\n    // Check if the page entry is a server component or not.\n    const entryDependency: NormalModule | undefined = entry.dependencies?.[0]\n    // Ensure only next-app-loader entries are handled.\n    if (!entryDependency || !entryDependency.request) continue\n\n    const request = entryDependency.request\n\n    if (\n      !request.startsWith('next-edge-ssr-loader?') &&\n      !request.startsWith('next-edge-app-route-loader?') &&\n      !request.startsWith(`${getAppLoader()}?`)\n    )\n      continue\n\n    let entryModule: NormalModule =\n      compilation.moduleGraph.getResolvedModule(entryDependency)\n\n    if (\n      request.startsWith('next-edge-ssr-loader?') ||\n      request.startsWith('next-edge-app-route-loader?')\n    ) {\n      entryModule.dependencies.forEach((dependency) => {\n        const modRequest: string | undefined = (dependency as any).request\n        if (modRequest?.includes(getAppLoader())) {\n          entryModule = compilation.moduleGraph.getResolvedModule(dependency)\n        }\n      })\n    }\n\n    callback({ name, entryModule })\n  }\n}\n\nexport function formatBarrelOptimizedResource(\n  resource: string,\n  matchResource: string\n) {\n  return `${resource}@${matchResource}`\n}\n\nexport function getModuleReferencesInOrder(\n  module: Module,\n  moduleGraph: ModuleGraph\n): ModuleGraphConnection[] {\n  if (\n    'getOutgoingConnectionsInOrder' in moduleGraph &&\n    typeof moduleGraph.getOutgoingConnectionsInOrder === 'function'\n  ) {\n    return moduleGraph.getOutgoingConnectionsInOrder(module)\n  }\n  const connections = []\n  for (const connection of moduleGraph.getOutgoingConnections(module)) {\n    if (connection.dependency && connection.module) {\n      connections.push({\n        connection,\n        index: moduleGraph.getParentBlockIndex(connection.dependency),\n      })\n    }\n  }\n  connections.sort((a, b) => a.index - b.index)\n  return connections.map((c) => c.connection)\n}\n\nexport function getCompilationSpan(\n  compilation: Compiler | Compilation\n): Span | undefined {\n  const compilationSpans = process.env.NEXT_RSPACK\n    ? rspackCompilationSpans\n    : webpackCompilationSpans\n\n  return compilationSpans.get(compilation)\n}\n"], "names": ["getApp<PERSON><PERSON>der", "spans", "webpackCompilationSpans", "compilationSpans", "rspackCompilationSpans", "traverseModules", "compilation", "callback", "filterChunkGroup", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "mod", "modId", "getModuleId", "toString", "anyModule", "modules", "subMod", "forEachEntryModule", "name", "entry", "entries", "startsWith", "entryDependency", "dependencies", "request", "entryModule", "moduleGraph", "getResolvedModule", "dependency", "modRequest", "includes", "formatBarrelOptimizedResource", "resource", "matchResource", "getModuleReferencesInOrder", "module", "getOutgoingConnectionsInOrder", "connections", "connection", "getOutgoingConnections", "push", "index", "getParentBlockIndex", "sort", "a", "b", "map", "c", "getCompilationSpan", "process", "env", "NEXT_RSPACK", "get"], "mappings": "AAUA,SAASA,YAAY,QAAQ,aAAY;AACzC,SAASC,SAASC,uBAAuB,QAAQ,6BAA4B;AAC7E,SAASC,oBAAoBC,sBAAsB,QAAQ,oCAAmC;AAG9F,OAAO,SAASC,gBACdC,WAAwB,EACxBC,QAKQ,EACRC,gBAAsD;IAEtDF,YAAYG,WAAW,CAACC,OAAO,CAAC,CAACC;QAC/B,IAAIH,oBAAoB,CAACA,iBAAiBG,aAAa;YACrD;QACF;QACAA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;YACzB,MAAMC,eAAeR,YAAYS,UAAU,CAACC,uBAAuB,CACjEH;YAGF,KAAK,MAAMI,OAAOH,aAAc;oBAChBR;gBAAd,MAAMY,SAAQZ,sCAAAA,YAAYS,UAAU,CAACI,WAAW,CAACF,yBAAnCX,oCAAyCc,QAAQ;gBAC/D,IAAIF,OAAOX,SAASU,KAAKJ,OAAOF,YAAYO;gBAC5C,MAAMG,YAAYJ;gBAClB,IAAII,UAAUC,OAAO,EAAE;oBACrB,KAAK,MAAMC,UAAUF,UAAUC,OAAO,CACpC,IAAIJ,OAAOX,SAASgB,QAAQV,OAAOF,YAAYO;gBACnD;YACF;QACF;IACF;AACF;AAEA,mCAAmC;AACnC,OAAO,SAASM,mBACdlB,WAAgB,EAChBC,QAA6E;IAE7E,KAAK,MAAM,CAACkB,MAAMC,MAAM,IAAIpB,YAAYqB,OAAO,CAACA,OAAO,GAAI;YAOPD;QANlD,gCAAgC;QAChC,IAAID,KAAKG,UAAU,CAAC,WAAW;YAC7B;QACF;QAEA,wDAAwD;QACxD,MAAMC,mBAA4CH,sBAAAA,MAAMI,YAAY,qBAAlBJ,mBAAoB,CAAC,EAAE;QACzE,mDAAmD;QACnD,IAAI,CAACG,mBAAmB,CAACA,gBAAgBE,OAAO,EAAE;QAElD,MAAMA,UAAUF,gBAAgBE,OAAO;QAEvC,IACE,CAACA,QAAQH,UAAU,CAAC,4BACpB,CAACG,QAAQH,UAAU,CAAC,kCACpB,CAACG,QAAQH,UAAU,CAAC,GAAG5B,eAAe,CAAC,CAAC,GAExC;QAEF,IAAIgC,cACF1B,YAAY2B,WAAW,CAACC,iBAAiB,CAACL;QAE5C,IACEE,QAAQH,UAAU,CAAC,4BACnBG,QAAQH,UAAU,CAAC,gCACnB;YACAI,YAAYF,YAAY,CAACpB,OAAO,CAAC,CAACyB;gBAChC,MAAMC,aAAiC,AAACD,WAAmBJ,OAAO;gBAClE,IAAIK,8BAAAA,WAAYC,QAAQ,CAACrC,iBAAiB;oBACxCgC,cAAc1B,YAAY2B,WAAW,CAACC,iBAAiB,CAACC;gBAC1D;YACF;QACF;QAEA5B,SAAS;YAAEkB;YAAMO;QAAY;IAC/B;AACF;AAEA,OAAO,SAASM,8BACdC,QAAgB,EAChBC,aAAqB;IAErB,OAAO,GAAGD,SAAS,CAAC,EAAEC,eAAe;AACvC;AAEA,OAAO,SAASC,2BACdC,MAAc,EACdT,WAAwB;IAExB,IACE,mCAAmCA,eACnC,OAAOA,YAAYU,6BAA6B,KAAK,YACrD;QACA,OAAOV,YAAYU,6BAA6B,CAACD;IACnD;IACA,MAAME,cAAc,EAAE;IACtB,KAAK,MAAMC,cAAcZ,YAAYa,sBAAsB,CAACJ,QAAS;QACnE,IAAIG,WAAWV,UAAU,IAAIU,WAAWH,MAAM,EAAE;YAC9CE,YAAYG,IAAI,CAAC;gBACfF;gBACAG,OAAOf,YAAYgB,mBAAmB,CAACJ,WAAWV,UAAU;YAC9D;QACF;IACF;IACAS,YAAYM,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEH,KAAK,GAAGI,EAAEJ,KAAK;IAC5C,OAAOJ,YAAYS,GAAG,CAAC,CAACC,IAAMA,EAAET,UAAU;AAC5C;AAEA,OAAO,SAASU,mBACdjD,WAAmC;IAEnC,MAAMH,mBAAmBqD,QAAQC,GAAG,CAACC,WAAW,GAC5CtD,yBACAF;IAEJ,OAAOC,iBAAiBwD,GAAG,CAACrD;AAC9B"}