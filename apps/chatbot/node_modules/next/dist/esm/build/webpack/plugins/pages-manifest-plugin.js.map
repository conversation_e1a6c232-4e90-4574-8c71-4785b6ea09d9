{"version": 3, "sources": ["../../../../src/build/webpack/plugins/pages-manifest-plugin.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs/promises'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  PAGES_MANIFEST,\n  APP_PATHS_MANIFEST,\n} from '../../../shared/lib/constants'\nimport getRouteFromEntrypoint from '../../../server/get-route-from-entrypoint'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\n\nexport type PagesManifest = { [page: string]: string }\n\nexport let edgeServerPages = {}\nexport let nodeServerPages = {}\nexport let edgeServerAppPaths = {}\nexport let nodeServerAppPaths = {}\n\n// This plugin creates a pages-manifest.json from page entrypoints.\n// This is used for mapping paths like `/` to `.next/server/static/<buildid>/pages/index.js` when doing SSR\n// It's also used by next export to provide defaultPathMap\nexport default class PagesManifestPlugin\n  implements webpack.WebpackPluginInstance\n{\n  dev: boolean\n  distDir?: string\n  isEdgeRuntime: boolean\n  appDirEnabled: boolean\n\n  constructor({\n    dev,\n    distDir,\n    isEdgeRuntime,\n    appDirEnabled,\n  }: {\n    dev: boolean\n    distDir?: string\n    isEdgeRuntime: boolean\n    appDirEnabled: boolean\n  }) {\n    this.dev = dev\n    this.distDir = distDir\n    this.isEdgeRuntime = isEdgeRuntime\n    this.appDirEnabled = appDirEnabled\n  }\n\n  async createAssets(compilation: any) {\n    const entrypoints = compilation.entrypoints\n    const pages: PagesManifest = {}\n    const appPaths: PagesManifest = {}\n\n    for (const entrypoint of entrypoints.values()) {\n      const pagePath = getRouteFromEntrypoint(\n        entrypoint.name,\n        this.appDirEnabled\n      )\n\n      if (!pagePath) {\n        continue\n      }\n\n      const files = entrypoint\n        .getFiles()\n        .filter(\n          (file: string) =>\n            !file.includes('webpack-runtime') &&\n            !file.includes('webpack-api-runtime') &&\n            file.endsWith('.js')\n        )\n\n      // Skip entries which are empty\n      if (!files.length) {\n        continue\n      }\n      // Write filename, replace any backslashes in path (on windows) with forwardslashes for cross-platform consistency.\n      let file = files[files.length - 1]\n\n      if (!this.dev) {\n        if (!this.isEdgeRuntime) {\n          file = file.slice(3)\n        }\n      }\n      file = normalizePathSep(file)\n\n      if (entrypoint.name.startsWith('app/')) {\n        appPaths[pagePath] = file\n      } else {\n        pages[pagePath] = file\n      }\n    }\n\n    // This plugin is used by both the Node server and Edge server compilers,\n    // we need to merge both pages to generate the full manifest.\n    if (this.isEdgeRuntime) {\n      edgeServerPages = pages\n      edgeServerAppPaths = appPaths\n    } else {\n      nodeServerPages = pages\n      nodeServerAppPaths = appPaths\n    }\n\n    // handle parallel compilers writing to the same\n    // manifest path by merging existing manifest with new\n    const writeMergedManifest = async (\n      manifestPath: string,\n      entries: Record<string, string>\n    ) => {\n      await fs.mkdir(path.dirname(manifestPath), { recursive: true })\n      await fs.writeFile(\n        manifestPath,\n        JSON.stringify(\n          {\n            ...(await fs\n              .readFile(manifestPath, 'utf8')\n              .then((res) => JSON.parse(res))\n              .catch(() => ({}))),\n            ...entries,\n          },\n          null,\n          2\n        )\n      )\n    }\n\n    if (this.distDir) {\n      const pagesManifestPath = path.join(\n        this.distDir,\n        'server',\n        PAGES_MANIFEST\n      )\n      await writeMergedManifest(pagesManifestPath, {\n        ...edgeServerPages,\n        ...nodeServerPages,\n      })\n    } else {\n      const pagesManifestPath =\n        (!this.dev && !this.isEdgeRuntime ? '../' : '') + PAGES_MANIFEST\n      compilation.emitAsset(\n        pagesManifestPath,\n        new sources.RawSource(\n          JSON.stringify(\n            {\n              ...edgeServerPages,\n              ...nodeServerPages,\n            },\n            null,\n            2\n          )\n        )\n      )\n    }\n\n    if (this.appDirEnabled) {\n      if (this.distDir) {\n        const appPathsManifestPath = path.join(\n          this.distDir,\n          'server',\n          APP_PATHS_MANIFEST\n        )\n        await writeMergedManifest(appPathsManifestPath, {\n          ...edgeServerAppPaths,\n          ...nodeServerAppPaths,\n        })\n      } else {\n        compilation.emitAsset(\n          (!this.dev && !this.isEdgeRuntime ? '../' : '') + APP_PATHS_MANIFEST,\n          new sources.RawSource(\n            JSON.stringify(\n              {\n                ...edgeServerAppPaths,\n                ...nodeServerAppPaths,\n              },\n              null,\n              2\n            )\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    }\n  }\n\n  apply(compiler: webpack.Compiler): void {\n    compiler.hooks.make.tap('NextJsPagesManifest', (compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: 'NextJsPagesManifest',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => this.createAssets(compilation)\n      )\n    })\n  }\n}\n"], "names": ["path", "fs", "webpack", "sources", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "getRouteFromEntrypoint", "normalizePathSep", "edgeServerPages", "nodeServerPages", "edgeServerAppPaths", "nodeServerAppPaths", "PagesManifestPlugin", "constructor", "dev", "distDir", "isEdgeRuntime", "appDirEnabled", "createAssets", "compilation", "entrypoints", "pages", "appPaths", "entrypoint", "values", "pagePath", "name", "files", "getFiles", "filter", "file", "includes", "endsWith", "length", "slice", "startsWith", "writeMergedManifest", "manifestPath", "entries", "mkdir", "dirname", "recursive", "writeFile", "JSON", "stringify", "readFile", "then", "res", "parse", "catch", "pagesManifestPath", "join", "emitAsset", "RawSource", "appPathsManifestPath", "apply", "compiler", "hooks", "make", "tap", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,cAAc,EACdC,kBAAkB,QACb,gCAA+B;AACtC,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,gBAAgB,QAAQ,mDAAkD;AAInF,OAAO,IAAIC,kBAAkB,CAAC,EAAC;AAC/B,OAAO,IAAIC,kBAAkB,CAAC,EAAC;AAC/B,OAAO,IAAIC,qBAAqB,CAAC,EAAC;AAClC,OAAO,IAAIC,qBAAqB,CAAC,EAAC;AAElC,mEAAmE;AACnE,2GAA2G;AAC3G,0DAA0D;AAC1D,eAAe,MAAMC;IAQnBC,YAAY,EACVC,GAAG,EACHC,OAAO,EACPC,aAAa,EACbC,aAAa,EAMd,CAAE;QACD,IAAI,CAACH,GAAG,GAAGA;QACX,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACC,aAAa,GAAGA;IACvB;IAEA,MAAMC,aAAaC,WAAgB,EAAE;QACnC,MAAMC,cAAcD,YAAYC,WAAW;QAC3C,MAAMC,QAAuB,CAAC;QAC9B,MAAMC,WAA0B,CAAC;QAEjC,KAAK,MAAMC,cAAcH,YAAYI,MAAM,GAAI;YAC7C,MAAMC,WAAWnB,uBACfiB,WAAWG,IAAI,EACf,IAAI,CAACT,aAAa;YAGpB,IAAI,CAACQ,UAAU;gBACb;YACF;YAEA,MAAME,QAAQJ,WACXK,QAAQ,GACRC,MAAM,CACL,CAACC,OACC,CAACA,KAAKC,QAAQ,CAAC,sBACf,CAACD,KAAKC,QAAQ,CAAC,0BACfD,KAAKE,QAAQ,CAAC;YAGpB,+BAA+B;YAC/B,IAAI,CAACL,MAAMM,MAAM,EAAE;gBACjB;YACF;YACA,mHAAmH;YACnH,IAAIH,OAAOH,KAAK,CAACA,MAAMM,MAAM,GAAG,EAAE;YAElC,IAAI,CAAC,IAAI,CAACnB,GAAG,EAAE;gBACb,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;oBACvBc,OAAOA,KAAKI,KAAK,CAAC;gBACpB;YACF;YACAJ,OAAOvB,iBAAiBuB;YAExB,IAAIP,WAAWG,IAAI,CAACS,UAAU,CAAC,SAAS;gBACtCb,QAAQ,CAACG,SAAS,GAAGK;YACvB,OAAO;gBACLT,KAAK,CAACI,SAAS,GAAGK;YACpB;QACF;QAEA,yEAAyE;QACzE,6DAA6D;QAC7D,IAAI,IAAI,CAACd,aAAa,EAAE;YACtBR,kBAAkBa;YAClBX,qBAAqBY;QACvB,OAAO;YACLb,kBAAkBY;YAClBV,qBAAqBW;QACvB;QAEA,gDAAgD;QAChD,sDAAsD;QACtD,MAAMc,sBAAsB,OAC1BC,cACAC;YAEA,MAAMrC,GAAGsC,KAAK,CAACvC,KAAKwC,OAAO,CAACH,eAAe;gBAAEI,WAAW;YAAK;YAC7D,MAAMxC,GAAGyC,SAAS,CAChBL,cACAM,KAAKC,SAAS,CACZ;gBACE,GAAI,MAAM3C,GACP4C,QAAQ,CAACR,cAAc,QACvBS,IAAI,CAAC,CAACC,MAAQJ,KAAKK,KAAK,CAACD,MACzBE,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA,EAAG;gBACpB,GAAGX,OAAO;YACZ,GACA,MACA;QAGN;QAEA,IAAI,IAAI,CAACvB,OAAO,EAAE;YAChB,MAAMmC,oBAAoBlD,KAAKmD,IAAI,CACjC,IAAI,CAACpC,OAAO,EACZ,UACAX;YAEF,MAAMgC,oBAAoBc,mBAAmB;gBAC3C,GAAG1C,eAAe;gBAClB,GAAGC,eAAe;YACpB;QACF,OAAO;YACL,MAAMyC,oBACJ,AAAC,CAAA,CAAC,IAAI,CAACpC,GAAG,IAAI,CAAC,IAAI,CAACE,aAAa,GAAG,QAAQ,EAAC,IAAKZ;YACpDe,YAAYiC,SAAS,CACnBF,mBACA,IAAI/C,QAAQkD,SAAS,CACnBV,KAAKC,SAAS,CACZ;gBACE,GAAGpC,eAAe;gBAClB,GAAGC,eAAe;YACpB,GACA,MACA;QAIR;QAEA,IAAI,IAAI,CAACQ,aAAa,EAAE;YACtB,IAAI,IAAI,CAACF,OAAO,EAAE;gBAChB,MAAMuC,uBAAuBtD,KAAKmD,IAAI,CACpC,IAAI,CAACpC,OAAO,EACZ,UACAV;gBAEF,MAAM+B,oBAAoBkB,sBAAsB;oBAC9C,GAAG5C,kBAAkB;oBACrB,GAAGC,kBAAkB;gBACvB;YACF,OAAO;gBACLQ,YAAYiC,SAAS,CACnB,AAAC,CAAA,CAAC,IAAI,CAACtC,GAAG,IAAI,CAAC,IAAI,CAACE,aAAa,GAAG,QAAQ,EAAC,IAAKX,oBAClD,IAAIF,QAAQkD,SAAS,CACnBV,KAAKC,SAAS,CACZ;oBACE,GAAGlC,kBAAkB;oBACrB,GAAGC,kBAAkB;gBACvB,GACA,MACA;YAIR;QACF;IACF;IAEA4C,MAAMC,QAA0B,EAAQ;QACtCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAACxC;YAC9CA,YAAYsC,KAAK,CAACG,aAAa,CAACC,UAAU,CACxC;gBACEnC,MAAM;gBACNoC,OAAO5D,QAAQ6D,WAAW,CAACC,8BAA8B;YAC3D,GACA,IAAM,IAAI,CAAC9C,YAAY,CAACC;QAE5B;IACF;AACF"}