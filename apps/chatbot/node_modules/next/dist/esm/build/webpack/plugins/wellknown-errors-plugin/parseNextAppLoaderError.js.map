{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNextAppLoaderError.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { relative } from 'path'\nimport { SimpleWebpackError } from './simpleWebpackError'\nimport { getAppLoader } from '../../../entries'\n\nexport function getNextAppLoaderError(\n  err: Error,\n  module: any,\n  compiler: webpack.Compiler\n): SimpleWebpackError | false {\n  try {\n    if (!module.loaders[0].loader.includes(getAppLoader())) {\n      return false\n    }\n\n    const file = relative(\n      compiler.context,\n      module.buildInfo.route.absolutePagePath\n    )\n\n    return new SimpleWebpackError(file, err.message)\n  } catch {\n    return false\n  }\n}\n"], "names": ["relative", "SimpleWebpackError", "getApp<PERSON><PERSON>der", "getNextAppLoaderError", "err", "module", "compiler", "loaders", "loader", "includes", "file", "context", "buildInfo", "route", "absolutePagePath", "message"], "mappings": "AACA,SAASA,QAAQ,QAAQ,OAAM;AAC/B,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SAASC,YAAY,QAAQ,mBAAkB;AAE/C,OAAO,SAASC,sBACdC,GAAU,EACVC,MAAW,EACXC,QAA0B;IAE1B,IAAI;QACF,IAAI,CAACD,OAAOE,OAAO,CAAC,EAAE,CAACC,MAAM,CAACC,QAAQ,CAACP,iBAAiB;YACtD,OAAO;QACT;QAEA,MAAMQ,OAAOV,SACXM,SAASK,OAAO,EAChBN,OAAOO,SAAS,CAACC,KAAK,CAACC,gBAAgB;QAGzC,OAAO,IAAIb,mBAAmBS,MAAMN,IAAIW,OAAO;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF"}