{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/index.ts"], "sourcesContent": ["import Warning from './Warning'\nimport SyntaxError from './Error'\nimport { normalizeSourceMap, normalizeSourceMapAfterPostcss } from './utils'\n\n/**\n * **PostCSS Loader**\n *\n * Loads && processes CSS with [PostCSS](https://github.com/postcss/postcss)\n */\nexport default async function loader(\n  this: any,\n  /** Source */\n  content: string,\n  /** Source Map */\n  sourceMap: any,\n  meta: any\n): Promise<void> {\n  const loaderSpan = this.currentTraceSpan.traceChild('postcss-loader')\n  const callback = this.async()\n\n  loaderSpan\n    .traceAsyncFn(async () => {\n      const options = this.getOptions()\n      const file = this.resourcePath\n\n      const useSourceMap =\n        typeof options.sourceMap !== 'undefined'\n          ? options.sourceMap\n          : this.sourceMap\n\n      const processOptions: any = {\n        from: file,\n        to: file,\n      }\n\n      if (useSourceMap) {\n        processOptions.map = {\n          inline: false,\n          annotation: false,\n          ...processOptions.map,\n        }\n      }\n\n      if (sourceMap && processOptions.map) {\n        processOptions.map.prev = loaderSpan\n          .traceChild('normalize-source-map')\n          .traceFn(() => normalizeSourceMap(sourceMap, this.context))\n      }\n\n      let root: any\n\n      // Reuse PostCSS AST from other loaders\n      if (meta && meta.ast && meta.ast.type === 'postcss') {\n        ;({ root } = meta.ast)\n        loaderSpan.setAttribute('astUsed', 'true')\n      }\n\n      // Initializes postcss with plugins\n      const { postcssWithPlugins } = await options.postcss()\n\n      let result\n\n      try {\n        result = await loaderSpan\n          .traceChild('postcss-process')\n          .traceAsyncFn(() =>\n            postcssWithPlugins.process(root || content, processOptions)\n          )\n      } catch (error: any) {\n        if (error.file) {\n          this.addDependency(error.file)\n        }\n\n        if (error.name === 'CssSyntaxError') {\n          throw new SyntaxError(error)\n        }\n\n        throw error\n      }\n\n      for (const warning of result.warnings()) {\n        this.emitWarning(new Warning(warning))\n      }\n\n      for (const message of result.messages) {\n        // eslint-disable-next-line default-case\n        switch (message.type) {\n          case 'dependency':\n            this.addDependency(message.file)\n            break\n          case 'build-dependency':\n            this.addBuildDependency(message.file)\n            break\n          case 'missing-dependency':\n            this.addMissingDependency(message.file)\n            break\n          case 'context-dependency':\n            this.addContextDependency(message.file)\n            break\n          case 'dir-dependency':\n            this.addContextDependency(message.dir)\n            break\n          case 'asset':\n            if (message.content && message.file) {\n              this.emitFile(\n                message.file,\n                message.content,\n                message.sourceMap,\n                message.info\n              )\n            }\n        }\n      }\n\n      // eslint-disable-next-line no-undefined\n      let map = result.map ? result.map.toJSON() : undefined\n\n      if (map && useSourceMap) {\n        map = normalizeSourceMapAfterPostcss(map, this.context)\n      }\n\n      const ast = {\n        type: 'postcss',\n        version: result.processor.version,\n        root: result.root,\n      }\n\n      return [result.css, map, { ast }]\n    })\n    .then(\n      ([css, map, { ast }]: any) => {\n        callback?.(null, css, map, { ast })\n      },\n      (err: Error) => {\n        callback?.(err)\n      }\n    )\n}\n"], "names": ["Warning", "SyntaxError", "normalizeSourceMap", "normalizeSourceMapAfterPostcss", "loader", "content", "sourceMap", "meta", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "callback", "async", "traceAsyncFn", "options", "getOptions", "file", "resourcePath", "useSourceMap", "processOptions", "from", "to", "map", "inline", "annotation", "prev", "traceFn", "context", "root", "ast", "type", "setAttribute", "postcssWithPlugins", "postcss", "result", "process", "error", "addDependency", "name", "warning", "warnings", "emitWarning", "message", "messages", "addBuildDependency", "addMissingDependency", "addContextDependency", "dir", "emitFile", "info", "toJSON", "undefined", "version", "processor", "css", "then", "err"], "mappings": "AAAA,OAAOA,aAAa,YAAW;AAC/B,OAAOC,iBAAiB,UAAS;AACjC,SAASC,kBAAkB,EAAEC,8BAA8B,QAAQ,UAAS;AAE5E;;;;CAIC,GACD,eAAe,eAAeC,OAE5B,WAAW,GACXC,OAAe,EACf,eAAe,GACfC,SAAc,EACdC,IAAS;IAET,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACpD,MAAMC,WAAW,IAAI,CAACC,KAAK;IAE3BJ,WACGK,YAAY,CAAC;QACZ,MAAMC,UAAU,IAAI,CAACC,UAAU;QAC/B,MAAMC,OAAO,IAAI,CAACC,YAAY;QAE9B,MAAMC,eACJ,OAAOJ,QAAQR,SAAS,KAAK,cACzBQ,QAAQR,SAAS,GACjB,IAAI,CAACA,SAAS;QAEpB,MAAMa,iBAAsB;YAC1BC,MAAMJ;YACNK,IAAIL;QACN;QAEA,IAAIE,cAAc;YAChBC,eAAeG,GAAG,GAAG;gBACnBC,QAAQ;gBACRC,YAAY;gBACZ,GAAGL,eAAeG,GAAG;YACvB;QACF;QAEA,IAAIhB,aAAaa,eAAeG,GAAG,EAAE;YACnCH,eAAeG,GAAG,CAACG,IAAI,GAAGjB,WACvBE,UAAU,CAAC,wBACXgB,OAAO,CAAC,IAAMxB,mBAAmBI,WAAW,IAAI,CAACqB,OAAO;QAC7D;QAEA,IAAIC;QAEJ,uCAAuC;QACvC,IAAIrB,QAAQA,KAAKsB,GAAG,IAAItB,KAAKsB,GAAG,CAACC,IAAI,KAAK,WAAW;;YACjD,CAAA,EAAEF,IAAI,EAAE,GAAGrB,KAAKsB,GAAG,AAAD;YACpBrB,WAAWuB,YAAY,CAAC,WAAW;QACrC;QAEA,mCAAmC;QACnC,MAAM,EAAEC,kBAAkB,EAAE,GAAG,MAAMlB,QAAQmB,OAAO;QAEpD,IAAIC;QAEJ,IAAI;YACFA,SAAS,MAAM1B,WACZE,UAAU,CAAC,mBACXG,YAAY,CAAC,IACZmB,mBAAmBG,OAAO,CAACP,QAAQvB,SAASc;QAElD,EAAE,OAAOiB,OAAY;YACnB,IAAIA,MAAMpB,IAAI,EAAE;gBACd,IAAI,CAACqB,aAAa,CAACD,MAAMpB,IAAI;YAC/B;YAEA,IAAIoB,MAAME,IAAI,KAAK,kBAAkB;gBACnC,MAAM,qBAAsB,CAAtB,IAAIrC,YAAYmC,QAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAqB;YAC7B;YAEA,MAAMA;QACR;QAEA,KAAK,MAAMG,WAAWL,OAAOM,QAAQ,GAAI;YACvC,IAAI,CAACC,WAAW,CAAC,qBAAoB,CAApB,IAAIzC,QAAQuC,UAAZ,qBAAA;uBAAA;4BAAA;8BAAA;YAAmB;QACtC;QAEA,KAAK,MAAMG,WAAWR,OAAOS,QAAQ,CAAE;YACrC,wCAAwC;YACxC,OAAQD,QAAQZ,IAAI;gBAClB,KAAK;oBACH,IAAI,CAACO,aAAa,CAACK,QAAQ1B,IAAI;oBAC/B;gBACF,KAAK;oBACH,IAAI,CAAC4B,kBAAkB,CAACF,QAAQ1B,IAAI;oBACpC;gBACF,KAAK;oBACH,IAAI,CAAC6B,oBAAoB,CAACH,QAAQ1B,IAAI;oBACtC;gBACF,KAAK;oBACH,IAAI,CAAC8B,oBAAoB,CAACJ,QAAQ1B,IAAI;oBACtC;gBACF,KAAK;oBACH,IAAI,CAAC8B,oBAAoB,CAACJ,QAAQK,GAAG;oBACrC;gBACF,KAAK;oBACH,IAAIL,QAAQrC,OAAO,IAAIqC,QAAQ1B,IAAI,EAAE;wBACnC,IAAI,CAACgC,QAAQ,CACXN,QAAQ1B,IAAI,EACZ0B,QAAQrC,OAAO,EACfqC,QAAQpC,SAAS,EACjBoC,QAAQO,IAAI;oBAEhB;YACJ;QACF;QAEA,wCAAwC;QACxC,IAAI3B,MAAMY,OAAOZ,GAAG,GAAGY,OAAOZ,GAAG,CAAC4B,MAAM,KAAKC;QAE7C,IAAI7B,OAAOJ,cAAc;YACvBI,MAAMnB,+BAA+BmB,KAAK,IAAI,CAACK,OAAO;QACxD;QAEA,MAAME,MAAM;YACVC,MAAM;YACNsB,SAASlB,OAAOmB,SAAS,CAACD,OAAO;YACjCxB,MAAMM,OAAON,IAAI;QACnB;QAEA,OAAO;YAACM,OAAOoB,GAAG;YAAEhC;YAAK;gBAAEO;YAAI;SAAE;IACnC,GACC0B,IAAI,CACH,CAAC,CAACD,KAAKhC,KAAK,EAAEO,GAAG,EAAE,CAAM;QACvBlB,4BAAAA,SAAW,MAAM2C,KAAKhC,KAAK;YAAEO;QAAI;IACnC,GACA,CAAC2B;QACC7C,4BAAAA,SAAW6C;IACb;AAEN"}