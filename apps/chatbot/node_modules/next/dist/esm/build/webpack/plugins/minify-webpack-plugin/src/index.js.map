{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/minify-webpack-plugin/src/index.ts"], "sourcesContent": ["import {\n  webpack,\n  ModuleFilenameHelpers,\n  sources,\n  WebpackError,\n  type CacheFacade,\n  type Compilation,\n} from 'next/dist/compiled/webpack/webpack'\nimport pLimit from 'next/dist/compiled/p-limit'\nimport { getCompilationSpan } from '../../../utils'\n\nfunction buildError(error: any, file: string) {\n  if (error.line) {\n    return new WebpackError(\n      `${file} from Minifier\\n${error.message} [${file}:${error.line},${\n        error.col\n      }]${\n        error.stack ? `\\n${error.stack.split('\\n').slice(1).join('\\n')}` : ''\n      }`\n    )\n  }\n\n  if (error.stack) {\n    return new WebpackError(\n      `${file} from Minifier\\n${error.message}\\n${error.stack}`\n    )\n  }\n\n  return new WebpackError(`${file} from Minifier\\n${error.message}`)\n}\n\nconst debugMinify = process.env.NEXT_DEBUG_MINIFY\n\nexport class MinifyPlugin {\n  constructor(private options: { noMangling?: boolean }) {}\n\n  async optimize(\n    compiler: any,\n    compilation: Compilation,\n    assets: any,\n    cache: CacheFacade,\n    {\n      SourceMapSource,\n      RawSource,\n    }: {\n      SourceMapSource: typeof sources.SourceMapSource\n      RawSource: typeof sources.RawSource\n    }\n  ) {\n    const mangle = !this.options.noMangling\n    const compilationSpan =\n      getCompilationSpan(compilation)! || getCompilationSpan(compiler)\n\n    const MinifierSpan = compilationSpan.traceChild(\n      'minify-webpack-plugin-optimize'\n    )\n\n    if (compilation.name) {\n      MinifierSpan.setAttribute('compilationName', compilation.name)\n    }\n\n    MinifierSpan.setAttribute('mangle', String(mangle))\n\n    return MinifierSpan.traceAsyncFn(async () => {\n      const assetsList = Object.keys(assets)\n\n      const assetsForMinify = await Promise.all(\n        assetsList\n          .filter((name) => {\n            if (\n              !ModuleFilenameHelpers.matchObject.bind(\n                // eslint-disable-next-line no-undefined\n                undefined,\n                { test: /\\.[cm]?js(\\?.*)?$/i }\n              )(name)\n            ) {\n              return false\n            }\n\n            const res = compilation.getAsset(name)\n            if (!res) {\n              console.log(name)\n              return false\n            }\n\n            const { info } = res\n\n            // Skip double minimize assets from child compilation\n            if (info.minimized) {\n              return false\n            }\n\n            return true\n          })\n          .map(async (name) => {\n            const { info, source } = compilation.getAsset(name)!\n\n            const eTag = cache.mergeEtags(\n              cache.getLazyHashedEtag(source),\n              JSON.stringify(this.options)\n            )\n\n            const output = await cache.getPromise<\n              { source: sources.Source } | undefined\n            >(name, eTag)\n\n            if (debugMinify && debugMinify === '1') {\n              console.log(\n                JSON.stringify({ name, source: source.source().toString() }),\n                { breakLength: Infinity, maxStringLength: Infinity }\n              )\n            }\n            return { name, info, inputSource: source, output, eTag }\n          })\n      )\n\n      let initializedWorker: any\n\n      // eslint-disable-next-line consistent-return\n      const getWorker = () => {\n        return {\n          minify: async (options: {\n            input: string\n            inputSourceMap: Object\n          }) => {\n            const result = await (\n              require('../../../../swc') as typeof import('../../../../swc')\n            ).minify(options.input, {\n              ...(options.inputSourceMap\n                ? {\n                    sourceMap: {\n                      content: JSON.stringify(options.inputSourceMap),\n                    },\n                  }\n                : {}),\n              // Compress options are defined in crates/napi/src/minify.rs.\n              compress: false,\n              // Mangle options may be amended in crates/napi/src/minify.rs.\n              mangle,\n              module: 'unknown',\n              output: {\n                comments: false,\n              },\n            })\n\n            return result\n          },\n        }\n      }\n\n      // The limit in the SWC minifier will be handled by Node.js\n      const limit = pLimit(Infinity)\n      const scheduledTasks = []\n\n      for (const asset of assetsForMinify) {\n        scheduledTasks.push(\n          limit(async () => {\n            const { name, inputSource, eTag } = asset\n            let { output } = asset\n\n            const minifySpan = MinifierSpan.traceChild('minify-js')\n            minifySpan.setAttribute('name', name)\n            minifySpan.setAttribute(\n              'cache',\n              typeof output === 'undefined' ? 'MISS' : 'HIT'\n            )\n\n            return minifySpan.traceAsyncFn(async () => {\n              if (!output) {\n                const { source: sourceFromInputSource, map: inputSourceMap } =\n                  inputSource.sourceAndMap()\n\n                const input = Buffer.isBuffer(sourceFromInputSource)\n                  ? sourceFromInputSource.toString()\n                  : sourceFromInputSource\n\n                let minifiedOutput: { code: string; map: any } | undefined\n\n                try {\n                  minifiedOutput = await getWorker().minify({\n                    input,\n                    inputSourceMap,\n                  })\n                } catch (error) {\n                  compilation.errors.push(buildError(error, name))\n\n                  return\n                }\n\n                const source = minifiedOutput.map\n                  ? new SourceMapSource(\n                      minifiedOutput.code,\n                      name,\n                      minifiedOutput.map,\n                      input,\n                      inputSourceMap,\n                      true\n                    )\n                  : new RawSource(minifiedOutput.code)\n\n                await cache.storePromise(name, eTag, { source })\n\n                output = { source }\n              }\n\n              const newInfo = { minimized: true }\n\n              compilation.updateAsset(name, output.source, newInfo)\n            })\n          })\n        )\n      }\n\n      await Promise.all(scheduledTasks)\n\n      if (initializedWorker) {\n        await initializedWorker.end()\n      }\n    })\n  }\n\n  apply(compiler: any) {\n    const { SourceMapSource, RawSource } = (compiler?.webpack?.sources ||\n      sources) as typeof sources\n\n    const pluginName = this.constructor.name\n\n    compiler.hooks.thisCompilation.tap(\n      pluginName,\n      (compilation: Compilation) => {\n        const cache = compilation.getCache('MinifierWebpackPlugin')\n\n        const handleHashForChunk = (hash: any, _chunk: any) => {\n          // increment 'c' to invalidate cache\n          hash.update('c')\n        }\n\n        const JSModulesHooks =\n          webpack.javascript.JavascriptModulesPlugin.getCompilationHooks(\n            compilation\n          )\n        JSModulesHooks.chunkHash.tap(pluginName, (chunk, hash) => {\n          if (!chunk.hasRuntime()) return\n          return handleHashForChunk(hash, chunk)\n        })\n\n        compilation.hooks.processAssets.tapPromise(\n          {\n            name: pluginName,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE,\n          },\n          (assets: any) =>\n            this.optimize(compiler, compilation, assets, cache, {\n              SourceMapSource,\n              RawSource,\n            })\n        )\n\n        compilation.hooks.statsPrinter.tap(pluginName, (stats: any) => {\n          stats.hooks.print\n            .for('asset.info.minimized')\n            .tap(\n              'minify-webpack-plugin',\n              (minimized: any, { green, formatFlag }: any) =>\n                // eslint-disable-next-line no-undefined\n                minimized ? green(formatFlag('minimized')) : undefined\n            )\n        })\n      }\n    )\n  }\n}\n"], "names": ["webpack", "ModuleFilenameHelpers", "sources", "WebpackError", "pLimit", "getCompilationSpan", "buildError", "error", "file", "line", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "MinifyPlugin", "constructor", "options", "optimize", "compiler", "compilation", "assets", "cache", "SourceMapSource", "RawSource", "mangle", "noMangling", "compilationSpan", "MinifierSpan", "<PERSON><PERSON><PERSON><PERSON>", "name", "setAttribute", "String", "traceAsyncFn", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "info", "minimized", "map", "source", "eTag", "mergeEtags", "getLazyHashedEtag", "JSON", "stringify", "output", "getPromise", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "compress", "module", "comments", "limit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "minifiedOutput", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": "AAAA,SACEA,OAAO,EACPC,qBAAqB,EACrBC,OAAO,EACPC,YAAY,QAGP,qCAAoC;AAC3C,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,kBAAkB,QAAQ,iBAAgB;AAEnD,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIN,aACT,GAAGK,KAAK,gBAAgB,EAAED,MAAMG,OAAO,CAAC,EAAE,EAAEF,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC9DF,MAAMI,GAAG,CACV,CAAC,EACAJ,MAAMK,KAAK,GAAG,CAAC,EAAE,EAAEL,MAAMK,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAO,GAAG,IACnE;IAEN;IAEA,IAAIR,MAAMK,KAAK,EAAE;QACf,OAAO,IAAIT,aACT,GAAGK,KAAK,gBAAgB,EAAED,MAAMG,OAAO,CAAC,EAAE,EAAEH,MAAMK,KAAK,EAAE;IAE7D;IAEA,OAAO,IAAIT,aAAa,GAAGK,KAAK,gBAAgB,EAAED,MAAMG,OAAO,EAAE;AACnE;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAEjD,OAAO,MAAMC;IACXC,YAAY,AAAQC,OAAiC,CAAE;aAAnCA,UAAAA;IAAoC;IAExD,MAAMC,SACJC,QAAa,EACbC,WAAwB,EACxBC,MAAW,EACXC,KAAkB,EAClB,EACEC,eAAe,EACfC,SAAS,EAIV,EACD;QACA,MAAMC,SAAS,CAAC,IAAI,CAACR,OAAO,CAACS,UAAU;QACvC,MAAMC,kBACJ3B,mBAAmBoB,gBAAiBpB,mBAAmBmB;QAEzD,MAAMS,eAAeD,gBAAgBE,UAAU,CAC7C;QAGF,IAAIT,YAAYU,IAAI,EAAE;YACpBF,aAAaG,YAAY,CAAC,mBAAmBX,YAAYU,IAAI;QAC/D;QAEAF,aAAaG,YAAY,CAAC,UAAUC,OAAOP;QAE3C,OAAOG,aAAaK,YAAY,CAAC;YAC/B,MAAMC,aAAaC,OAAOC,IAAI,CAACf;YAE/B,MAAMgB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACV;gBACP,IACE,CAAClC,sBAAsB6C,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bd,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMe,MAAMzB,YAAY0B,QAAQ,CAAChB;gBACjC,IAAI,CAACe,KAAK;oBACRE,QAAQC,GAAG,CAAClB;oBACZ,OAAO;gBACT;gBAEA,MAAM,EAAEmB,IAAI,EAAE,GAAGJ;gBAEjB,qDAAqD;gBACrD,IAAII,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOrB;gBACV,MAAM,EAAEmB,IAAI,EAAEG,MAAM,EAAE,GAAGhC,YAAY0B,QAAQ,CAAChB;gBAE9C,MAAMuB,OAAO/B,MAAMgC,UAAU,CAC3BhC,MAAMiC,iBAAiB,CAACH,SACxBI,KAAKC,SAAS,CAAC,IAAI,CAACxC,OAAO;gBAG7B,MAAMyC,SAAS,MAAMpC,MAAMqC,UAAU,CAEnC7B,MAAMuB;gBAER,IAAI1C,eAAeA,gBAAgB,KAAK;oBACtCoC,QAAQC,GAAG,CACTQ,KAAKC,SAAS,CAAC;wBAAE3B;wBAAMsB,QAAQA,OAAOA,MAAM,GAAGQ,QAAQ;oBAAG,IAC1D;wBAAEC,aAAaC;wBAAUC,iBAAiBD;oBAAS;gBAEvD;gBACA,OAAO;oBAAEhC;oBAAMmB;oBAAMe,aAAaZ;oBAAQM;oBAAQL;gBAAK;YACzD;YAGJ,IAAIY;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,OAAO;oBACLC,QAAQ,OAAOlD;wBAIb,MAAMmD,SAAS,MAAM,AACnBC,QAAQ,mBACRF,MAAM,CAAClD,QAAQqD,KAAK,EAAE;4BACtB,GAAIrD,QAAQsD,cAAc,GACtB;gCACEC,WAAW;oCACTC,SAASjB,KAAKC,SAAS,CAACxC,QAAQsD,cAAc;gCAChD;4BACF,IACA,CAAC,CAAC;4BACN,6DAA6D;4BAC7DG,UAAU;4BACV,8DAA8D;4BAC9DjD;4BACAkD,QAAQ;4BACRjB,QAAQ;gCACNkB,UAAU;4BACZ;wBACF;wBAEA,OAAOR;oBACT;gBACF;YACF;YAEA,2DAA2D;YAC3D,MAAMS,QAAQ9E,OAAO+D;YACrB,MAAMgB,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAAS1C,gBAAiB;gBACnCyC,eAAeE,IAAI,CACjBH,MAAM;oBACJ,MAAM,EAAE/C,IAAI,EAAEkC,WAAW,EAAEX,IAAI,EAAE,GAAG0B;oBACpC,IAAI,EAAErB,MAAM,EAAE,GAAGqB;oBAEjB,MAAME,aAAarD,aAAaC,UAAU,CAAC;oBAC3CoD,WAAWlD,YAAY,CAAC,QAAQD;oBAChCmD,WAAWlD,YAAY,CACrB,SACA,OAAO2B,WAAW,cAAc,SAAS;oBAG3C,OAAOuB,WAAWhD,YAAY,CAAC;wBAC7B,IAAI,CAACyB,QAAQ;4BACX,MAAM,EAAEN,QAAQ8B,qBAAqB,EAAE/B,KAAKoB,cAAc,EAAE,GAC1DP,YAAYmB,YAAY;4BAE1B,MAAMb,QAAQc,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsBtB,QAAQ,KAC9BsB;4BAEJ,IAAII;4BAEJ,IAAI;gCACFA,iBAAiB,MAAMpB,YAAYC,MAAM,CAAC;oCACxCG;oCACAC;gCACF;4BACF,EAAE,OAAOrE,OAAO;gCACdkB,YAAYmE,MAAM,CAACP,IAAI,CAAC/E,WAAWC,OAAO4B;gCAE1C;4BACF;4BAEA,MAAMsB,SAASkC,eAAenC,GAAG,GAC7B,IAAI5B,gBACF+D,eAAeE,IAAI,EACnB1D,MACAwD,eAAenC,GAAG,EAClBmB,OACAC,gBACA,QAEF,IAAI/C,UAAU8D,eAAeE,IAAI;4BAErC,MAAMlE,MAAMmE,YAAY,CAAC3D,MAAMuB,MAAM;gCAAED;4BAAO;4BAE9CM,SAAS;gCAAEN;4BAAO;wBACpB;wBAEA,MAAMsC,UAAU;4BAAExC,WAAW;wBAAK;wBAElC9B,YAAYuE,WAAW,CAAC7D,MAAM4B,OAAON,MAAM,EAAEsC;oBAC/C;gBACF;YAEJ;YAEA,MAAMpD,QAAQC,GAAG,CAACuC;YAElB,IAAIb,mBAAmB;gBACrB,MAAMA,kBAAkB2B,GAAG;YAC7B;QACF;IACF;IAEAC,MAAM1E,QAAa,EAAE;YACqBA;QAAxC,MAAM,EAAEI,eAAe,EAAEC,SAAS,EAAE,GAAIL,CAAAA,6BAAAA,oBAAAA,SAAUxB,OAAO,qBAAjBwB,kBAAmBtB,OAAO,KAChEA;QAEF,MAAMiG,aAAa,IAAI,CAAC9E,WAAW,CAACc,IAAI;QAExCX,SAAS4E,KAAK,CAACC,eAAe,CAACC,GAAG,CAChCH,YACA,CAAC1E;YACC,MAAME,QAAQF,YAAY8E,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJ5G,QAAQ6G,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5DtF;YAEJmF,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEAxF,YAAY2E,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACEjF,MAAMgE;gBACNkB,OAAOrH,QAAQsH,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAAC7F,SACC,IAAI,CAACH,QAAQ,CAACC,UAAUC,aAAaC,QAAQC,OAAO;oBAClDC;oBACAC;gBACF;YAGJJ,YAAY2E,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAAC/C,WAAgB,EAAEqE,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxCtE,YAAYqE,MAAMC,WAAW,gBAAgB7E;YAErD;QACF;IAEJ;AACF"}