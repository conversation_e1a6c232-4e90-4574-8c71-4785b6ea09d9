{"version": 3, "sources": ["../../../../src/build/webpack/loaders/utils.ts"], "sourcesContent": ["import type webpack from 'webpack'\nimport { RSC_MODULE_TYPES } from '../../../shared/lib/constants'\nimport { getModuleBuildInfo } from './get-module-build-info'\n\nconst imageExtensions = ['jpg', 'jpeg', 'png', 'webp', 'avif', 'ico', 'svg']\nconst imageRegex = new RegExp(`\\\\.(${imageExtensions.join('|')})$`)\n\n// Determine if the whole module is client action, 'use server' in nested closure in the client module\nfunction isActionClientLayerModule(mod: webpack.NormalModule) {\n  const rscInfo = getModuleBuildInfo(mod).rsc\n  return !!(rscInfo?.actionIds && rscInfo?.type === RSC_MODULE_TYPES.client)\n}\n\nexport function isClientComponentEntryModule(mod: webpack.NormalModule) {\n  const rscInfo = getModuleBuildInfo(mod).rsc\n  const hasClientDirective = rscInfo?.isClientRef\n  const isActionLayerEntry = isActionClientLayerModule(mod)\n  return (\n    hasClientDirective || isActionLayerEntry || imageRegex.test(mod.resource)\n  )\n}\n\nexport const regexCSS = /\\.(css|scss|sass)(\\?.*)?$/\n\n// This function checks if a module is able to emit CSS resources. You should\n// never only rely on a single regex to do that.\nexport function isCSSMod(mod: {\n  resource: string\n  type?: string\n  loaders?: { loader: string }[]\n}): boolean {\n  return !!(\n    mod.type === 'css/mini-extract' ||\n    (mod.resource && regexCSS.test(mod.resource)) ||\n    mod.loaders?.some(\n      ({ loader }) =>\n        loader.includes('next-style-loader/index.js') ||\n        (process.env.NEXT_RSPACK &&\n          loader.includes('rspack.CssExtractRspackPlugin.loader')) ||\n        loader.includes('mini-css-extract-plugin/loader.js') ||\n        loader.includes('@vanilla-extract/webpack-plugin/loader/')\n    )\n  )\n}\n\nexport function encodeToBase64<T extends object>(obj: T): string {\n  return Buffer.from(JSON.stringify(obj)).toString('base64')\n}\n\nexport function decodeFromBase64<T extends object>(str: string): T {\n  return JSON.parse(Buffer.from(str, 'base64').toString('utf8'))\n}\n\nexport async function getLoaderModuleNamedExports(\n  resourcePath: string,\n  context: webpack.LoaderContext<any>\n): Promise<string[]> {\n  const mod = await new Promise<webpack.NormalModule>((res, rej) => {\n    context.loadModule(\n      resourcePath,\n      (err: null | Error, _source: any, _sourceMap: any, module: any) => {\n        if (err) {\n          return rej(err)\n        }\n        res(module)\n      }\n    )\n  })\n\n  const exportNames =\n    mod.dependencies\n      ?.filter((dep) => {\n        return (\n          [\n            'HarmonyExportImportedSpecifierDependency',\n            'HarmonyExportSpecifierDependency',\n          ].includes(dep.constructor.name) &&\n          'name' in dep &&\n          dep.name !== 'default'\n        )\n      })\n      .map((dep: any) => {\n        return dep.name\n      }) || []\n  return exportNames\n}\n"], "names": ["RSC_MODULE_TYPES", "getModuleBuildInfo", "imageExtensions", "imageRegex", "RegExp", "join", "isActionClientLayerModule", "mod", "rscInfo", "rsc", "actionIds", "type", "client", "isClientComponentEntryModule", "hasClientDirective", "isClientRef", "isActionLayerEntry", "test", "resource", "regexCSS", "isCSSMod", "loaders", "some", "loader", "includes", "process", "env", "NEXT_RSPACK", "encodeToBase64", "obj", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "decodeFromBase64", "str", "parse", "getLoaderModuleNamedExports", "resourcePath", "context", "Promise", "res", "rej", "loadModule", "err", "_source", "_sourceMap", "module", "exportNames", "dependencies", "filter", "dep", "constructor", "name", "map"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,kBAAkB,QAAQ,0BAAyB;AAE5D,MAAMC,kBAAkB;IAAC;IAAO;IAAQ;IAAO;IAAQ;IAAQ;IAAO;CAAM;AAC5E,MAAMC,aAAa,IAAIC,OAAO,CAAC,IAAI,EAAEF,gBAAgBG,IAAI,CAAC,KAAK,EAAE,CAAC;AAElE,sGAAsG;AACtG,SAASC,0BAA0BC,GAAyB;IAC1D,MAAMC,UAAUP,mBAAmBM,KAAKE,GAAG;IAC3C,OAAO,CAAC,CAAED,CAAAA,CAAAA,2BAAAA,QAASE,SAAS,KAAIF,CAAAA,2BAAAA,QAASG,IAAI,MAAKX,iBAAiBY,MAAM,AAAD;AAC1E;AAEA,OAAO,SAASC,6BAA6BN,GAAyB;IACpE,MAAMC,UAAUP,mBAAmBM,KAAKE,GAAG;IAC3C,MAAMK,qBAAqBN,2BAAAA,QAASO,WAAW;IAC/C,MAAMC,qBAAqBV,0BAA0BC;IACrD,OACEO,sBAAsBE,sBAAsBb,WAAWc,IAAI,CAACV,IAAIW,QAAQ;AAE5E;AAEA,OAAO,MAAMC,WAAW,4BAA2B;AAEnD,6EAA6E;AAC7E,gDAAgD;AAChD,OAAO,SAASC,SAASb,GAIxB;QAIGA;IAHF,OAAO,CAAC,CACNA,CAAAA,IAAII,IAAI,KAAK,sBACZJ,IAAIW,QAAQ,IAAIC,SAASF,IAAI,CAACV,IAAIW,QAAQ,OAC3CX,eAAAA,IAAIc,OAAO,qBAAXd,aAAae,IAAI,CACf,CAAC,EAAEC,MAAM,EAAE,GACTA,OAAOC,QAAQ,CAAC,iCACfC,QAAQC,GAAG,CAACC,WAAW,IACtBJ,OAAOC,QAAQ,CAAC,2CAClBD,OAAOC,QAAQ,CAAC,wCAChBD,OAAOC,QAAQ,CAAC,4CACpB;AAEJ;AAEA,OAAO,SAASI,eAAiCC,GAAM;IACrD,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,MAAMK,QAAQ,CAAC;AACnD;AAEA,OAAO,SAASC,iBAAmCC,GAAW;IAC5D,OAAOJ,KAAKK,KAAK,CAACP,OAAOC,IAAI,CAACK,KAAK,UAAUF,QAAQ,CAAC;AACxD;AAEA,OAAO,eAAeI,4BACpBC,YAAoB,EACpBC,OAAmC;QAejCjC;IAbF,MAAMA,MAAM,MAAM,IAAIkC,QAA8B,CAACC,KAAKC;QACxDH,QAAQI,UAAU,CAChBL,cACA,CAACM,KAAmBC,SAAcC,YAAiBC;YACjD,IAAIH,KAAK;gBACP,OAAOF,IAAIE;YACb;YACAH,IAAIM;QACN;IAEJ;IAEA,MAAMC,cACJ1C,EAAAA,oBAAAA,IAAI2C,YAAY,qBAAhB3C,kBACI4C,MAAM,CAAC,CAACC;QACR,OACE;YACE;YACA;SACD,CAAC5B,QAAQ,CAAC4B,IAAIC,WAAW,CAACC,IAAI,KAC/B,UAAUF,OACVA,IAAIE,IAAI,KAAK;IAEjB,GACCC,GAAG,CAAC,CAACH;QACJ,OAAOA,IAAIE,IAAI;IACjB,OAAM,EAAE;IACZ,OAAOL;AACT"}