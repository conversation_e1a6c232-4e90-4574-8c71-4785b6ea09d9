{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-font-loader/postcss-next-font.ts"], "sourcesContent": ["import type { AdjustFontFallback } from '../../../../../font'\nimport type { Declaration } from 'postcss'\nimport postcss from 'postcss'\n\n/**\n * The next/font postcss plugin recieves the @font-face declarations returned from the next/font loaders.\n *\n * It hashes the font-family name to make it unguessable, it shouldn't be globally accessible.\n * If it were global, we wouldn't be able to tell which pages are using which fonts when generating preload tags.\n *\n * If the font loader returned fallback metrics, generate a fallback @font-face.\n *\n * If the font loader returned a variable name, add a CSS class that declares a variable containing the font and fallback fonts.\n *\n * Lastly, it adds the font-family to the exports object.\n * This enables you to access the actual font-family name, not just through the CSS class.\n * e.g:\n * const inter = Inter({ subsets: ['latin'] })\n * inter.style.fontFamily // => '__Inter_123456'\n */\nconst postcssNextFontPlugin = ({\n  exports,\n  fallbackFonts = [],\n  adjustFontFallback,\n  variable,\n  weight,\n  style,\n}: {\n  exports: { name: any; value: any }[]\n  fallbackFonts?: string[]\n  adjustFontFallback?: AdjustFontFallback\n  variable?: string\n  weight?: string\n  style?: string\n}) => {\n  return {\n    postcssPlugin: 'postcss-next-font',\n    Once(root: any) {\n      let fontFamily: string | undefined\n\n      const normalizeFamily = (family: string) => {\n        return family.replace(/['\"]/g, '')\n      }\n\n      const formatFamily = (family: string) => {\n        return `'${family}'`\n      }\n\n      // Hash font-family names\n      for (const node of root.nodes) {\n        if (node.type === 'atrule' && node.name === 'font-face') {\n          const familyNode = node.nodes.find(\n            (decl: Declaration) => decl.prop === 'font-family'\n          )\n          if (!familyNode) {\n            continue\n          }\n\n          if (!fontFamily) {\n            fontFamily = normalizeFamily(familyNode.value)\n          }\n\n          familyNode.value = formatFamily(fontFamily)\n        }\n      }\n\n      if (!fontFamily) {\n        throw new Error(\"Font loaders must return one or more @font-face's\")\n      }\n\n      // Add fallback @font-face with the provided override values\n      let adjustFontFallbackFamily: string | undefined\n      if (adjustFontFallback) {\n        adjustFontFallbackFamily = formatFamily(`${fontFamily} Fallback`)\n        const fallbackFontFace = postcss.atRule({ name: 'font-face' })\n        const {\n          fallbackFont,\n          ascentOverride,\n          descentOverride,\n          lineGapOverride,\n          sizeAdjust,\n        } = adjustFontFallback\n        fallbackFontFace.nodes = [\n          new postcss.Declaration({\n            prop: 'font-family',\n            value: adjustFontFallbackFamily,\n          }),\n          new postcss.Declaration({\n            prop: 'src',\n            value: `local(\"${fallbackFont}\")`,\n          }),\n          ...(ascentOverride\n            ? [\n                new postcss.Declaration({\n                  prop: 'ascent-override',\n                  value: ascentOverride,\n                }),\n              ]\n            : []),\n          ...(descentOverride\n            ? [\n                new postcss.Declaration({\n                  prop: 'descent-override',\n                  value: descentOverride,\n                }),\n              ]\n            : []),\n          ...(lineGapOverride\n            ? [\n                new postcss.Declaration({\n                  prop: 'line-gap-override',\n                  value: lineGapOverride,\n                }),\n              ]\n            : []),\n          ...(sizeAdjust\n            ? [\n                new postcss.Declaration({\n                  prop: 'size-adjust',\n                  value: sizeAdjust,\n                }),\n              ]\n            : []),\n        ]\n        root.nodes.push(fallbackFontFace)\n      }\n\n      // Variable fonts can define ranges of values\n      const isRange = (value: string) => value.trim().includes(' ')\n\n      // Format the font families to be used in the CSS\n      const formattedFontFamilies = [\n        formatFamily(fontFamily),\n        ...(adjustFontFallbackFamily ? [adjustFontFallbackFamily] : []),\n        ...fallbackFonts,\n      ].join(', ')\n\n      // Add class with family, weight and style\n      const classRule = new postcss.Rule({ selector: '.className' })\n      classRule.nodes = [\n        new postcss.Declaration({\n          prop: 'font-family',\n          value: formattedFontFamilies,\n        }),\n        // If the font only has one weight or style, we can set it on the class\n        ...(weight && !isRange(weight)\n          ? [\n              new postcss.Declaration({\n                prop: 'font-weight',\n                value: weight,\n              }),\n            ]\n          : []),\n        ...(style && !isRange(style)\n          ? [\n              new postcss.Declaration({\n                prop: 'font-style',\n                value: style,\n              }),\n            ]\n          : []),\n      ]\n      root.nodes.push(classRule)\n\n      // Add CSS class that defines a variable with the font families\n      if (variable) {\n        const varialbeRule = new postcss.Rule({ selector: '.variable' })\n        varialbeRule.nodes = [\n          new postcss.Declaration({\n            prop: variable,\n            value: formattedFontFamilies,\n          }),\n        ]\n        root.nodes.push(varialbeRule)\n      }\n\n      // Export @font-face values as is\n      exports.push({\n        name: 'style',\n        value: {\n          fontFamily: formattedFontFamilies,\n          fontWeight: !Number.isNaN(Number(weight))\n            ? Number(weight)\n            : undefined,\n          fontStyle: style && !isRange(style) ? style : undefined,\n        },\n      })\n    },\n  }\n}\n\npostcssNextFontPlugin.postcss = true\n\nexport default postcssNextFontPlugin\n"], "names": ["postcss", "postcssNextFontPlugin", "exports", "fallbackFonts", "adjustFontFallback", "variable", "weight", "style", "postcssPlugin", "Once", "root", "fontFamily", "normalizeFamily", "family", "replace", "formatFamily", "node", "nodes", "type", "name", "familyNode", "find", "decl", "prop", "value", "Error", "adjustFontFallbackFamily", "fallbackFontFace", "atRule", "fallbackFont", "ascentOverride", "descentOverride", "lineGapOverride", "sizeAdjust", "Declaration", "push", "isRange", "trim", "includes", "formattedFontFamilies", "join", "classRule", "Rule", "selector", "varialbeRule", "fontWeight", "Number", "isNaN", "undefined", "fontStyle"], "mappings": "AAEA,OAAOA,aAAa,UAAS;AAE7B;;;;;;;;;;;;;;;CAeC,GACD,MAAMC,wBAAwB,CAAC,EAC7BC,OAAO,EACPC,gBAAgB,EAAE,EAClBC,kBAAkB,EAClBC,QAAQ,EACRC,MAAM,EACNC,KAAK,EAQN;IACC,OAAO;QACLC,eAAe;QACfC,MAAKC,IAAS;YACZ,IAAIC;YAEJ,MAAMC,kBAAkB,CAACC;gBACvB,OAAOA,OAAOC,OAAO,CAAC,SAAS;YACjC;YAEA,MAAMC,eAAe,CAACF;gBACpB,OAAO,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC;YACtB;YAEA,yBAAyB;YACzB,KAAK,MAAMG,QAAQN,KAAKO,KAAK,CAAE;gBAC7B,IAAID,KAAKE,IAAI,KAAK,YAAYF,KAAKG,IAAI,KAAK,aAAa;oBACvD,MAAMC,aAAaJ,KAAKC,KAAK,CAACI,IAAI,CAChC,CAACC,OAAsBA,KAAKC,IAAI,KAAK;oBAEvC,IAAI,CAACH,YAAY;wBACf;oBACF;oBAEA,IAAI,CAACT,YAAY;wBACfA,aAAaC,gBAAgBQ,WAAWI,KAAK;oBAC/C;oBAEAJ,WAAWI,KAAK,GAAGT,aAAaJ;gBAClC;YACF;YAEA,IAAI,CAACA,YAAY;gBACf,MAAM,qBAA8D,CAA9D,IAAIc,MAAM,sDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;YAEA,4DAA4D;YAC5D,IAAIC;YACJ,IAAItB,oBAAoB;gBACtBsB,2BAA2BX,aAAa,GAAGJ,WAAW,SAAS,CAAC;gBAChE,MAAMgB,mBAAmB3B,QAAQ4B,MAAM,CAAC;oBAAET,MAAM;gBAAY;gBAC5D,MAAM,EACJU,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,UAAU,EACX,GAAG7B;gBACJuB,iBAAiBV,KAAK,GAAG;oBACvB,IAAIjB,QAAQkC,WAAW,CAAC;wBACtBX,MAAM;wBACNC,OAAOE;oBACT;oBACA,IAAI1B,QAAQkC,WAAW,CAAC;wBACtBX,MAAM;wBACNC,OAAO,CAAC,OAAO,EAAEK,aAAa,EAAE,CAAC;oBACnC;uBACIC,iBACA;wBACE,IAAI9B,QAAQkC,WAAW,CAAC;4BACtBX,MAAM;4BACNC,OAAOM;wBACT;qBACD,GACD,EAAE;uBACFC,kBACA;wBACE,IAAI/B,QAAQkC,WAAW,CAAC;4BACtBX,MAAM;4BACNC,OAAOO;wBACT;qBACD,GACD,EAAE;uBACFC,kBACA;wBACE,IAAIhC,QAAQkC,WAAW,CAAC;4BACtBX,MAAM;4BACNC,OAAOQ;wBACT;qBACD,GACD,EAAE;uBACFC,aACA;wBACE,IAAIjC,QAAQkC,WAAW,CAAC;4BACtBX,MAAM;4BACNC,OAAOS;wBACT;qBACD,GACD,EAAE;iBACP;gBACDvB,KAAKO,KAAK,CAACkB,IAAI,CAACR;YAClB;YAEA,6CAA6C;YAC7C,MAAMS,UAAU,CAACZ,QAAkBA,MAAMa,IAAI,GAAGC,QAAQ,CAAC;YAEzD,iDAAiD;YACjD,MAAMC,wBAAwB;gBAC5BxB,aAAaJ;mBACTe,2BAA2B;oBAACA;iBAAyB,GAAG,EAAE;mBAC3DvB;aACJ,CAACqC,IAAI,CAAC;YAEP,0CAA0C;YAC1C,MAAMC,YAAY,IAAIzC,QAAQ0C,IAAI,CAAC;gBAAEC,UAAU;YAAa;YAC5DF,UAAUxB,KAAK,GAAG;gBAChB,IAAIjB,QAAQkC,WAAW,CAAC;oBACtBX,MAAM;oBACNC,OAAOe;gBACT;gBACA,uEAAuE;mBACnEjC,UAAU,CAAC8B,QAAQ9B,UACnB;oBACE,IAAIN,QAAQkC,WAAW,CAAC;wBACtBX,MAAM;wBACNC,OAAOlB;oBACT;iBACD,GACD,EAAE;mBACFC,SAAS,CAAC6B,QAAQ7B,SAClB;oBACE,IAAIP,QAAQkC,WAAW,CAAC;wBACtBX,MAAM;wBACNC,OAAOjB;oBACT;iBACD,GACD,EAAE;aACP;YACDG,KAAKO,KAAK,CAACkB,IAAI,CAACM;YAEhB,+DAA+D;YAC/D,IAAIpC,UAAU;gBACZ,MAAMuC,eAAe,IAAI5C,QAAQ0C,IAAI,CAAC;oBAAEC,UAAU;gBAAY;gBAC9DC,aAAa3B,KAAK,GAAG;oBACnB,IAAIjB,QAAQkC,WAAW,CAAC;wBACtBX,MAAMlB;wBACNmB,OAAOe;oBACT;iBACD;gBACD7B,KAAKO,KAAK,CAACkB,IAAI,CAACS;YAClB;YAEA,iCAAiC;YACjC1C,QAAQiC,IAAI,CAAC;gBACXhB,MAAM;gBACNK,OAAO;oBACLb,YAAY4B;oBACZM,YAAY,CAACC,OAAOC,KAAK,CAACD,OAAOxC,WAC7BwC,OAAOxC,UACP0C;oBACJC,WAAW1C,SAAS,CAAC6B,QAAQ7B,SAASA,QAAQyC;gBAChD;YACF;QACF;IACF;AACF;AAEA/C,sBAAsBD,OAAO,GAAG;AAEhC,eAAeC,sBAAqB"}