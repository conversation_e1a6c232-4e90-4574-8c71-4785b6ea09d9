{"version": 3, "sources": ["../../src/build/webpack-config.ts"], "sourcesContent": ["import React from 'react'\nimport ReactRefreshWebpackPlugin from 'next/dist/compiled/@next/react-refresh-utils/dist/ReactRefreshWebpackPlugin'\nimport { yellow, bold } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES } from '../lib/constants'\nimport type { WebpackLayerName } from '../lib/constants'\nimport {\n  isWebpackBundledLayer,\n  isWebpackClientOnlyLayer,\n  isWebpackDefaultLayer,\n  isWebpackServerOnlyLayer,\n} from './utils'\nimport type { CustomRoutes } from '../lib/load-custom-routes.js'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  SERVER_DIRECTORY,\n  COMPILER_NAMES,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport { execOnce } from '../shared/lib/utils'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { finalizeEntrypoint } from './entries'\nimport * as Log from './output/log'\nimport { buildConfiguration } from './webpack/config'\nimport MiddlewarePlugin, {\n  getEdgePolyfilledModules,\n  handleWebpackExternalForEdgeRuntime,\n} from './webpack/plugins/middleware-plugin'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\nimport { regexLikeCss } from './webpack/config/blocks/css'\nimport { CopyFilePlugin } from './webpack/plugins/copy-file-plugin'\nimport { ClientReferenceManifestPlugin } from './webpack/plugins/flight-manifest-plugin'\nimport { FlightClientEntryPlugin as NextFlightClientEntryPlugin } from './webpack/plugins/flight-client-entry-plugin'\nimport { RspackFlightClientEntryPlugin } from './webpack/plugins/rspack-flight-client-entry-plugin'\nimport { NextTypesPlugin } from './webpack/plugins/next-types-plugin'\nimport type {\n  Feature,\n  SWC_TARGET_TRIPLE,\n} from './webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { Span } from '../trace'\nimport type { MiddlewareMatcher } from './analysis/get-page-static-info'\nimport loadJsConfig, {\n  type JsConfig,\n  type ResolvedBaseUrl,\n} from './load-jsconfig'\nimport { loadBindings } from './swc'\nimport { AppBuildManifestPlugin } from './webpack/plugins/app-build-manifest-plugin'\nimport { SubresourceIntegrityPlugin } from './webpack/plugins/subresource-integrity-plugin'\nimport { NextFontManifestPlugin } from './webpack/plugins/next-font-manifest-plugin'\nimport { getSupportedBrowsers } from './utils'\nimport { MemoryWithGcCachePlugin } from './webpack/plugins/memory-with-gc-cache-plugin'\nimport { getBabelConfigFile } from './get-babel-config-file'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport { getDefineEnvPlugin } from './webpack/plugins/define-env-plugin'\nimport type { SWCLoaderOptions } from './webpack/loaders/next-swc-loader'\nimport { isResourceInPackages, makeExternalHandler } from './handle-externals'\nimport {\n  getMainField,\n  edgeConditionNames,\n} from './webpack-config-rules/resolve'\nimport { OptionalPeerDependencyResolverPlugin } from './webpack/plugins/optional-peer-dependency-resolve-plugin'\nimport {\n  createWebpackAliases,\n  createServerOnlyClientOnlyAliases,\n  createRSCAliases,\n  createNextApiEsmAliases,\n  createAppRouterApiAliases,\n} from './create-compiler-aliases'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { CssChunkingPlugin } from './webpack/plugins/css-chunking-plugin'\nimport {\n  getBabelLoader,\n  getReactCompilerLoader,\n} from './get-babel-loader-config'\nimport {\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n} from './next-dir-paths'\nimport { getRspackCore, getRspackReactRefresh } from '../shared/lib/get-rspack'\nimport { RspackProfilingPlugin } from './webpack/plugins/rspack-profiling-plugin'\nimport getWebpackBundler from '../shared/lib/get-webpack-bundler'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nconst EXTERNAL_PACKAGES =\n  require('../lib/server-external-packages.json') as string[]\n\nconst DEFAULT_TRANSPILED_PACKAGES =\n  require('../lib/default-transpiled-packages.json') as string[]\n\nif (parseInt(React.version) < 18) {\n  throw new Error('Next.js requires react >= 18.2.0 to be installed.')\n}\n\nexport const babelIncludeRegexes: RegExp[] = [\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?shared[\\\\/]lib/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?client/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?pages/,\n  /[\\\\/](strip-ansi|ansi-regex|styled-jsx)[\\\\/]/,\n]\n\nconst browserNonTranspileModules = [\n  // Transpiling `process/browser` will trigger babel compilation error due to value replacement.\n  // TypeError: Property left of AssignmentExpression expected node to be of a type [\"LVal\"] but instead got \"BooleanLiteral\"\n  // e.g. `process.browser = true` will become `true = true`.\n  /[\\\\/]node_modules[\\\\/]process[\\\\/]browser/,\n  // Exclude precompiled react packages from browser compilation due to SWC helper insertion (#61791),\n  // We fixed the issue but it's safer to exclude them from compilation since they don't need to be re-compiled.\n  /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/](react|react-dom|react-server-dom-webpack)(-experimental)?($|[\\\\/])/,\n]\nconst precompileRegex = /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/]/\n\nconst asyncStoragesRegex =\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]app-render[\\\\/](work-async-storage|action-async-storage|work-unit-async-storage)/\n\n// Support for NODE_PATH\nconst nodePathList = (process.env.NODE_PATH || '')\n  .split(process.platform === 'win32' ? ';' : ':')\n  .filter((p) => !!p)\n\nconst baseWatchOptions: webpack.Configuration['watchOptions'] = Object.freeze({\n  aggregateTimeout: 5,\n  ignored:\n    // Matches **/node_modules/**, **/.git/** and **/.next/**\n    /^((?:[^/]*(?:\\/|$))*)(\\.(git|next)|node_modules)(\\/((?:[^/]*(?:\\/|$))*)(?:$|\\/))?/,\n})\n\nfunction isModuleCSS(module: { type: string }) {\n  return (\n    // mini-css-extract-plugin\n    module.type === `css/mini-extract` ||\n    // extract-css-chunks-webpack-plugin (old)\n    module.type === `css/extract-chunks` ||\n    // extract-css-chunks-webpack-plugin (new)\n    module.type === `css/extract-css-chunks`\n  )\n}\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      yellow(bold('Warning: ')) +\n        bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nlet loggedSwcDisabled = false\nlet loggedIgnoredCompilerOptions = false\nconst reactRefreshLoaderName =\n  'next/dist/compiled/@next/react-refresh-utils/dist/loader'\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  const reactRefreshLoader = require.resolve(reactRefreshLoaderName)\n  webpackConfig.module?.rules?.forEach((rule) => {\n    if (rule && typeof rule === 'object' && 'use' in rule) {\n      const curr = rule.use\n      // When the user has configured `defaultLoaders.babel` for a input file:\n      if (curr === targetLoader) {\n        rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n      } else if (\n        Array.isArray(curr) &&\n        curr.some((r) => r === targetLoader) &&\n        // Check if loader already exists:\n        !curr.some(\n          (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n        )\n      ) {\n        const idx = curr.findIndex((r) => r === targetLoader)\n        // Clone to not mutate user input\n        rule.use = [...curr]\n\n        // inject / input: [other, babel] output: [other, refresh, babel]:\n        rule.use.splice(idx, 0, reactRefreshLoader)\n      }\n    }\n  })\n}\n\nexport const NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nexport const NODE_BASE_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const NODE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n  dependencyType: 'esm',\n  conditionNames: ['node', 'import'],\n  fullySpecified: true,\n}\n\nexport const NODE_BASE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_ESM_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const nextImageLoaderRegex =\n  /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i\n\nexport async function loadProjectInfo({\n  dir,\n  config,\n  dev,\n}: {\n  dir: string\n  config: NextConfigComplete\n  dev: boolean\n}): Promise<{\n  jsConfig: JsConfig\n  jsConfigPath?: string\n  resolvedBaseUrl: ResolvedBaseUrl\n  supportedBrowsers: string[] | undefined\n}> {\n  const { jsConfig, jsConfigPath, resolvedBaseUrl } = await loadJsConfig(\n    dir,\n    config\n  )\n  const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  return {\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n  }\n}\n\nexport function hasExternalOtelApiPackage(): boolean {\n  try {\n    require('@opentelemetry/api')\n    return true\n  } catch {\n    return false\n  }\n}\n\nconst UNSAFE_CACHE_REGEX = /[\\\\/]pages[\\\\/][^\\\\/]+(?:$|\\?|#)/\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    encryptionKey,\n    config,\n    compilerType,\n    dev = false,\n    entrypoints,\n    isDevFallback = false,\n    pagesDir,\n    reactProductionProfiling = false,\n    rewrites,\n    originalRewrites,\n    originalRedirects,\n    runWebpackSpan,\n    appDir,\n    middlewareMatchers,\n    noMangling,\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n    clientRouterFilters,\n    fetchCacheKeyPrefix,\n    edgePreviewProps,\n    isCompileMode,\n  }: {\n    isCompileMode?: boolean\n    buildId: string\n    encryptionKey: string\n    config: NextConfigComplete\n    compilerType: CompilerNameValues\n    dev?: boolean\n    entrypoints: webpack.EntryObject\n    isDevFallback?: boolean\n    pagesDir: string | undefined\n    reactProductionProfiling?: boolean\n    rewrites: CustomRoutes['rewrites']\n    originalRewrites: CustomRoutes['rewrites'] | undefined\n    originalRedirects: CustomRoutes['redirects'] | undefined\n    runWebpackSpan: Span\n    appDir: string | undefined\n    middlewareMatchers?: MiddlewareMatcher[]\n    noMangling?: boolean\n    jsConfig: any\n    jsConfigPath?: string\n    resolvedBaseUrl: ResolvedBaseUrl\n    supportedBrowsers: string[] | undefined\n    edgePreviewProps?: Record<string, string>\n    clientRouterFilters?: {\n      staticFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n      dynamicFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n    }\n    fetchCacheKeyPrefix?: string\n  }\n): Promise<webpack.Configuration> {\n  const bundler = getWebpackBundler()\n  const isClient = compilerType === COMPILER_NAMES.client\n  const isEdgeServer = compilerType === COMPILER_NAMES.edgeServer\n  const isNodeServer = compilerType === COMPILER_NAMES.server\n\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n\n  const FlightClientEntryPlugin =\n    isRspack && process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN\n      ? RspackFlightClientEntryPlugin\n      : NextFlightClientEntryPlugin\n\n  // If the current compilation is aimed at server-side code instead of client-side code.\n  const isNodeOrEdgeCompilation = isNodeServer || isEdgeServer\n\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n\n  const hasAppDir = !!appDir\n  const disableOptimizedLoading = true\n  const enableTypedRoutes = !!config.experimental.typedRoutes && hasAppDir\n  const bundledReactChannel = needsExperimentalReact(config)\n    ? '-experimental'\n    : ''\n\n  const babelConfigFile = getBabelConfigFile(dir)\n\n  if (!dev && hasCustomExportOutput(config)) {\n    config.distDir = '.next'\n  }\n  const distDir = path.join(dir, config.distDir)\n\n  let useSWCLoader = !babelConfigFile || config.experimental.forceSwcTransforms\n  let SWCBinaryTarget: [Feature, boolean] | undefined = undefined\n  if (useSWCLoader) {\n    // TODO: we do not collect wasm target yet\n    const binaryTarget = require('./swc')?.getBinaryMetadata?.()\n      ?.target as SWC_TARGET_TRIPLE\n    SWCBinaryTarget = binaryTarget\n      ? [`swc/target/${binaryTarget}` as const, true]\n      : undefined\n  }\n\n  if (!loggedSwcDisabled && !useSWCLoader && babelConfigFile) {\n    Log.info(\n      `Disabled SWC as replacement for Babel because of custom Babel configuration \"${path.relative(\n        dir,\n        babelConfigFile\n      )}\" https://nextjs.org/docs/messages/swc-disabled`\n    )\n    loggedSwcDisabled = true\n  }\n\n  // eagerly load swc bindings instead of waiting for transform calls\n  if (!babelConfigFile && isClient) {\n    await loadBindings(config.experimental.useWasmBinary)\n  }\n\n  // since `pages` doesn't always bundle by default we need to\n  // auto-include optimizePackageImports in transpilePackages\n  const finalTranspilePackages: string[] = (\n    config.transpilePackages || []\n  ).concat(DEFAULT_TRANSPILED_PACKAGES)\n\n  for (const pkg of config.experimental.optimizePackageImports || []) {\n    if (!finalTranspilePackages.includes(pkg)) {\n      finalTranspilePackages.push(pkg)\n    }\n  }\n\n  if (!loggedIgnoredCompilerOptions && !useSWCLoader && config.compiler) {\n    Log.info(\n      '`compiler` options in `next.config.js` will be ignored while using Babel https://nextjs.org/docs/messages/ignored-compiler-options'\n    )\n    loggedIgnoredCompilerOptions = true\n  }\n\n  const shouldIncludeExternalDirs =\n    config.experimental.externalDir || !!config.transpilePackages\n  const codeCondition = {\n    test: { or: [/\\.(tsx|ts|js|cjs|mjs|jsx)$/, /__barrel_optimize__/] },\n    ...(shouldIncludeExternalDirs\n      ? // Allowing importing TS/TSX files from outside of the root dir.\n        {}\n      : { include: [dir, ...babelIncludeRegexes] }),\n    exclude: (excludePath: string) => {\n      if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n        return false\n      }\n\n      const shouldBeBundled = isResourceInPackages(\n        excludePath,\n        finalTranspilePackages\n      )\n      if (shouldBeBundled) return false\n\n      return excludePath.includes('node_modules')\n    },\n  }\n\n  const babelLoader = getBabelLoader(\n    useSWCLoader,\n    babelConfigFile,\n    isNodeOrEdgeCompilation,\n    distDir,\n    pagesDir,\n    dir,\n    (appDir || pagesDir)!,\n    dev,\n    isClient,\n    config.experimental?.reactCompiler,\n    codeCondition.exclude\n  )\n\n  const reactCompilerLoader = babelLoader\n    ? undefined\n    : getReactCompilerLoader(\n        config.experimental?.reactCompiler,\n        dir,\n        dev,\n        isNodeOrEdgeCompilation,\n        codeCondition.exclude\n      )\n\n  let swcTraceProfilingInitialized = false\n  const getSwcLoader = (extraOptions: Partial<SWCLoaderOptions>) => {\n    if (\n      config?.experimental?.swcTraceProfiling &&\n      !swcTraceProfilingInitialized\n    ) {\n      // This will init subscribers once only in a single process lifecycle,\n      // even though it can be called multiple times.\n      // Subscriber need to be initialized _before_ any actual swc's call (transform, etcs)\n      // to collect correct trace spans when they are called.\n      swcTraceProfilingInitialized = true\n      require('./swc')?.initCustomTraceSubscriber?.(\n        path.join(distDir, `swc-trace-profile-${Date.now()}.json`)\n      )\n    }\n\n    const useBuiltinSwcLoader = process.env.BUILTIN_SWC_LOADER\n    if (isRspack && useBuiltinSwcLoader) {\n      return {\n        loader: 'builtin:next-swc-loader',\n        options: {\n          isServer: isNodeOrEdgeCompilation,\n          rootDir: dir,\n          pagesDir,\n          appDir,\n          hasReactRefresh: dev && isClient,\n          transpilePackages: finalTranspilePackages,\n          supportedBrowsers,\n          swcCacheDir: path.join(\n            dir,\n            config?.distDir ?? '.next',\n            'cache',\n            'swc'\n          ),\n          serverReferenceHashSalt: encryptionKey,\n\n          // rspack specific options\n          pnp: Boolean(process.versions.pnp),\n          optimizeServerReact: Boolean(config.experimental.optimizeServerReact),\n          modularizeImports: config.modularizeImports,\n          decorators: Boolean(\n            jsConfig?.compilerOptions?.experimentalDecorators\n          ),\n          emitDecoratorMetadata: Boolean(\n            jsConfig?.compilerOptions?.emitDecoratorMetadata\n          ),\n          regeneratorRuntimePath: require.resolve(\n            'next/dist/compiled/regenerator-runtime'\n          ),\n\n          ...extraOptions,\n        },\n      }\n    }\n\n    return {\n      loader: 'next-swc-loader',\n      options: {\n        isServer: isNodeOrEdgeCompilation,\n        rootDir: dir,\n        pagesDir,\n        appDir,\n        hasReactRefresh: dev && isClient,\n        nextConfig: config,\n        jsConfig,\n        transpilePackages: finalTranspilePackages,\n        supportedBrowsers,\n        swcCacheDir: path.join(dir, config?.distDir ?? '.next', 'cache', 'swc'),\n        serverReferenceHashSalt: encryptionKey,\n        ...extraOptions,\n      } satisfies SWCLoaderOptions,\n    }\n  }\n\n  // RSC loaders, prefer ESM, set `esm` to true\n  const swcServerLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.reactServerComponents,\n    esm: true,\n  })\n  const swcSSRLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.serverSideRendering,\n    esm: true,\n  })\n  const swcBrowserLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.appPagesBrowser,\n    esm: true,\n  })\n  // Default swc loaders for pages doesn't prefer ESM.\n  const swcDefaultLoader = getSwcLoader({\n    serverComponents: true,\n    esm: false,\n  })\n\n  const defaultLoaders = {\n    babel: useSWCLoader ? swcDefaultLoader : babelLoader!,\n  }\n\n  const appServerLayerLoaders = hasAppDir\n    ? [\n        // When using Babel, we will have to add the SWC loader\n        // as an additional pass to handle RSC correctly.\n        // This will cause some performance overhead but\n        // acceptable as Babel will not be recommended.\n        swcServerLayerLoader,\n        babelLoader,\n        reactCompilerLoader,\n      ].filter(Boolean)\n    : []\n\n  const instrumentLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to add the SWC loader\n    // as an additional pass to handle RSC correctly.\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    swcServerLayerLoader,\n    babelLoader,\n  ].filter(Boolean)\n\n  const middlewareLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to use SWC to do the optimization\n    // for middleware to tree shake the unused default optimized imports like \"next/server\".\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    getSwcLoader({\n      serverComponents: true,\n      bundleLayer: WEBPACK_LAYERS.middleware,\n    }),\n    babelLoader,\n  ].filter(Boolean)\n\n  // Rspack will inject their own React Refresh loader in @rspack/plugin-react-refresh\n  const reactRefreshLoaders =\n    !isRspack && dev && isClient\n      ? [require.resolve(reactRefreshLoaderName)]\n      : []\n\n  // client components layers: SSR or browser\n  const createClientLayerLoader = ({\n    isBrowserLayer,\n    reactRefresh,\n  }: {\n    isBrowserLayer: boolean\n    reactRefresh: boolean\n  }) => [\n    ...(reactRefresh ? reactRefreshLoaders : []),\n    {\n      // This loader handles actions and client entries\n      // in the client layer.\n      loader: 'next-flight-client-module-loader',\n    },\n    ...(hasAppDir\n      ? [\n          // When using Babel, we will have to add the SWC loader\n          // as an additional pass to handle RSC correctly.\n          // This will cause some performance overhead but\n          // acceptable as Babel will not be recommended.\n          isBrowserLayer ? swcBrowserLayerLoader : swcSSRLayerLoader,\n          babelLoader,\n          reactCompilerLoader,\n        ].filter(Boolean)\n      : []),\n  ]\n\n  const appBrowserLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: true,\n    // reactRefresh for browser layer is applied conditionally to user-land source\n    reactRefresh: false,\n  })\n  const appSSRLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: false,\n    reactRefresh: true,\n  })\n\n  // Loader for API routes needs to be differently configured as it shouldn't\n  // have RSC transpiler enabled, so syntax checks such as invalid imports won't\n  // be performed.\n  const apiRoutesLayerLoaders = useSWCLoader\n    ? getSwcLoader({\n        serverComponents: false,\n        bundleLayer: WEBPACK_LAYERS.apiNode,\n      })\n    : defaultLoaders.babel\n\n  const pageExtensions = config.pageExtensions\n\n  const outputPath = isNodeOrEdgeCompilation\n    ? path.join(distDir, SERVER_DIRECTORY)\n    : distDir\n\n  const reactServerCondition = [\n    'react-server',\n    ...(isEdgeServer ? edgeConditionNames : []),\n    // inherits the default conditions\n    '...',\n  ]\n\n  const clientEntries = isClient\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: require.resolve(\n                `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n              ),\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                path\n                  .relative(\n                    dir,\n                    path.join(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                  )\n                  .replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n        ...(hasAppDir\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]: dev\n                ? [\n                    require.resolve(\n                      `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n                    ),\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next-dev.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ]\n                : [\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ],\n            }\n          : {}),\n      } satisfies ClientEntries)\n    : undefined\n\n  const resolveConfig: webpack.Configuration['resolve'] = {\n    // Disable .mjs for node_modules bundling\n    extensions: ['.js', '.mjs', '.tsx', '.ts', '.jsx', '.json', '.wasm'],\n    extensionAlias: config.experimental.extensionAlias,\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: createWebpackAliases({\n      distDir,\n      isClient,\n      isEdgeServer,\n      isNodeServer,\n      dev,\n      config,\n      pagesDir,\n      appDir,\n      dir,\n      reactProductionProfiling,\n      hasRewrites,\n    }),\n    ...(isClient\n      ? {\n          fallback: {\n            process: require.resolve('./polyfills/process'),\n          },\n        }\n      : undefined),\n    // default main fields use pages dir ones, and customize app router ones in loaders.\n    mainFields: getMainField(compilerType, false),\n    ...(isEdgeServer && {\n      conditionNames: edgeConditionNames,\n    }),\n    plugins: [\n      isNodeServer ? new OptionalPeerDependencyResolverPlugin() : undefined,\n    ].filter(Boolean) as webpack.ResolvePluginInstance[],\n    ...((isRspack && jsConfigPath\n      ? {\n          tsConfig: {\n            configFile: jsConfigPath,\n          },\n        }\n      : {}) as any),\n  }\n\n  // Packages which will be split into the 'framework' chunk.\n  // Only top-level packages are included, e.g. nested copies like\n  // 'node_modules/meow/node_modules/object-assign' are not included.\n  const nextFrameworkPaths: string[] = []\n  const topLevelFrameworkPaths: string[] = []\n  const visitedFrameworkPackages = new Set<string>()\n  // Adds package-paths of dependencies recursively\n  const addPackagePath = (\n    packageName: string,\n    relativeToPath: string,\n    paths: string[]\n  ) => {\n    try {\n      if (visitedFrameworkPackages.has(packageName)) {\n        return\n      }\n      visitedFrameworkPackages.add(packageName)\n\n      const packageJsonPath = require.resolve(`${packageName}/package.json`, {\n        paths: [relativeToPath],\n      })\n\n      // Include a trailing slash so that a `.startsWith(packagePath)` check avoids false positives\n      // when one package name starts with the full name of a different package.\n      // For example:\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react\")  // true\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react/\") // false\n      const directory = path.join(packageJsonPath, '../')\n\n      // Returning from the function in case the directory has already been added and traversed\n      if (paths.includes(directory)) return\n      paths.push(directory)\n      const dependencies = require(packageJsonPath).dependencies || {}\n      for (const name of Object.keys(dependencies)) {\n        addPackagePath(name, directory, paths)\n      }\n    } catch (_) {\n      // don't error on failing to resolve framework packages\n    }\n  }\n\n  for (const packageName of [\n    'react',\n    'react-dom',\n    ...(hasAppDir\n      ? [\n          `next/dist/compiled/react${bundledReactChannel}`,\n          `next/dist/compiled/react-dom${bundledReactChannel}`,\n        ]\n      : []),\n  ]) {\n    addPackagePath(packageName, dir, topLevelFrameworkPaths)\n  }\n  addPackagePath('next', dir, nextFrameworkPaths)\n\n  const crossOrigin = config.crossOrigin\n\n  // The `serverExternalPackages` should not conflict with\n  // the `transpilePackages`.\n  if (config.serverExternalPackages && finalTranspilePackages) {\n    const externalPackageConflicts = finalTranspilePackages.filter((pkg) =>\n      config.serverExternalPackages?.includes(pkg)\n    )\n    if (externalPackageConflicts.length > 0) {\n      throw new Error(\n        `The packages specified in the 'transpilePackages' conflict with the 'serverExternalPackages': ${externalPackageConflicts.join(\n          ', '\n        )}`\n      )\n    }\n  }\n\n  // For original request, such as `package name`\n  const optOutBundlingPackages = EXTERNAL_PACKAGES.concat(\n    ...(config.serverExternalPackages || [])\n  ).filter((pkg) => !finalTranspilePackages?.includes(pkg))\n  // For resolved request, such as `absolute path/package name/foo/bar.js`\n  const optOutBundlingPackageRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${optOutBundlingPackages\n      .map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const transpilePackagesRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${finalTranspilePackages\n      ?.map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const handleExternals = makeExternalHandler({\n    config,\n    optOutBundlingPackageRegex,\n    transpiledPackages: finalTranspilePackages,\n    dir,\n  })\n\n  const pageExtensionsRegex = new RegExp(`\\\\.(${pageExtensions.join('|')})$`)\n\n  const aliasCodeConditionTest = [codeCondition.test, pageExtensionsRegex]\n\n  const builtinModules = require('module').builtinModules\n\n  const shouldEnableSlowModuleDetection =\n    !!config.experimental.slowModuleDetection && dev\n\n  const getParallelism = () => {\n    const override = Number(process.env.NEXT_WEBPACK_PARALLELISM)\n    if (shouldEnableSlowModuleDetection) {\n      if (override) {\n        console.warn(\n          'NEXT_WEBPACK_PARALLELISM is specified but will be ignored due to experimental.slowModuleDetection being enabled.'\n        )\n      }\n      return 1\n    }\n    return override || undefined\n  }\n\n  const telemetryPlugin =\n    !dev &&\n    isClient &&\n    new (\n      require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n    ).TelemetryPlugin(\n      new Map(\n        [\n          ['swcLoader', useSWCLoader],\n          ['swcRelay', !!config.compiler?.relay],\n          ['swcStyledComponents', !!config.compiler?.styledComponents],\n          [\n            'swcReactRemoveProperties',\n            !!config.compiler?.reactRemoveProperties,\n          ],\n          [\n            'swcExperimentalDecorators',\n            !!jsConfig?.compilerOptions?.experimentalDecorators,\n          ],\n          ['swcRemoveConsole', !!config.compiler?.removeConsole],\n          ['swcImportSource', !!jsConfig?.compilerOptions?.jsxImportSource],\n          ['swcEmotion', !!config.compiler?.emotion],\n          ['transpilePackages', !!config.transpilePackages],\n          ['skipMiddlewareUrlNormalize', !!config.skipMiddlewareUrlNormalize],\n          ['skipTrailingSlashRedirect', !!config.skipTrailingSlashRedirect],\n          ['modularizeImports', !!config.modularizeImports],\n          // If esmExternals is not same as default value, it represents customized usage\n          ['esmExternals', config.experimental.esmExternals !== true],\n          SWCBinaryTarget,\n        ].filter<[Feature, boolean]>(Boolean as any)\n      )\n    )\n\n  let webpackConfig: webpack.Configuration = {\n    parallelism: getParallelism(),\n    ...(isNodeServer ? { externalsPresets: { node: true } } : {}),\n    // @ts-ignore\n    externals:\n      isClient || isEdgeServer\n        ? // make sure importing \"next\" is handled gracefully for client\n          // bundles in case a user imported types and it wasn't removed\n          // TODO: should we warn/error for this instead?\n          [\n            'next',\n            ...(isEdgeServer\n              ? [\n                  {\n                    '@builder.io/partytown': '{}',\n                    'next/dist/compiled/etag': '{}',\n                  },\n                  getEdgePolyfilledModules(),\n                  handleWebpackExternalForEdgeRuntime,\n                ]\n              : []),\n          ]\n        : [\n            ...builtinModules,\n            ({\n              context,\n              request,\n              dependencyType,\n              contextInfo,\n              getResolve,\n            }: {\n              context: string\n              request: string\n              dependencyType: string\n              contextInfo: {\n                issuer: string\n                issuerLayer: string | null\n                compiler: string\n              }\n              getResolve: (\n                options: any\n              ) => (\n                resolveContext: string,\n                resolveRequest: string,\n                callback: (\n                  err?: Error,\n                  result?: string,\n                  resolveData?: { descriptionFileData?: { type?: any } }\n                ) => void\n              ) => void\n            }) =>\n              handleExternals(\n                context,\n                request,\n                dependencyType,\n                contextInfo.issuerLayer as WebpackLayerName,\n                (options) => {\n                  const resolveFunction = getResolve(options)\n                  return (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve, reject) => {\n                      resolveFunction(\n                        resolveContext,\n                        requestToResolve,\n                        (err, result, resolveData) => {\n                          if (err) return reject(err)\n                          if (!result) return resolve([null, false])\n                          const isEsm = /\\.js$/i.test(result)\n                            ? resolveData?.descriptionFileData?.type ===\n                              'module'\n                            : /\\.mjs$/i.test(result)\n                          resolve([result, isEsm])\n                        }\n                      )\n                    })\n                }\n              ),\n          ],\n\n    optimization: {\n      emitOnErrors: !dev,\n      checkWasmTypes: false,\n      nodeEnv: false,\n\n      splitChunks: (():\n        | Required<webpack.Configuration>['optimization']['splitChunks']\n        | false => {\n        // server chunking\n        if (dev) {\n          if (isNodeServer) {\n            /*\n              In development, we want to split code that comes from `node_modules` into their own chunks.\n              This is because in development, we often need to reload the user bundle due to changes in the code.\n              To work around this, we put all the vendor code into separate chunks so that we don't need to reload them.\n              This is safe because the vendor code doesn't change between reloads.\n            */\n            const extractRootNodeModule = (modulePath: string) => {\n              // This regex is used to extract the root node module name to be used as the chunk group name.\n              // example: ../../node_modules/.pnpm/next@10/foo/node_modules/bar -> next@10\n              const regex =\n                /node_modules(?:\\/|\\\\)\\.?(?:pnpm(?:\\/|\\\\))?([^/\\\\]+)/\n              const match = modulePath.match(regex)\n              return match ? match[1] : null\n            }\n            return {\n              cacheGroups: {\n                // this chunk configuration gives us a separate chunk for each top level module in node_modules\n                // or a hashed chunk if we can't extract the module name.\n                vendor: {\n                  chunks: 'all',\n                  reuseExistingChunk: true,\n                  test: /[\\\\/]node_modules[\\\\/]/,\n                  minSize: 0,\n                  minChunks: 1,\n                  maxAsyncRequests: 300,\n                  maxInitialRequests: 300,\n                  name: (module: webpack.Module) => {\n                    const moduleId = module.nameForCondition()!\n                    const rootModule = extractRootNodeModule(moduleId)\n                    if (rootModule) {\n                      return `vendor-chunks/${rootModule}`\n                    } else {\n                      const hash = crypto.createHash('sha1').update(moduleId)\n                      hash.update(moduleId)\n                      return `vendor-chunks/${hash.digest('hex')}`\n                    }\n                  },\n                },\n                // disable the default chunk groups\n                default: false,\n                defaultVendors: false,\n              },\n            }\n          }\n\n          return false\n        }\n\n        if (isNodeServer || isEdgeServer) {\n          return {\n            filename: `${isEdgeServer ? `edge-chunks/` : ''}[name].js`,\n            chunks: 'all',\n            minChunks: 2,\n          }\n        }\n\n        const frameworkCacheGroup = {\n          chunks: 'all' as const,\n          name: 'framework',\n          // Ensures the framework chunk is not created for App Router.\n          layer: isWebpackDefaultLayer,\n          test(module: any) {\n            const resource = module.nameForCondition?.()\n            return resource\n              ? topLevelFrameworkPaths.some((pkgPath) =>\n                  resource.startsWith(pkgPath)\n                )\n              : false\n          },\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        }\n\n        const libCacheGroup = {\n          test(module: {\n            type: string\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              !module.type?.startsWith('css') &&\n              module.size() > 160000 &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            layer: string | null | undefined\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              if (!module.libIdent) {\n                throw new Error(\n                  `Encountered unknown module type: ${module.type}. Please open an issue.`\n                )\n              }\n              hash.update(module.libIdent({ context: dir }))\n            }\n\n            // Ensures the name of the chunk is not the same between two modules in different layers\n            // E.g. if you import 'button-library' in App Router and Pages Router we don't want these to be bundled in the same chunk\n            // as they're never used on the same page.\n            if (module.layer) {\n              hash.update(module.layer)\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        }\n\n        // client chunking\n        return {\n          // Keep main and _app chunks unsplitted in webpack 5\n          // as we don't need a separate vendor chunk from that\n          // and all other chunk depend on them so there is no\n          // duplication that need to be pulled out.\n          chunks: isRspack\n            ? // using a function here causes noticable slowdown\n              // in rspack\n              /(?!polyfills|main|pages\\/_app)/\n            : (chunk: any) =>\n                !/^(polyfills|main|pages\\/_app)$/.test(chunk.name),\n\n          // TODO: investigate these cache groups with rspack\n          cacheGroups: isRspack\n            ? {}\n            : {\n                framework: frameworkCacheGroup,\n                lib: libCacheGroup,\n              },\n          maxInitialRequests: 25,\n          minSize: 20000,\n        }\n      })(),\n      runtimeChunk: isClient\n        ? { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK }\n        : undefined,\n\n      minimize:\n        !dev &&\n        (isClient ||\n          isEdgeServer ||\n          (isNodeServer && config.experimental.serverMinification)),\n      minimizer: isRspack\n        ? [\n            new (getRspackCore().SwcJsMinimizerRspackPlugin)({\n              // JS minimizer configuration\n              // options should align with crates/napi/src/minify.rs#patch_opts\n              minimizerOptions: {\n                compress: {\n                  inline: 2,\n                  global_defs: {\n                    'process.env.__NEXT_PRIVATE_MINIMIZE_MACRO_FALSE': false,\n                  },\n                },\n                mangle: !noMangling && { reserved: ['AbortSignal'] },\n              },\n            }),\n            new (getRspackCore().LightningCssMinimizerRspackPlugin)({\n              // CSS minimizer configuration\n              minimizerOptions: {\n                targets: supportedBrowsers,\n              },\n            }),\n          ]\n        : [\n            // Minify JavaScript\n            (compiler: webpack.Compiler) => {\n              // @ts-ignore No typings yet\n              const { MinifyPlugin } =\n                require('./webpack/plugins/minify-webpack-plugin/src/index.js') as typeof import('./webpack/plugins/minify-webpack-plugin/src')\n              new MinifyPlugin({ noMangling }).apply(compiler)\n            },\n            // Minify CSS\n            (compiler: webpack.Compiler) => {\n              const {\n                CssMinimizerPlugin,\n              } = require('./webpack/plugins/css-minimizer-plugin')\n              new CssMinimizerPlugin({\n                postcssOptions: {\n                  map: {\n                    // `inline: false` generates the source map in a separate file.\n                    // Otherwise, the CSS file is needlessly large.\n                    inline: false,\n                    // `annotation: false` skips appending the `sourceMappingURL`\n                    // to the end of the CSS file. Webpack already handles this.\n                    annotation: false,\n                  },\n                },\n              }).apply(compiler)\n            },\n          ],\n    },\n    context: dir,\n    // Kept as function to be backwards compatible\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: Object.freeze({\n      ...baseWatchOptions,\n      poll: config.watchOptions?.pollIntervalMs,\n    }),\n    output: {\n      // we must set publicPath to an empty value to override the default of\n      // auto which doesn't work in IE11\n      publicPath: `${\n        config.assetPrefix\n          ? config.assetPrefix.endsWith('/')\n            ? config.assetPrefix.slice(0, -1)\n            : config.assetPrefix\n          : ''\n      }/_next/`,\n      path: !dev && isNodeServer ? path.join(outputPath, 'chunks') : outputPath,\n      // On the server we don't use hashes\n      filename: isNodeOrEdgeCompilation\n        ? dev || isEdgeServer\n          ? `[name].js`\n          : `../[name].js`\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : appDir ? '-[chunkhash]' : '-[contenthash]'\n          }.js`,\n      library: isClient || isEdgeServer ? '_N_E' : undefined,\n      libraryTarget: isClient || isEdgeServer ? 'assign' : 'commonjs2',\n      hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',\n      hotUpdateMainFilename:\n        'static/webpack/[fullhash].[runtime].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isNodeOrEdgeCompilation\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      // if `sources[number]` is not an absolute path, it's is resolved\n      // relative to the location of the source map file (https://tc39.es/source-map/#resolving-sources).\n      // However, Webpack's `resource-path` is relative to the app dir.\n      // TODO: Either `sourceRoot` should be populated with the root and then we can use `[resource-path]`\n      // or we need a way to resolve return `path.relative(sourceMapLocation, info.resourcePath)`\n      devtoolModuleFilenameTemplate: dev\n        ? '[absolute-resource-path]'\n        : undefined,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n      hashFunction: 'xxhash64',\n      hashDigestLength: 16,\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'error-loader',\n        'next-swc-loader',\n        'next-client-pages-loader',\n        'next-image-loader',\n        'next-metadata-image-loader',\n        'next-style-loader',\n        'next-flight-loader',\n        'next-flight-client-entry-loader',\n        'next-flight-action-entry-loader',\n        'next-flight-client-module-loader',\n        'next-flight-server-reference-proxy-loader',\n        'empty-loader',\n        'next-middleware-loader',\n        'next-edge-function-loader',\n        'next-edge-app-route-loader',\n        'next-edge-ssr-loader',\n        'next-middleware-asset-loader',\n        'next-middleware-wasm-loader',\n        'next-app-loader',\n        'next-route-loader',\n        'next-font-loader',\n        'next-invalid-import-error-loader',\n        'next-metadata-route-loader',\n        'modularize-import-loader',\n        'next-barrel-loader',\n        'next-error-browser-binary-loader',\n      ].reduce(\n        (alias, loader) => {\n          // using multiple aliases to replace `resolveLoader.modules`\n          alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n          return alias\n        },\n        {} as Record<string, string>\n      ),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: [],\n    },\n    module: {\n      rules: [\n        // Alias server-only and client-only to proper exports based on bundling layers\n        {\n          issuerLayer: {\n            or: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on client-only but allow server-only\n            alias: createServerOnlyClientOnlyAliases(true),\n          },\n        },\n        {\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on server-only but allow client-only\n            alias: createServerOnlyClientOnlyAliases(false),\n          },\n        },\n        // Detect server-only / client-only imports and error in build time\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.serverOnly,\n          },\n          options: {\n            message:\n              \"'client-only' cannot be imported from a Server Component module. It should only be used from a Client Component.\",\n          },\n        },\n        {\n          test: [\n            /^server-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]server-only[\\\\/]index/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          options: {\n            message:\n              \"'server-only' cannot be imported from a Client Component module. It should only be used from a Server Component.\",\n          },\n        },\n        // Potential the bundle introduced into middleware and api can be poisoned by client-only\n        // but not being used, so we disabled the `client-only` erroring on these layers.\n        // `server-only` is still available.\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'empty-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.neutralTarget,\n          },\n        },\n        ...(isNodeServer\n          ? []\n          : [\n              {\n                test: /[\\\\/].*?\\.node$/,\n                loader: 'next-error-browser-binary-loader',\n              },\n            ]),\n        ...(hasAppDir\n          ? [\n              {\n                // Make sure that AsyncLocalStorage module instance is shared between server and client\n                // layers.\n                layer: WEBPACK_LAYERS.shared,\n                test: asyncStoragesRegex,\n              },\n              // Convert metadata routes to separate layer\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.metadataRoute\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n              {\n                // Ensure that the app page module is in the client layers, this\n                // enables React to work correctly for RSC.\n                layer: WEBPACK_LAYERS.serverSideRendering,\n                test: /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]route-modules[\\\\/]app-page[\\\\/]module/,\n              },\n              {\n                issuerLayer: isWebpackBundledLayer,\n                resolve: {\n                  alias: createNextApiEsmAliases(),\n                },\n              },\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(true),\n                },\n              },\n              {\n                issuerLayer: isWebpackClientOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(false),\n                },\n              },\n            ]\n          : []),\n        ...(hasAppDir && !isClient\n          ? [\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                test: {\n                  // Resolve it if it is a source code file, and it has NOT been\n                  // opted out of bundling.\n                  and: [\n                    aliasCodeConditionTest,\n                    {\n                      not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                    },\n                  ],\n                },\n                resourceQuery: {\n                  // Do not apply next-flight-loader to imports generated by the\n                  // next-metadata-image-loader, to avoid generating unnecessary\n                  // and conflicting entries in the flight client entry plugin.\n                  // These are already covered by the next-metadata-route-loader\n                  // entries.\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                  conditionNames: reactServerCondition,\n                  // If missing the alias override here, the default alias will be used which aliases\n                  // react to the direct file path, not the package name. In that case the condition\n                  // will be ignored completely.\n                  alias: createRSCAliases(bundledReactChannel, {\n                    // No server components profiling\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.reactServerComponents,\n                    isEdgeServer,\n                  }),\n                },\n                use: 'next-flight-loader',\n              },\n            ]\n          : []),\n        // TODO: FIXME: do NOT webpack 5 support with this\n        // x-ref: https://github.com/webpack/webpack/issues/11467\n        ...(!config.experimental.fullySpecified\n          ? [\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        ...(hasAppDir && isEdgeServer\n          ? [\n              // The Edge bundle includes the server in its entrypoint, so it has to\n              // be in the SSR layer — here we convert the actual page request to\n              // the RSC layer via a webpack rule.\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n            ]\n          : []),\n        ...(hasAppDir\n          ? [\n              {\n                // Alias react-dom for ReactDOM.preload usage.\n                // Alias react for switching between default set and share subset.\n                oneOf: [\n                  {\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    test: {\n                      // Resolve it if it is a source code file, and it has NOT been\n                      // opted out of bundling.\n                      and: [\n                        aliasCodeConditionTest,\n                        {\n                          not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                        },\n                      ],\n                    },\n                    resolve: {\n                      // It needs `conditionNames` here to require the proper asset,\n                      // when react is acting as dependency of compiled/react-dom.\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.reactServerComponents,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                  {\n                    test: aliasCodeConditionTest,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    resolve: {\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.serverSideRendering,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                ],\n              },\n              {\n                test: aliasCodeConditionTest,\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                resolve: {\n                  alias: createRSCAliases(bundledReactChannel, {\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.appPagesBrowser,\n                    isEdgeServer,\n                  }),\n                },\n              },\n            ]\n          : []),\n        // Do not apply react-refresh-loader to node_modules for app router browser layer\n        ...(hasAppDir && dev && isClient\n          ? [\n              {\n                test: codeCondition.test,\n                exclude: [\n                  // exclude unchanged modules from react-refresh\n                  codeCondition.exclude,\n                  transpilePackagesRegex,\n                  precompileRegex,\n                ],\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                use: reactRefreshLoaders,\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                },\n              },\n            ]\n          : []),\n        {\n          oneOf: [\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiNode,\n              use: apiRoutesLayerLoaders,\n              // In Node.js, switch back to normal URL handling.\n              // We won't bundle `new URL()` cases in Node.js bundler layer.\n              parser: {\n                url: true,\n              },\n            },\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiEdge,\n              use: apiRoutesLayerLoaders,\n              // In Edge runtime, we leave the url handling by default.\n              // The new URL assets will be converted into edge assets through assets loader.\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.middleware,\n              use: middlewareLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.middleware,\n                  isEdgeServer,\n                }),\n              },\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.instrument,\n              use: instrumentLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.instrument,\n                  isEdgeServer,\n                }),\n              },\n            },\n            ...(hasAppDir\n              ? [\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    exclude: asyncStoragesRegex,\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    resourceQuery: new RegExp(\n                      WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                    ),\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                    // Exclude the transpilation of the app layer due to compilation issues\n                    exclude: browserNonTranspileModules,\n                    use: appBrowserLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    exclude: asyncStoragesRegex,\n                    use: appSSRLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                ]\n              : []),\n            {\n              ...codeCondition,\n              use: [\n                ...reactRefreshLoaders,\n                defaultLoaders.babel,\n                reactCompilerLoader,\n              ].filter(Boolean),\n            },\n          ],\n        },\n\n        ...(!config.images.disableStaticImages\n          ? [\n              {\n                test: nextImageLoaderRegex,\n                loader: 'next-image-loader',\n                issuer: { not: regexLikeCss },\n                dependency: { not: ['url'] },\n                resourceQuery: {\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataRoute),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                options: {\n                  isDev: dev,\n                  compilerType,\n                  basePath: config.basePath,\n                  assetPrefix: config.assetPrefix,\n                },\n              },\n            ]\n          : []),\n        ...(isEdgeServer\n          ? [\n              {\n                resolve: {\n                  fallback: {\n                    process: require.resolve('./polyfills/process'),\n                  },\n                },\n              },\n            ]\n          : isClient\n            ? [\n                {\n                  resolve: {\n                    fallback:\n                      config.experimental.fallbackNodePolyfills === false\n                        ? {\n                            assert: false,\n                            buffer: false,\n                            constants: false,\n                            crypto: false,\n                            domain: false,\n                            http: false,\n                            https: false,\n                            os: false,\n                            path: false,\n                            punycode: false,\n                            process: false,\n                            querystring: false,\n                            stream: false,\n                            string_decoder: false,\n                            sys: false,\n                            timers: false,\n                            tty: false,\n                            util: false,\n                            vm: false,\n                            zlib: false,\n                            events: false,\n                            setImmediate: false,\n                          }\n                        : {\n                            assert: require.resolve(\n                              'next/dist/compiled/assert'\n                            ),\n                            buffer: require.resolve(\n                              'next/dist/compiled/buffer'\n                            ),\n                            constants: require.resolve(\n                              'next/dist/compiled/constants-browserify'\n                            ),\n                            crypto: require.resolve(\n                              'next/dist/compiled/crypto-browserify'\n                            ),\n                            domain: require.resolve(\n                              'next/dist/compiled/domain-browser'\n                            ),\n                            http: require.resolve(\n                              'next/dist/compiled/stream-http'\n                            ),\n                            https: require.resolve(\n                              'next/dist/compiled/https-browserify'\n                            ),\n                            os: require.resolve(\n                              'next/dist/compiled/os-browserify'\n                            ),\n                            path: require.resolve(\n                              'next/dist/compiled/path-browserify'\n                            ),\n                            punycode: require.resolve(\n                              'next/dist/compiled/punycode'\n                            ),\n                            process: require.resolve('./polyfills/process'),\n                            // Handled in separate alias\n                            querystring: require.resolve(\n                              'next/dist/compiled/querystring-es3'\n                            ),\n                            stream: require.resolve(\n                              'next/dist/compiled/stream-browserify'\n                            ),\n                            string_decoder: require.resolve(\n                              'next/dist/compiled/string_decoder'\n                            ),\n                            sys: require.resolve('next/dist/compiled/util'),\n                            timers: require.resolve(\n                              'next/dist/compiled/timers-browserify'\n                            ),\n                            tty: require.resolve(\n                              'next/dist/compiled/tty-browserify'\n                            ),\n                            // Handled in separate alias\n                            // url: require.resolve('url'),\n                            util: require.resolve('next/dist/compiled/util'),\n                            vm: require.resolve(\n                              'next/dist/compiled/vm-browserify'\n                            ),\n                            zlib: require.resolve(\n                              'next/dist/compiled/browserify-zlib'\n                            ),\n                            events: require.resolve(\n                              'next/dist/compiled/events'\n                            ),\n                            setImmediate: require.resolve(\n                              'next/dist/compiled/setimmediate'\n                            ),\n                          },\n                  },\n                },\n              ]\n            : []),\n        {\n          // Mark `image-response.js` as side-effects free to make sure we can\n          // tree-shake it if not used.\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js/,\n          sideEffects: false,\n        },\n        // Mark the action-client-wrapper module as side-effects free to make sure\n        // the individual transformed module of client action can be tree-shaken.\n        // This will make modules processed by `next-flight-server-reference-proxy-loader` become side-effects free,\n        // then on client side the module ids will become tree-shakable.\n        // e.g. the output of client action module will look like:\n        // `export { a } from 'next-flight-server-reference-proxy-loader?id=idOfA&name=a!\n        // `export { b } from 'next-flight-server-reference-proxy-loader?id=idOfB&name=b!\n        {\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?build[\\\\/]webpack[\\\\/]loaders[\\\\/]next-flight-loader[\\\\/]action-client-wrapper\\.js/,\n          sideEffects: false,\n        },\n        {\n          // This loader rule should be before other rules, as it can output code\n          // that still contains `\"use client\"` or `\"use server\"` statements that\n          // needs to be re-transformed by the RSC compilers.\n          // This loader rule works like a bridge between user's import and\n          // the target module behind a package's barrel file. It reads SWC's\n          // analysis result from the previous loader, and directly returns the\n          // code that only exports values that are asked by the user.\n          test: /__barrel_optimize__/,\n          use: ({ resourceQuery }: { resourceQuery: string }) => {\n            const names = (\n              resourceQuery.match(/\\?names=([^&]+)/)?.[1] || ''\n            ).split(',')\n\n            return [\n              {\n                loader: 'next-barrel-loader',\n                options: {\n                  names,\n                  swcCacheDir: path.join(\n                    dir,\n                    config?.distDir ?? '.next',\n                    'cache',\n                    'swc'\n                  ),\n                },\n                // This is part of the request value to serve as the module key.\n                // The barrel loader are no-op re-exported modules keyed by\n                // export names.\n                ident: 'next-barrel-loader:' + resourceQuery,\n              },\n            ]\n          },\n        },\n        {\n          resolve: {\n            alias: {\n              next: NEXT_PROJECT_ROOT,\n            },\n          },\n        },\n      ],\n    },\n    plugins: [\n      isNodeServer &&\n        new bundler.NormalModuleReplacementPlugin(\n          /\\.\\/(.+)\\.shared-runtime$/,\n          function (resource) {\n            const moduleName = path.basename(\n              resource.request,\n              '.shared-runtime'\n            )\n            const layer = resource.contextInfo.issuerLayer\n            let runtime\n\n            switch (layer) {\n              case WEBPACK_LAYERS.serverSideRendering:\n              case WEBPACK_LAYERS.reactServerComponents:\n              case WEBPACK_LAYERS.appPagesBrowser:\n              case WEBPACK_LAYERS.actionBrowser:\n                runtime = 'app-page'\n                break\n              default:\n                runtime = 'pages'\n            }\n            resource.request = `next/dist/server/route-modules/${runtime}/vendored/contexts/${moduleName}`\n          }\n        ),\n      dev && new MemoryWithGcCachePlugin({ maxGenerations: 5 }),\n      dev &&\n        isClient &&\n        (isRspack\n          ? // eslint-disable-next-line\n            new (getRspackReactRefresh() as any)()\n          : new ReactRefreshWebpackPlugin(webpack)),\n      // Makes sure `Buffer` and `process` are polyfilled in client and flight bundles (same behavior as webpack 4)\n      (isClient || isEdgeServer) &&\n        new bundler.ProvidePlugin({\n          // Buffer is used by getInlineScriptSource\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          // Avoid process being overridden when in web run time\n          ...(isClient && { process: [require.resolve('process')] }),\n        }),\n      getDefineEnvPlugin({\n        isTurbopack: false,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix,\n        hasRewrites,\n        isClient,\n        isEdgeServer,\n        isNodeOrEdgeCompilation,\n        isNodeServer,\n        middlewareMatchers,\n        omitNonDeterministic: isCompileMode,\n      }),\n      isClient &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n          appDir,\n          runtimeAsset: `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n          dev,\n        }),\n      // rspack doesn't support the parser hooks used here\n      !isRspack && (isClient || isEdgeServer) && new DropClientPage(),\n      isNodeServer &&\n        !dev &&\n        new (require('./webpack/plugins/next-trace-entrypoints-plugin')\n          .TraceEntryPointsPlugin as typeof import('./webpack/plugins/next-trace-entrypoints-plugin').TraceEntryPointsPlugin)(\n          {\n            rootDir: dir,\n            appDir: appDir,\n            pagesDir: pagesDir,\n            esmExternals: config.experimental.esmExternals,\n            outputFileTracingRoot: config.outputFileTracingRoot,\n            appDirEnabled: hasAppDir,\n            traceIgnores: [],\n            compilerType,\n          }\n        ),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.excludeDefaultMomentLocales &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const { NextJsRequireCacheHotReloader } =\n              require('./webpack/plugins/nextjs-require-cache-hot-reloader') as typeof import('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins: any[] = [\n              new NextJsRequireCacheHotReloader({\n                serverComponents: hasAppDir,\n              }),\n            ]\n\n            if (isClient || isEdgeServer) {\n              devPlugins.push(new bundler.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      !dev &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /next[\\\\/]dist[\\\\/]/,\n        }),\n      isNodeOrEdgeCompilation &&\n        new PagesManifestPlugin({\n          dev,\n          appDirEnabled: hasAppDir,\n          isEdgeRuntime: isEdgeServer,\n          distDir: !dev ? distDir : undefined,\n        }),\n      // MiddlewarePlugin should be after DefinePlugin so NEXT_PUBLIC_*\n      // replacement is done before its process.env.* handling\n      isEdgeServer &&\n        new MiddlewarePlugin({\n          dev,\n          sriEnabled: !dev && !!config.experimental.sri?.algorithm,\n          rewrites,\n          edgeEnvironments: {\n            __NEXT_BUILD_ID: buildId,\n            NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: encryptionKey,\n            ...edgePreviewProps,\n          },\n        }),\n      isClient &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n          appDirEnabled: hasAppDir,\n          clientRouterFilters,\n        }),\n      isRspack\n        ? new RspackProfilingPlugin({ runWebpackSpan })\n        : new ProfilingPlugin({ runWebpackSpan, rootDir: dir }),\n      new WellKnownErrorsPlugin(),\n      isClient &&\n        new CopyFilePlugin({\n          // file path to build output of `@next/polyfill-nomodule`\n          filePath: require.resolve('./polyfills/polyfill-nomodule'),\n          cacheKey: process.env.__NEXT_VERSION as string,\n          name: `static/chunks/polyfills${dev ? '' : '-[hash]'}.js`,\n          minimize: false,\n          info: {\n            [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL]: 1,\n            // This file is already minified\n            minimized: true,\n          },\n        }),\n      hasAppDir && isClient && new AppBuildManifestPlugin({ dev }),\n      hasAppDir &&\n        (isClient\n          ? new ClientReferenceManifestPlugin({\n              dev,\n              appDir,\n              experimentalInlineCss: !!config.experimental.inlineCss,\n            })\n          : new FlightClientEntryPlugin({\n              appDir,\n              dev,\n              isEdgeServer,\n              encryptionKey,\n            })),\n      hasAppDir &&\n        !isClient &&\n        new NextTypesPlugin({\n          dir,\n          distDir: config.distDir,\n          appDir,\n          dev,\n          isEdgeServer,\n          pageExtensions: config.pageExtensions,\n          typedRoutes: enableTypedRoutes,\n          cacheLifeConfig: config.experimental.cacheLife,\n          originalRewrites,\n          originalRedirects,\n        }),\n      !dev &&\n        isClient &&\n        !!config.experimental.sri?.algorithm &&\n        new SubresourceIntegrityPlugin(config.experimental.sri.algorithm),\n      isClient &&\n        new NextFontManifestPlugin({\n          appDir,\n        }),\n      !isRspack &&\n        !dev &&\n        isClient &&\n        config.experimental.cssChunking &&\n        new CssChunkingPlugin(config.experimental.cssChunking === 'strict'),\n      telemetryPlugin,\n      !dev &&\n        isNodeServer &&\n        new (\n          require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n        ).TelemetryPlugin(new Map()),\n      shouldEnableSlowModuleDetection &&\n        new (\n          require('./webpack/plugins/slow-module-detection-plugin') as typeof import('./webpack/plugins/slow-module-detection-plugin')\n        ).default({\n          compilerType,\n          ...config.experimental.slowModuleDetection!,\n        }),\n    ].filter(Boolean as any as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n  if (resolvedBaseUrl && !resolvedBaseUrl.isImplicit) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n  }\n\n  // always add JsConfigPathsPlugin to allow hot-reloading\n  // if the config is added/removed\n  webpackConfig.resolve?.plugins?.unshift(\n    new JsConfigPathsPlugin(\n      jsConfig?.compilerOptions?.paths || {},\n      resolvedBaseUrl\n    )\n  )\n\n  const webpack5Config = webpackConfig as webpack.Configuration\n\n  if (isEdgeServer) {\n    webpack5Config.module?.rules?.unshift({\n      test: /\\.wasm$/,\n      loader: 'next-middleware-wasm-loader',\n      type: 'javascript/auto',\n      resourceQuery: /module/i,\n    })\n    webpack5Config.module?.rules?.unshift({\n      dependency: 'url',\n      loader: 'next-middleware-asset-loader',\n      type: 'javascript/auto',\n      layer: WEBPACK_LAYERS.edgeAsset,\n    })\n    webpack5Config.module?.rules?.unshift({\n      issuerLayer: WEBPACK_LAYERS.edgeAsset,\n      type: 'asset/source',\n    })\n  }\n\n  webpack5Config.experiments = {\n    layers: true,\n    cacheUnaffected: true,\n    buildHttp: Array.isArray(config.experimental.urlImports)\n      ? {\n          allowedUris: config.experimental.urlImports,\n          cacheLocation: path.join(dir, 'next.lock/data'),\n          lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n        }\n      : config.experimental.urlImports\n        ? {\n            cacheLocation: path.join(dir, 'next.lock/data'),\n            lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n            ...config.experimental.urlImports,\n          }\n        : undefined,\n  }\n\n  webpack5Config.module!.parser = {\n    javascript: {\n      url: 'relative',\n    },\n  }\n  webpack5Config.module!.generator = {\n    asset: {\n      filename: 'static/media/[name].[hash:8][ext]',\n    },\n  }\n\n  if (!webpack5Config.output) {\n    webpack5Config.output = {}\n  }\n  if (isClient) {\n    webpack5Config.output.trustedTypes = 'nextjs#bundler'\n  }\n\n  if (isClient || isEdgeServer) {\n    webpack5Config.output.enabledLibraryTypes = ['assign']\n  }\n\n  // This enables managedPaths for all node_modules\n  // and also for the unplugged folder when using yarn pnp\n  // It also add the yarn cache to the immutable paths\n  webpack5Config.snapshot = {}\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.managedPaths = [\n      /^(.+?(?:[\\\\/]\\.yarn[\\\\/]unplugged[\\\\/][^\\\\/]+)?[\\\\/]node_modules[\\\\/])/,\n    ]\n  } else {\n    webpack5Config.snapshot.managedPaths = [/^(.+?[\\\\/]node_modules[\\\\/])/]\n  }\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.immutablePaths = [\n      /^(.+?[\\\\/]cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/])/,\n    ]\n  }\n\n  if (dev) {\n    if (!webpack5Config.optimization) {\n      webpack5Config.optimization = {}\n    }\n\n    // For Server Components, it's necessary to have provided exports collected\n    // to generate the correct flight manifest.\n    if (!hasAppDir) {\n      webpack5Config.optimization.providedExports = false\n    }\n    webpack5Config.optimization.usedExports = false\n  }\n\n  const configVars = JSON.stringify({\n    optimizePackageImports: config?.experimental?.optimizePackageImports,\n    crossOrigin: config.crossOrigin,\n    pageExtensions: pageExtensions,\n    trailingSlash: config.trailingSlash,\n    buildActivityPosition:\n      config.devIndicators === false\n        ? undefined\n        : config.devIndicators.position,\n    productionBrowserSourceMaps: !!config.productionBrowserSourceMaps,\n    reactStrictMode: config.reactStrictMode,\n    optimizeCss: config.experimental.optimizeCss,\n    nextScriptWorkers: config.experimental.nextScriptWorkers,\n    scrollRestoration: config.experimental.scrollRestoration,\n    typedRoutes: config.experimental.typedRoutes,\n    basePath: config.basePath,\n    excludeDefaultMomentLocales: config.excludeDefaultMomentLocales,\n    assetPrefix: config.assetPrefix,\n    disableOptimizedLoading,\n    isEdgeRuntime: isEdgeServer,\n    reactProductionProfiling,\n    webpack: !!config.webpack,\n    hasRewrites,\n    swcLoader: useSWCLoader,\n    removeConsole: config.compiler?.removeConsole,\n    reactRemoveProperties: config.compiler?.reactRemoveProperties,\n    styledComponents: config.compiler?.styledComponents,\n    relay: config.compiler?.relay,\n    emotion: config.compiler?.emotion,\n    modularizeImports: config.modularizeImports,\n    imageLoaderFile: config.images.loaderFile,\n    clientTraceMetadata: config.experimental.clientTraceMetadata,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n    serverReferenceHashSalt: encryptionKey,\n  })\n\n  const cache: any = {\n    type: 'filesystem',\n    // Disable memory cache in development in favor of our own MemoryWithGcCachePlugin.\n    maxMemoryGenerations: dev ? 0 : Infinity, // Infinity is default value for production in webpack currently.\n    // Includes:\n    //  - Next.js location on disk (some loaders use absolute paths and some resolve options depend on absolute paths)\n    //  - Next.js version\n    //  - next.config.js keys that affect compilation\n    version: `${__dirname}|${process.env.__NEXT_VERSION}|${configVars}`,\n    cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    // For production builds, it's more efficient to compress all cache files together instead of compression each one individually.\n    // So we disable compression here and allow the build runner to take care of compressing the cache as a whole.\n    // For local development, we still want to compress the cache files individually to avoid I/O bottlenecks\n    // as we are seeing 1~10 seconds of fs I/O time from user reports.\n    compression: dev ? 'gzip' : false,\n  }\n\n  // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n  if (config.webpack && config.configFile) {\n    cache.buildDependencies = {\n      config: [config.configFile],\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  } else {\n    cache.buildDependencies = {\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  }\n  webpack5Config.plugins?.push((compiler) => {\n    compiler.hooks.done.tap('next-build-dependencies', (stats) => {\n      const buildDependencies = stats.compilation.buildDependencies\n      const nextPackage = path.dirname(require.resolve('next/package.json'))\n      // Remove all next.js build dependencies, they are already covered by the cacheVersion\n      // and next.js also imports the output files which leads to broken caching.\n      for (const dep of buildDependencies) {\n        if (dep.startsWith(nextPackage)) {\n          buildDependencies.delete(dep)\n        }\n      }\n    })\n  })\n\n  webpack5Config.cache = cache\n\n  if (process.env.NEXT_WEBPACK_LOGGING) {\n    const infra = process.env.NEXT_WEBPACK_LOGGING.includes('infrastructure')\n    const profileClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-client')\n    const profileServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-server')\n    const summaryClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-client')\n    const summaryServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-server')\n\n    const profile =\n      (profileClient && isClient) || (profileServer && isNodeOrEdgeCompilation)\n    const summary =\n      (summaryClient && isClient) || (summaryServer && isNodeOrEdgeCompilation)\n\n    const logDefault = !infra && !profile && !summary\n\n    if (logDefault || infra) {\n      webpack5Config.infrastructureLogging = {\n        level: 'verbose',\n        debug: /FileSystemInfo/,\n      }\n    }\n\n    if (logDefault || profile) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              colors: true,\n              logging: logDefault ? 'log' : 'verbose',\n            })\n          )\n        })\n      })\n    } else if (summary) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              preset: 'summary',\n              colors: true,\n              timings: true,\n            })\n          )\n        })\n      })\n    }\n\n    if (profile) {\n      const ProgressPlugin =\n        webpack.ProgressPlugin as unknown as typeof webpack.ProgressPlugin\n      webpack5Config.plugins!.push(\n        new ProgressPlugin({\n          profile: true,\n        })\n      )\n      webpack5Config.profile = true\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    supportedBrowsers,\n    rootDirectory: dir,\n    customAppFile: pagesDir\n      ? new RegExp(escapeStringRegexp(path.join(pagesDir, `_app`)))\n      : undefined,\n    hasAppDir,\n    isDevelopment: dev,\n    isServer: isNodeOrEdgeCompilation,\n    isEdgeRuntime: isEdgeServer,\n    targetWeb: isClient || isEdgeServer,\n    assetPrefix: config.assetPrefix || '',\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n    experimental: config.experimental,\n    disableStaticImages: config.images.disableStaticImages,\n    transpilePackages: config.transpilePackages,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n  })\n\n  // @ts-ignore Cache exists\n  webpackConfig.cache.name = `${webpackConfig.name}-${webpackConfig.mode}${\n    isDevFallback ? '-fallback' : ''\n  }`\n\n  if (dev) {\n    if (webpackConfig.module) {\n      webpackConfig.module.unsafeCache = (module: any) =>\n        !UNSAFE_CACHE_REGEX.test(module.resource)\n    } else {\n      webpackConfig.module = {\n        unsafeCache: (module: any) => !UNSAFE_CACHE_REGEX.test(module.resource),\n      }\n    }\n  }\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    const pluginCountBefore = webpackConfig.plugins?.length\n\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer: isNodeOrEdgeCompilation,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages: Object.keys(entrypoints).length,\n      webpack,\n      ...(isNodeOrEdgeCompilation\n        ? {\n            nextRuntime: isEdgeServer ? 'edge' : 'nodejs',\n          }\n        : {}),\n    })\n\n    if (telemetryPlugin && pluginCountBefore) {\n      const pluginCountAfter = webpackConfig.plugins?.length\n      if (pluginCountAfter) {\n        const pluginsChanged = pluginCountAfter !== pluginCountBefore\n        telemetryPlugin.addUsage('webpackPlugins', pluginsChanged ? 1 : 0)\n      }\n    }\n\n    if (!webpackConfig) {\n      throw new Error(\n        `Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your ${config.configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const webpack5Config = webpackConfig as webpack.Configuration\n\n    // disable lazy compilation of entries as next.js has it's own method here\n    if (webpack5Config.experiments?.lazyCompilation === true) {\n      webpack5Config.experiments.lazyCompilation = {\n        entries: false,\n      }\n    } else if (\n      typeof webpack5Config.experiments?.lazyCompilation === 'object' &&\n      webpack5Config.experiments.lazyCompilation.entries !== false\n    ) {\n      webpack5Config.experiments.lazyCompilation.entries = false\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n  const rules = webpackConfig.module?.rules || []\n\n  const customSvgRule = rules.find(\n    (rule): rule is webpack.RuleSetRule =>\n      (rule &&\n        typeof rule === 'object' &&\n        rule.loader !== 'next-image-loader' &&\n        'test' in rule &&\n        rule.test instanceof RegExp &&\n        rule.test.test('.svg')) ||\n      false\n  )\n\n  if (customSvgRule && hasAppDir) {\n    // Create React aliases for SVG components that were transformed using a\n    // custom webpack config with e.g. the `@svgr/webpack` loader, or the\n    // `babel-plugin-inline-react-svg` plugin.\n    rules.push({\n      test: customSvgRule.test,\n      oneOf: [\n        WEBPACK_LAYERS.reactServerComponents,\n        WEBPACK_LAYERS.serverSideRendering,\n        WEBPACK_LAYERS.appPagesBrowser,\n      ].map((layer) => ({\n        issuerLayer: layer,\n        resolve: {\n          alias: createRSCAliases(bundledReactChannel, {\n            reactProductionProfiling,\n            layer,\n            isEdgeServer,\n          }),\n        },\n      })),\n    })\n  }\n\n  if (!config.images.disableStaticImages) {\n    const nextImageRule = rules.find(\n      (rule) =>\n        rule && typeof rule === 'object' && rule.loader === 'next-image-loader'\n    )\n    if (customSvgRule && nextImageRule && typeof nextImageRule === 'object') {\n      // Exclude svg if the user already defined it in custom\n      // webpack config such as the `@svgr/webpack` loader, or\n      // the `babel-plugin-inline-react-svg` plugin.\n      nextImageRule.test = /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$/i\n    }\n  }\n\n  if (\n    config.experimental.craCompat &&\n    webpackConfig.module?.rules &&\n    webpackConfig.plugins\n  ) {\n    // CRA allows importing non-webpack handled files with file-loader\n    // these need to be the last rule to prevent catching other items\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L594\n    const fileLoaderExclude = [/\\.(js|mjs|jsx|ts|tsx|json)$/]\n    const fileLoader = {\n      exclude: fileLoaderExclude,\n      issuer: fileLoaderExclude,\n      type: 'asset/resource',\n    }\n\n    const topRules = []\n    const innerRules = []\n\n    for (const rule of webpackConfig.module.rules) {\n      if (!rule || typeof rule !== 'object') continue\n      if (rule.resolve) {\n        topRules.push(rule)\n      } else {\n        if (\n          rule.oneOf &&\n          !(rule.test || rule.exclude || rule.resource || rule.issuer)\n        ) {\n          rule.oneOf.forEach((r) => innerRules.push(r))\n        } else {\n          innerRules.push(rule)\n        }\n      }\n    }\n\n    webpackConfig.module.rules = [\n      ...(topRules as any),\n      {\n        oneOf: [...innerRules, fileLoader],\n      },\n    ]\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.css',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.scss',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.sass',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.less',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules?.some(\n      (rule: any) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isNodeOrEdgeCompilation) {\n      console.warn(\n        yellow(bold('Warning: ')) +\n          bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules?.length) {\n      // Remove default CSS Loaders\n      webpackConfig.module.rules.forEach((r) => {\n        if (!r || typeof r !== 'object') return\n        if (Array.isArray(r.oneOf)) {\n          r.oneOf = r.oneOf.filter(\n            (o) => (o as any)[Symbol.for('__next_css_remove')] !== true\n          )\n        }\n      })\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer =\n        webpackConfig.optimization.minimizer.filter(\n          (e) => (e as any).__next_css_remove !== true\n        )\n    }\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  // Rspack will inject their own React Refresh loader in @rspack/plugin-react-refresh\n  if (!isRspack && dev && isClient) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: webpack.EntryObject =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      for (const name of Object.keys(entry)) {\n        entry[name] = finalizeEntrypoint({\n          value: entry[name],\n          compilerType,\n          name,\n          hasAppDir,\n        })\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev && typeof webpackConfig.entry === 'function') {\n    // entry is always a function\n    webpackConfig.entry = await webpackConfig.entry()\n  }\n\n  return webpackConfig\n}\n"], "names": ["React", "ReactRefreshWebpackPlugin", "yellow", "bold", "crypto", "webpack", "path", "escapeStringRegexp", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "isWebpackBundledLayer", "isWebpackClientOnlyLayer", "isWebpackDefaultLayer", "isWebpackServerOnlyLayer", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "REACT_LOADABLE_MANIFEST", "SERVER_DIRECTORY", "COMPILER_NAMES", "execOnce", "finalizeEntrypoint", "Log", "buildConfiguration", "MiddlewarePlugin", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "BuildManifestPlugin", "JsConfigPathsPlugin", "DropClientPage", "PagesManifestPlugin", "Profiling<PERSON><PERSON><PERSON>", "ReactLoadablePlugin", "WellKnownErrorsPlugin", "regexLikeCss", "CopyFilePlugin", "ClientReferenceManifestPlugin", "FlightClientEntryPlugin", "NextFlightClientEntryPlugin", "RspackFlightClientEntryPlugin", "NextTypesPlugin", "loadJsConfig", "loadBindings", "AppBuildManifestPlugin", "SubresourceIntegrityPlugin", "NextFontManifestPlugin", "getSupportedBrowsers", "MemoryWithGcCachePlugin", "getBabelConfigFile", "needsExperimentalReact", "getDefineEnvPlugin", "isResourceInPackages", "makeExternalHandler", "getMainField", "edgeConditionNames", "OptionalPeerDependencyResolverPlugin", "createWebpackAliases", "createServerOnlyClientOnlyAliases", "createRSCAliases", "createNextApiEsmAliases", "createAppRouterApiAliases", "hasCustomExportOutput", "CssChunkingPlugin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getReactCompilerLoader", "NEXT_PROJECT_ROOT", "NEXT_PROJECT_ROOT_DIST_CLIENT", "getRspackCore", "getRspackReactRefresh", "RspackProfilingPlugin", "getWebpackBundler", "EXTERNAL_PACKAGES", "require", "DEFAULT_TRANSPILED_PACKAGES", "parseInt", "version", "Error", "babelIncludeRegexes", "browserNonTranspileModules", "precompileRegex", "asyncStoragesRegex", "nodePathList", "process", "env", "NODE_PATH", "split", "platform", "filter", "p", "baseWatchOptions", "Object", "freeze", "aggregateTimeout", "ignored", "isModuleCSS", "module", "type", "devtoolRevertWarning", "devtool", "console", "warn", "loggedSwcDisabled", "loggedIgnoredCompilerOptions", "reactRefreshLoaderName", "attachReactRefresh", "webpackConfig", "target<PERSON><PERSON><PERSON>", "reactRefreshLoader", "resolve", "rules", "for<PERSON>ach", "rule", "curr", "use", "Array", "isArray", "some", "r", "idx", "findIndex", "splice", "NODE_RESOLVE_OPTIONS", "dependencyType", "modules", "fallback", "exportsFields", "importsFields", "conditionNames", "descriptionFiles", "extensions", "enforceExtensions", "symlinks", "mainFields", "mainFiles", "roots", "fullySpecified", "preferRelative", "preferAbsolute", "restrictions", "NODE_BASE_RESOLVE_OPTIONS", "alias", "NODE_ESM_RESOLVE_OPTIONS", "NODE_BASE_ESM_RESOLVE_OPTIONS", "nextImageLoaderRegex", "loadProjectInfo", "dir", "config", "dev", "jsConfig", "jsConfigPath", "resolvedBaseUrl", "supportedBrowsers", "hasExternalOtelApiPackage", "UNSAFE_CACHE_REGEX", "getBaseWebpackConfig", "buildId", "<PERSON><PERSON><PERSON>", "compilerType", "entrypoints", "isDev<PERSON><PERSON><PERSON>", "pagesDir", "reactProductionProfiling", "rewrites", "originalRewrites", "originalRedirects", "runWebpackSpan", "appDir", "middlewareMatchers", "noMangling", "clientRouterFilters", "fetchCacheKeyPrefix", "edgePreviewProps", "isCompileMode", "webpack5Config", "bundler", "isClient", "client", "isEdgeServer", "edgeServer", "isNodeServer", "server", "isRspack", "Boolean", "NEXT_RSPACK", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "isNodeOrEdgeCompilation", "hasRewrites", "beforeFiles", "length", "afterFiles", "hasAppDir", "disableOptimizedLoading", "enableTypedRoutes", "experimental", "typedRoutes", "bundledReactChannel", "babelConfigFile", "distDir", "join", "useSWCLoader", "forceSwcTransforms", "SWCBinaryTarget", "undefined", "binaryTarget", "getBinaryMetadata", "target", "info", "relative", "useWasmBinary", "finalTranspilePackages", "transpilePackages", "concat", "pkg", "optimizePackageImports", "includes", "push", "compiler", "shouldIncludeExternalDirs", "externalDir", "codeCondition", "test", "or", "include", "exclude", "excludePath", "shouldBeBundled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reactCompiler", "reactCompilerLoader", "swcTraceProfilingInitialized", "getSwcLoader", "extraOptions", "swcTraceProfiling", "initCustomTraceSubscriber", "Date", "now", "useBuiltinSwcLoader", "BUILTIN_SWC_LOADER", "loader", "options", "isServer", "rootDir", "hasReactRefresh", "swcCacheDir", "serverReferenceHashSalt", "pnp", "versions", "optimizeServerReact", "modularizeImports", "decorators", "compilerOptions", "experimentalDecorators", "emitDecoratorMetadata", "regeneratorRuntimePath", "nextConfig", "swcServerLayerLoader", "serverComponents", "bundleLayer", "reactServerComponents", "esm", "swcSS<PERSON>ayer<PERSON><PERSON>der", "serverSideRendering", "swcBrowser<PERSON><PERSON><PERSON><PERSON><PERSON>der", "appPagesBrowser", "swcDefaultLoader", "defaultLoaders", "babel", "appServerLayerLoaders", "instrumentLayerLoaders", "middlewareLayerLoaders", "middleware", "reactRefreshLoaders", "createClientLayerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactRefresh", "appBrowserLayerLoaders", "appSSRLayerLoaders", "apiRoutesLayerLoaders", "apiNode", "pageExtensions", "outputPath", "reactServerCondition", "clientEntries", "replace", "resolveConfig", "extensionAlias", "plugins", "tsConfig", "configFile", "<PERSON><PERSON><PERSON>eworkP<PERSON><PERSON>", "topLevelFrameworkPaths", "visitedFrameworkPackages", "Set", "addPackagePath", "packageName", "relativeToPath", "paths", "has", "add", "packageJsonPath", "directory", "dependencies", "name", "keys", "_", "crossOrigin", "serverExternalPackages", "externalPackageConflicts", "optOutBundlingPackages", "optOutBundlingPackageRegex", "RegExp", "map", "transpilePackagesRegex", "handleExternals", "transpiledPackages", "pageExtensionsRegex", "aliasCodeConditionTest", "builtinModules", "shouldEnableSlowModuleDetection", "slowModuleDetection", "getParallelism", "override", "Number", "NEXT_WEBPACK_PARALLELISM", "telemetryPlugin", "TelemetryPlugin", "Map", "relay", "styledComponents", "reactRemoveProperties", "removeConsole", "jsxImportSource", "emotion", "skipMiddlewareUrlNormalize", "skipTrailingSlashRedirect", "esmExternals", "parallelism", "externalsPresets", "node", "externals", "context", "request", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "resolveFunction", "resolveContext", "requestToResolve", "Promise", "reject", "err", "result", "resolveData", "isEsm", "descriptionFileData", "optimization", "emitOnErrors", "checkWasmTypes", "nodeEnv", "splitChunks", "extractRootNodeModule", "modulePath", "regex", "match", "cacheGroups", "vendor", "chunks", "reuseExistingChunk", "minSize", "minChunks", "maxAsyncRequests", "maxInitialRequests", "moduleId", "nameForCondition", "rootModule", "hash", "createHash", "update", "digest", "default", "defaultVendors", "filename", "frameworkCacheGroup", "layer", "resource", "pkgPath", "startsWith", "priority", "enforce", "libCacheGroup", "size", "updateHash", "libIdent", "substring", "chunk", "framework", "lib", "runtimeChunk", "minimize", "serverMinification", "minimizer", "SwcJsMinimizerRspackPlugin", "minimizerOptions", "compress", "inline", "global_defs", "mangle", "reserved", "LightningCssMinimizerRspackPlugin", "targets", "MinifyPlugin", "apply", "CssMinimizerPlugin", "postcssOptions", "annotation", "entry", "watchOptions", "poll", "pollIntervalMs", "output", "publicPath", "assetPrefix", "endsWith", "slice", "library", "libraryTarget", "hotUpdateChunkFilename", "hotUpdateMainFilename", "chunkFilename", "strictModuleExceptionHandling", "crossOriginLoading", "devtoolModuleFilenameTemplate", "webassemblyModuleFilename", "hashFunction", "hashDigestLength", "performance", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "__dirname", "GROUP", "serverOnly", "neutralTarget", "not", "message", "shared", "resourceQuery", "metadataRoute", "and", "metadata", "metadataImageMeta", "edgeSSREntry", "oneOf", "parser", "url", "apiEdge", "instrument", "images", "disableStaticImages", "issuer", "dependency", "isDev", "basePath", "fallbackNodePolyfills", "assert", "buffer", "constants", "domain", "http", "https", "os", "punycode", "querystring", "stream", "string_decoder", "sys", "timers", "tty", "util", "vm", "zlib", "events", "setImmediate", "sideEffects", "names", "ident", "next", "NormalModuleReplacementPlugin", "moduleName", "basename", "runtime", "<PERSON><PERSON><PERSON><PERSON>", "maxGenerations", "ProvidePlugin", "<PERSON><PERSON><PERSON>", "isTurbopack", "omitNonDeterministic", "runtimeAsset", "TraceEntryPointsPlugin", "outputFileTracingRoot", "appDirEnabled", "traceIgnores", "excludeDefaultMomentLocales", "IgnorePlugin", "resourceRegExp", "contextRegExp", "NextJsRequireCacheHotReloader", "devP<PERSON><PERSON>", "HotModuleReplacementPlugin", "isEdgeRuntime", "sriEnabled", "sri", "algorithm", "edgeEnvironments", "__NEXT_BUILD_ID", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "filePath", "cache<PERSON>ey", "__NEXT_VERSION", "minimized", "experimentalInlineCss", "inlineCss", "cacheLifeConfig", "cacheLife", "cssChunking", "isImplicit", "baseUrl", "unshift", "edgeAsset", "experiments", "layers", "cacheUnaffected", "buildHttp", "urlImports", "<PERSON><PERSON><PERSON>", "cacheLocation", "lockfileLocation", "javascript", "generator", "asset", "trustedTypes", "enabledLibraryTypes", "snapshot", "managedPaths", "immutablePaths", "providedExports", "usedExports", "configVars", "JSON", "stringify", "trailingSlash", "buildActivityPosition", "devIndicators", "position", "productionBrowserSourceMaps", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "sw<PERSON><PERSON><PERSON><PERSON>", "imageLoaderFile", "loaderFile", "clientTraceMetadata", "serverSourceMaps", "cache", "maxMemoryGenerations", "Infinity", "cacheDirectory", "compression", "buildDependencies", "defaultWebpack", "hooks", "done", "tap", "stats", "compilation", "nextPackage", "dirname", "dep", "delete", "NEXT_WEBPACK_LOGGING", "infra", "profileClient", "profileServer", "summaryClient", "summaryServer", "profile", "summary", "logDefault", "infrastructureLogging", "level", "debug", "log", "toString", "colors", "logging", "preset", "timings", "ProgressPlugin", "rootDirectory", "customAppFile", "isDevelopment", "targetWeb", "sassOptions", "future", "mode", "unsafeCache", "originalDevtool", "pluginCountBefore", "totalPages", "nextRuntime", "pluginCountAfter", "pluginsChanged", "addUsage", "configFileName", "lazyCompilation", "entries", "then", "customSvgRule", "find", "nextImageRule", "craCompat", "fileLoaderExclude", "fileLoader", "topRules", "innerRules", "webpackDevMiddleware", "canMatchCss", "fileNames", "input", "hasUserCssConfig", "o", "Symbol", "for", "__next_css_remove", "e", "originalEntry", "updatedEntry", "originalFile", "value"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,+BAA+B,8EAA6E;AACnH,SAASC,MAAM,EAAEC,IAAI,QAAQ,oBAAmB;AAChD,OAAOC,YAAY,SAAQ;AAC3B,SAASC,OAAO,QAAQ,qCAAoC;AAC5D,OAAOC,UAAU,OAAM;AAEvB,SAASC,kBAAkB,QAAQ,8BAA6B;AAChE,SAASC,cAAc,EAAEC,wBAAwB,QAAQ,mBAAkB;AAE3E,SACEC,qBAAqB,EACrBC,wBAAwB,EACxBC,qBAAqB,EACrBC,wBAAwB,QACnB,UAAS;AAEhB,SACEC,+BAA+B,EAC/BC,gCAAgC,EAChCC,oCAAoC,EACpCC,4CAA4C,EAC5CC,yCAAyC,EACzCC,mCAAmC,EACnCC,kCAAkC,EAClCC,uBAAuB,EACvBC,gBAAgB,EAChBC,cAAc,QACT,0BAAyB;AAEhC,SAASC,QAAQ,QAAQ,sBAAqB;AAE9C,SAASC,kBAAkB,QAAQ,YAAW;AAC9C,YAAYC,SAAS,eAAc;AACnC,SAASC,kBAAkB,QAAQ,mBAAkB;AACrD,OAAOC,oBACLC,wBAAwB,EACxBC,mCAAmC,QAC9B,sCAAqC;AAC5C,OAAOC,yBAAyB,0CAAyC;AACzE,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,cAAc,QAAQ,iDAAgD;AAC/E,OAAOC,yBAAyB,0CAAyC;AACzE,SAASC,eAAe,QAAQ,qCAAoC;AACpE,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,qBAAqB,QAAQ,4CAA2C;AACjF,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,cAAc,QAAQ,qCAAoC;AACnE,SAASC,6BAA6B,QAAQ,2CAA0C;AACxF,SAASC,2BAA2BC,2BAA2B,QAAQ,+CAA8C;AACrH,SAASC,6BAA6B,QAAQ,sDAAqD;AACnG,SAASC,eAAe,QAAQ,sCAAqC;AAOrE,OAAOC,kBAGA,kBAAiB;AACxB,SAASC,YAAY,QAAQ,QAAO;AACpC,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,oBAAoB,QAAQ,UAAS;AAC9C,SAASC,uBAAuB,QAAQ,gDAA+C;AACvF,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,sBAAsB,QAAQ,kCAAiC;AACxE,SAASC,kBAAkB,QAAQ,sCAAqC;AAExE,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,qBAAoB;AAC9E,SACEC,YAAY,EACZC,kBAAkB,QACb,iCAAgC;AACvC,SAASC,oCAAoC,QAAQ,4DAA2D;AAChH,SACEC,oBAAoB,EACpBC,iCAAiC,EACjCC,gBAAgB,EAChBC,uBAAuB,EACvBC,yBAAyB,QACpB,4BAA2B;AAClC,SAASC,qBAAqB,QAAQ,kBAAiB;AACvD,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,cAAc,EACdC,sBAAsB,QACjB,4BAA2B;AAClC,SACEC,iBAAiB,EACjBC,6BAA6B,QACxB,mBAAkB;AACzB,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,2BAA0B;AAC/E,SAASC,qBAAqB,QAAQ,4CAA2C;AACjF,OAAOC,uBAAuB,oCAAmC;AAOjE,MAAMC,oBACJC,QAAQ;AAEV,MAAMC,8BACJD,QAAQ;AAEV,IAAIE,SAAS9E,MAAM+E,OAAO,IAAI,IAAI;IAChC,MAAM,qBAA8D,CAA9D,IAAIC,MAAM,sDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6D;AACrE;AAEA,OAAO,MAAMC,sBAAgC;IAC3C;IACA;IACA;IACA;CACD,CAAA;AAED,MAAMC,6BAA6B;IACjC,+FAA+F;IAC/F,2HAA2H;IAC3H,2DAA2D;IAC3D;IACA,oGAAoG;IACpG,8GAA8G;IAC9G;CACD;AACD,MAAMC,kBAAkB;AAExB,MAAMC,qBACJ;AAEF,wBAAwB;AACxB,MAAMC,eAAe,AAACC,CAAAA,QAAQC,GAAG,CAACC,SAAS,IAAI,EAAC,EAC7CC,KAAK,CAACH,QAAQI,QAAQ,KAAK,UAAU,MAAM,KAC3CC,MAAM,CAAC,CAACC,IAAM,CAAC,CAACA;AAEnB,MAAMC,mBAA0DC,OAAOC,MAAM,CAAC;IAC5EC,kBAAkB;IAClBC,SACE,yDAAyD;IACzD;AACJ;AAEA,SAASC,YAAYC,MAAwB;IAC3C,OACE,0BAA0B;IAC1BA,OAAOC,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAClC,0CAA0C;IAC1CD,OAAOC,IAAI,KAAK,CAAC,kBAAkB,CAAC,IACpC,0CAA0C;IAC1CD,OAAOC,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAE5C;AAEA,MAAMC,uBAAuB7E,SAC3B,CAAC8E;IACCC,QAAQC,IAAI,CACVtG,OAAOC,KAAK,gBACVA,KAAK,CAAC,8BAA8B,EAAEmG,QAAQ,IAAI,CAAC,IACnD,kGACA;AAEN;AAGF,IAAIG,oBAAoB;AACxB,IAAIC,+BAA+B;AACnC,MAAMC,yBACJ;AAEF,OAAO,SAASC,mBACdC,aAAoC,EACpCC,YAAoC;QAGpCD,6BAAAA;IADA,MAAME,qBAAqBnC,QAAQoC,OAAO,CAACL;KAC3CE,wBAAAA,cAAcV,MAAM,sBAApBU,8BAAAA,sBAAsBI,KAAK,qBAA3BJ,4BAA6BK,OAAO,CAAC,CAACC;QACpC,IAAIA,QAAQ,OAAOA,SAAS,YAAY,SAASA,MAAM;YACrD,MAAMC,OAAOD,KAAKE,GAAG;YACrB,wEAAwE;YACxE,IAAID,SAASN,cAAc;gBACzBK,KAAKE,GAAG,GAAG;oBAACN;oBAAoBK;iBAA+B;YACjE,OAAO,IACLE,MAAMC,OAAO,CAACH,SACdA,KAAKI,IAAI,CAAC,CAACC,IAAMA,MAAMX,iBACvB,kCAAkC;YAClC,CAACM,KAAKI,IAAI,CACR,CAACC,IAAMA,MAAMV,sBAAsBU,MAAMd,yBAE3C;gBACA,MAAMe,MAAMN,KAAKO,SAAS,CAAC,CAACF,IAAMA,MAAMX;gBACxC,iCAAiC;gBACjCK,KAAKE,GAAG,GAAG;uBAAID;iBAAK;gBAEpB,kEAAkE;gBAClED,KAAKE,GAAG,CAACO,MAAM,CAACF,KAAK,GAAGX;YAC1B;QACF;IACF;AACF;AAEA,OAAO,MAAMc,uBAAuB;IAClCC,gBAAgB;IAChBC,SAAS;QAAC;KAAe;IACzBC,UAAU;IACVC,eAAe;QAAC;KAAU;IAC1BC,eAAe;QAAC;KAAU;IAC1BC,gBAAgB;QAAC;QAAQ;KAAU;IACnCC,kBAAkB;QAAC;KAAe;IAClCC,YAAY;QAAC;QAAO;QAAS;KAAQ;IACrCC,mBAAmB;IACnBC,UAAU;IACVC,YAAY;QAAC;KAAO;IACpBC,WAAW;QAAC;KAAQ;IACpBC,OAAO,EAAE;IACTC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,cAAc,EAAE;AAClB,EAAC;AAED,OAAO,MAAMC,4BAA4B;IACvC,GAAGlB,oBAAoB;IACvBmB,OAAO;AACT,EAAC;AAED,OAAO,MAAMC,2BAA2B;IACtC,GAAGpB,oBAAoB;IACvBmB,OAAO;IACPlB,gBAAgB;IAChBK,gBAAgB;QAAC;QAAQ;KAAS;IAClCQ,gBAAgB;AAClB,EAAC;AAED,OAAO,MAAMO,gCAAgC;IAC3C,GAAGD,wBAAwB;IAC3BD,OAAO;AACT,EAAC;AAED,OAAO,MAAMG,uBACX,+CAA8C;AAEhD,OAAO,eAAeC,gBAAgB,EACpCC,GAAG,EACHC,MAAM,EACNC,GAAG,EAKJ;IAMC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAM7G,aACxDwG,KACAC;IAEF,MAAMK,oBAAoB,MAAMzG,qBAAqBmG,KAAKE;IAC1D,OAAO;QACLC;QACAC;QACAC;QACAC;IACF;AACF;AAEA,OAAO,SAASC;IACd,IAAI;QACFhF,QAAQ;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMiF,qBAAqB;AAE3B,eAAe,eAAeC,qBAC5BT,GAAW,EACX,EACEU,OAAO,EACPC,aAAa,EACbV,MAAM,EACNW,YAAY,EACZV,MAAM,KAAK,EACXW,WAAW,EACXC,gBAAgB,KAAK,EACrBC,QAAQ,EACRC,2BAA2B,KAAK,EAChCC,QAAQ,EACRC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,MAAM,EACNC,kBAAkB,EAClBC,UAAU,EACVpB,QAAQ,EACRC,YAAY,EACZC,eAAe,EACfC,iBAAiB,EACjBkB,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,aAAa,EAiCd;QAkHC1B,sBAOIA,uBA6biBA,kBACWA,mBAGtBA,mBAIAE,2BAEmBF,mBACDE,4BACLF,mBAyTfA,sBA2vBoBA,0BA+DtBA,2BAqCJE,4BAJJ,wDAAwD;IACxD,iCAAiC;IACjC3C,gCAAAA,wBAmG0ByC,uBAuBTA,mBACQA,mBACLA,mBACXA,mBACEA,mBAsCX2B,yBAgLcpE,uBAmDZA,wBA0FAA,6BAAAA;IAvrEF,MAAMqE,UAAUxG;IAChB,MAAMyG,WAAWlB,iBAAiB1I,eAAe6J,MAAM;IACvD,MAAMC,eAAepB,iBAAiB1I,eAAe+J,UAAU;IAC/D,MAAMC,eAAetB,iBAAiB1I,eAAeiK,MAAM;IAE3D,MAAMC,WAAWC,QAAQpG,QAAQC,GAAG,CAACoG,WAAW;IAEhD,MAAMlJ,0BACJgJ,YAAYnG,QAAQC,GAAG,CAACqG,kCAAkC,GACtDjJ,gCACAD;IAEN,uFAAuF;IACvF,MAAMmJ,0BAA0BN,gBAAgBF;IAEhD,MAAMS,cACJxB,SAASyB,WAAW,CAACC,MAAM,GAAG,KAC9B1B,SAAS2B,UAAU,CAACD,MAAM,GAAG,KAC7B1B,SAAStC,QAAQ,CAACgE,MAAM,GAAG;IAE7B,MAAME,YAAY,CAAC,CAACxB;IACpB,MAAMyB,0BAA0B;IAChC,MAAMC,oBAAoB,CAAC,CAAC9C,OAAO+C,YAAY,CAACC,WAAW,IAAIJ;IAC/D,MAAMK,sBAAsBlJ,uBAAuBiG,UAC/C,kBACA;IAEJ,MAAMkD,kBAAkBpJ,mBAAmBiG;IAE3C,IAAI,CAACE,OAAOtF,sBAAsBqF,SAAS;QACzCA,OAAOmD,OAAO,GAAG;IACnB;IACA,MAAMA,UAAUnM,KAAKoM,IAAI,CAACrD,KAAKC,OAAOmD,OAAO;IAE7C,IAAIE,eAAe,CAACH,mBAAmBlD,OAAO+C,YAAY,CAACO,kBAAkB;IAC7E,IAAIC,kBAAkDC;IACtD,IAAIH,cAAc;YAEK/H,4BAAAA,6BAAAA;QADrB,0CAA0C;QAC1C,MAAMmI,gBAAenI,WAAAA,QAAQ,8BAARA,8BAAAA,SAAkBoI,iBAAiB,sBAAnCpI,6BAAAA,iCAAAA,8BAAAA,2BACjBqI,MAAM;QACVJ,kBAAkBE,eACd;YAAC,CAAC,WAAW,EAAEA,cAAc;YAAW;SAAK,GAC7CD;IACN;IAEA,IAAI,CAACrG,qBAAqB,CAACkG,gBAAgBH,iBAAiB;QAC1D9K,IAAIwL,IAAI,CACN,CAAC,6EAA6E,EAAE5M,KAAK6M,QAAQ,CAC3F9D,KACAmD,iBACA,+CAA+C,CAAC;QAEpD/F,oBAAoB;IACtB;IAEA,mEAAmE;IACnE,IAAI,CAAC+F,mBAAmBrB,UAAU;QAChC,MAAMrI,aAAawG,OAAO+C,YAAY,CAACe,aAAa;IACtD;IAEA,4DAA4D;IAC5D,2DAA2D;IAC3D,MAAMC,yBAAmC,AACvC/D,CAAAA,OAAOgE,iBAAiB,IAAI,EAAE,AAAD,EAC7BC,MAAM,CAAC1I;IAET,KAAK,MAAM2I,OAAOlE,OAAO+C,YAAY,CAACoB,sBAAsB,IAAI,EAAE,CAAE;QAClE,IAAI,CAACJ,uBAAuBK,QAAQ,CAACF,MAAM;YACzCH,uBAAuBM,IAAI,CAACH;QAC9B;IACF;IAEA,IAAI,CAAC9G,gCAAgC,CAACiG,gBAAgBrD,OAAOsE,QAAQ,EAAE;QACrElM,IAAIwL,IAAI,CACN;QAEFxG,+BAA+B;IACjC;IAEA,MAAMmH,4BACJvE,OAAO+C,YAAY,CAACyB,WAAW,IAAI,CAAC,CAACxE,OAAOgE,iBAAiB;IAC/D,MAAMS,gBAAgB;QACpBC,MAAM;YAAEC,IAAI;gBAAC;gBAA8B;aAAsB;QAAC;QAClE,GAAIJ,4BAEA,CAAC,IACD;YAAEK,SAAS;gBAAC7E;mBAAQpE;aAAoB;QAAC,CAAC;QAC9CkJ,SAAS,CAACC;YACR,IAAInJ,oBAAoBuC,IAAI,CAAC,CAACC,IAAMA,EAAEuG,IAAI,CAACI,eAAe;gBACxD,OAAO;YACT;YAEA,MAAMC,kBAAkB9K,qBACtB6K,aACAf;YAEF,IAAIgB,iBAAiB,OAAO;YAE5B,OAAOD,YAAYV,QAAQ,CAAC;QAC9B;IACF;IAEA,MAAMY,cAAcnK,eAClBwI,cACAH,iBACAX,yBACAY,SACArC,UACAf,KACCqB,UAAUN,UACXb,KACA4B,WACA7B,uBAAAA,OAAO+C,YAAY,qBAAnB/C,qBAAqBiF,aAAa,EAClCR,cAAcI,OAAO;IAGvB,MAAMK,sBAAsBF,cACxBxB,YACA1I,wBACEkF,wBAAAA,OAAO+C,YAAY,qBAAnB/C,sBAAqBiF,aAAa,EAClClF,KACAE,KACAsC,yBACAkC,cAAcI,OAAO;IAG3B,IAAIM,+BAA+B;IACnC,MAAMC,eAAe,CAACC;YAElBrF;QADF,IACEA,CAAAA,2BAAAA,uBAAAA,OAAQ+C,YAAY,qBAApB/C,qBAAsBsF,iBAAiB,KACvC,CAACH,8BACD;gBAMA7J,oCAAAA;YALA,sEAAsE;YACtE,+CAA+C;YAC/C,qFAAqF;YACrF,uDAAuD;YACvD6J,+BAA+B;aAC/B7J,WAAAA,QAAQ,8BAARA,qCAAAA,SAAkBiK,yBAAyB,qBAA3CjK,wCAAAA,UACEtE,KAAKoM,IAAI,CAACD,SAAS,CAAC,kBAAkB,EAAEqC,KAAKC,GAAG,GAAG,KAAK,CAAC;QAE7D;QAEA,MAAMC,sBAAsB1J,QAAQC,GAAG,CAAC0J,kBAAkB;QAC1D,IAAIxD,YAAYuD,qBAAqB;gBAwB7BxF,2BAGAA;YA1BN,OAAO;gBACL0F,QAAQ;gBACRC,SAAS;oBACPC,UAAUvD;oBACVwD,SAAShG;oBACTe;oBACAM;oBACA4E,iBAAiB/F,OAAO4B;oBACxBmC,mBAAmBD;oBACnB1D;oBACA4F,aAAajP,KAAKoM,IAAI,CACpBrD,KACAC,CAAAA,0BAAAA,OAAQmD,OAAO,KAAI,SACnB,SACA;oBAEF+C,yBAAyBxF;oBAEzB,0BAA0B;oBAC1ByF,KAAK/D,QAAQpG,QAAQoK,QAAQ,CAACD,GAAG;oBACjCE,qBAAqBjE,QAAQpC,OAAO+C,YAAY,CAACsD,mBAAmB;oBACpEC,mBAAmBtG,OAAOsG,iBAAiB;oBAC3CC,YAAYnE,QACVlC,6BAAAA,4BAAAA,SAAUsG,eAAe,qBAAzBtG,0BAA2BuG,sBAAsB;oBAEnDC,uBAAuBtE,QACrBlC,6BAAAA,6BAAAA,SAAUsG,eAAe,qBAAzBtG,2BAA2BwG,qBAAqB;oBAElDC,wBAAwBrL,QAAQoC,OAAO,CACrC;oBAGF,GAAG2H,YAAY;gBACjB;YACF;QACF;QAEA,OAAO;YACLO,QAAQ;YACRC,SAAS;gBACPC,UAAUvD;gBACVwD,SAAShG;gBACTe;gBACAM;gBACA4E,iBAAiB/F,OAAO4B;gBACxB+E,YAAY5G;gBACZE;gBACA8D,mBAAmBD;gBACnB1D;gBACA4F,aAAajP,KAAKoM,IAAI,CAACrD,KAAKC,CAAAA,0BAAAA,OAAQmD,OAAO,KAAI,SAAS,SAAS;gBACjE+C,yBAAyBxF;gBACzB,GAAG2E,YAAY;YACjB;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAMwB,uBAAuBzB,aAAa;QACxC0B,kBAAkB;QAClBC,aAAa7P,eAAe8P,qBAAqB;QACjDC,KAAK;IACP;IACA,MAAMC,oBAAoB9B,aAAa;QACrC0B,kBAAkB;QAClBC,aAAa7P,eAAeiQ,mBAAmB;QAC/CF,KAAK;IACP;IACA,MAAMG,wBAAwBhC,aAAa;QACzC0B,kBAAkB;QAClBC,aAAa7P,eAAemQ,eAAe;QAC3CJ,KAAK;IACP;IACA,oDAAoD;IACpD,MAAMK,mBAAmBlC,aAAa;QACpC0B,kBAAkB;QAClBG,KAAK;IACP;IAEA,MAAMM,iBAAiB;QACrBC,OAAOnE,eAAeiE,mBAAmBtC;IAC3C;IAEA,MAAMyC,wBAAwB7E,YAC1B;QACE,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/CiE;QACA7B;QACAE;KACD,CAAC7I,MAAM,CAAC+F,WACT,EAAE;IAEN,MAAMsF,yBAAyB;QAC7B;QACA,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/Cb;QACA7B;KACD,CAAC3I,MAAM,CAAC+F;IAET,MAAMuF,yBAAyB;QAC7B;QACA,mEAAmE;QACnE,wFAAwF;QACxF,gDAAgD;QAChD,+CAA+C;QAC/CvC,aAAa;YACX0B,kBAAkB;YAClBC,aAAa7P,eAAe0Q,UAAU;QACxC;QACA5C;KACD,CAAC3I,MAAM,CAAC+F;IAET,oFAAoF;IACpF,MAAMyF,sBACJ,CAAC1F,YAAYlC,OAAO4B,WAChB;QAACvG,QAAQoC,OAAO,CAACL;KAAwB,GACzC,EAAE;IAER,2CAA2C;IAC3C,MAAMyK,0BAA0B,CAAC,EAC/BC,cAAc,EACdC,YAAY,EAIb,GAAK;eACAA,eAAeH,sBAAsB,EAAE;YAC3C;gBACE,iDAAiD;gBACjD,uBAAuB;gBACvBjC,QAAQ;YACV;eACIhD,YACA;gBACE,uDAAuD;gBACvD,iDAAiD;gBACjD,gDAAgD;gBAChD,+CAA+C;gBAC/CmF,iBAAiBX,wBAAwBF;gBACzClC;gBACAE;aACD,CAAC7I,MAAM,CAAC+F,WACT,EAAE;SACP;IAED,MAAM6F,yBAAyBH,wBAAwB;QACrDC,gBAAgB;QAChB,8EAA8E;QAC9EC,cAAc;IAChB;IACA,MAAME,qBAAqBJ,wBAAwB;QACjDC,gBAAgB;QAChBC,cAAc;IAChB;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMG,wBAAwB9E,eAC1B+B,aAAa;QACX0B,kBAAkB;QAClBC,aAAa7P,eAAekR,OAAO;IACrC,KACAb,eAAeC,KAAK;IAExB,MAAMa,iBAAiBrI,OAAOqI,cAAc;IAE5C,MAAMC,aAAa/F,0BACfvL,KAAKoM,IAAI,CAACD,SAASnL,oBACnBmL;IAEJ,MAAMoF,uBAAuB;QAC3B;WACIxG,eAAe3H,qBAAqB,EAAE;QAC1C,kCAAkC;QAClC;KACD;IAED,MAAMoO,gBAAgB3G,WACjB;QACC,0BAA0B;QAC1B,WAAW,EAAE;QACb,GAAI5B,MACA;YACE,CAACrI,0CAA0C,EAAE0D,QAAQoC,OAAO,CAC1D,CAAC,yDAAyD,CAAC;YAE7D,CAAClG,gCAAgC,EAC/B,CAAC,EAAE,CAAC,GACJR,KACG6M,QAAQ,CACP9D,KACA/I,KAAKoM,IAAI,CAACpI,+BAA+B,OAAO,YAEjDyN,OAAO,CAAC,OAAO;QACtB,IACA,CAAC,CAAC;QACN,CAAChR,iCAAiC,EAChC,CAAC,EAAE,CAAC,GACJT,KACG6M,QAAQ,CACP9D,KACA/I,KAAKoM,IAAI,CACPpI,+BACAiF,MAAM,CAAC,WAAW,CAAC,GAAG,YAGzBwI,OAAO,CAAC,OAAO;QACpB,GAAI7F,YACA;YACE,CAAClL,qCAAqC,EAAEuI,MACpC;gBACE3E,QAAQoC,OAAO,CACb,CAAC,yDAAyD,CAAC;gBAE7D,CAAC,EAAE,CAAC,GACF1G,KACG6M,QAAQ,CACP9D,KACA/I,KAAKoM,IAAI,CACPpI,+BACA,oBAGHyN,OAAO,CAAC,OAAO;aACrB,GACD;gBACE,CAAC,EAAE,CAAC,GACFzR,KACG6M,QAAQ,CACP9D,KACA/I,KAAKoM,IAAI,CACPpI,+BACA,gBAGHyN,OAAO,CAAC,OAAO;aACrB;QACP,IACA,CAAC,CAAC;IACR,IACAjF;IAEJ,MAAMkF,gBAAkD;QACtD,yCAAyC;QACzC3J,YAAY;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAS;SAAQ;QACpE4J,gBAAgB3I,OAAO+C,YAAY,CAAC4F,cAAc;QAClDlK,SAAS;YACP;eACG1C;SACJ;QACD2D,OAAOpF,qBAAqB;YAC1B6I;YACAtB;YACAE;YACAE;YACAhC;YACAD;YACAc;YACAM;YACArB;YACAgB;YACAyB;QACF;QACA,GAAIX,WACA;YACEnD,UAAU;gBACR1C,SAASV,QAAQoC,OAAO,CAAC;YAC3B;QACF,IACA8F,SAAS;QACb,oFAAoF;QACpFtE,YAAY/E,aAAawG,cAAc;QACvC,GAAIoB,gBAAgB;YAClBlD,gBAAgBzE;QAClB,CAAC;QACDwO,SAAS;YACP3G,eAAe,IAAI5H,yCAAyCmJ;SAC7D,CAACnH,MAAM,CAAC+F;QACT,GAAKD,YAAYhC,eACb;YACE0I,UAAU;gBACRC,YAAY3I;YACd;QACF,IACA,CAAC,CAAC;IACR;IAEA,2DAA2D;IAC3D,gEAAgE;IAChE,mEAAmE;IACnE,MAAM4I,qBAA+B,EAAE;IACvC,MAAMC,yBAAmC,EAAE;IAC3C,MAAMC,2BAA2B,IAAIC;IACrC,iDAAiD;IACjD,MAAMC,iBAAiB,CACrBC,aACAC,gBACAC;QAEA,IAAI;YACF,IAAIL,yBAAyBM,GAAG,CAACH,cAAc;gBAC7C;YACF;YACAH,yBAAyBO,GAAG,CAACJ;YAE7B,MAAMK,kBAAkBnO,QAAQoC,OAAO,CAAC,GAAG0L,YAAY,aAAa,CAAC,EAAE;gBACrEE,OAAO;oBAACD;iBAAe;YACzB;YAEA,6FAA6F;YAC7F,0EAA0E;YAC1E,eAAe;YACf,0EAA0E;YAC1E,2EAA2E;YAC3E,MAAMK,YAAY1S,KAAKoM,IAAI,CAACqG,iBAAiB;YAE7C,yFAAyF;YACzF,IAAIH,MAAMlF,QAAQ,CAACsF,YAAY;YAC/BJ,MAAMjF,IAAI,CAACqF;YACX,MAAMC,eAAerO,QAAQmO,iBAAiBE,YAAY,IAAI,CAAC;YAC/D,KAAK,MAAMC,QAAQpN,OAAOqN,IAAI,CAACF,cAAe;gBAC5CR,eAAeS,MAAMF,WAAWJ;YAClC;QACF,EAAE,OAAOQ,GAAG;QACV,uDAAuD;QACzD;IACF;IAEA,KAAK,MAAMV,eAAe;QACxB;QACA;WACIxG,YACA;YACE,CAAC,wBAAwB,EAAEK,qBAAqB;YAChD,CAAC,4BAA4B,EAAEA,qBAAqB;SACrD,GACD,EAAE;KACP,CAAE;QACDkG,eAAeC,aAAarJ,KAAKiJ;IACnC;IACAG,eAAe,QAAQpJ,KAAKgJ;IAE5B,MAAMgB,cAAc/J,OAAO+J,WAAW;IAEtC,wDAAwD;IACxD,2BAA2B;IAC3B,IAAI/J,OAAOgK,sBAAsB,IAAIjG,wBAAwB;QAC3D,MAAMkG,2BAA2BlG,uBAAuB1H,MAAM,CAAC,CAAC6H;gBAC9DlE;oBAAAA,iCAAAA,OAAOgK,sBAAsB,qBAA7BhK,+BAA+BoE,QAAQ,CAACF;;QAE1C,IAAI+F,yBAAyBvH,MAAM,GAAG,GAAG;YACvC,MAAM,qBAIL,CAJK,IAAIhH,MACR,CAAC,8FAA8F,EAAEuO,yBAAyB7G,IAAI,CAC5H,OACC,GAHC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,+CAA+C;IAC/C,MAAM8G,yBAAyB7O,kBAAkB4I,MAAM,IACjDjE,OAAOgK,sBAAsB,IAAI,EAAE,EACvC3N,MAAM,CAAC,CAAC6H,MAAQ,EAACH,0CAAAA,uBAAwBK,QAAQ,CAACF;IACpD,wEAAwE;IACxE,MAAMiG,6BAA6B,IAAIC,OACrC,CAAC,2BAA2B,EAAEF,uBAC3BG,GAAG,CAAC,CAAC/N,IAAMA,EAAEmM,OAAO,CAAC,OAAO,YAC5BrF,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMkH,yBAAyB,IAAIF,OACjC,CAAC,2BAA2B,EAAErG,0CAAAA,uBAC1BsG,GAAG,CAAC,CAAC/N,IAAMA,EAAEmM,OAAO,CAAC,OAAO,YAC7BrF,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMmH,kBAAkBrQ,oBAAoB;QAC1C8F;QACAmK;QACAK,oBAAoBzG;QACpBhE;IACF;IAEA,MAAM0K,sBAAsB,IAAIL,OAAO,CAAC,IAAI,EAAE/B,eAAejF,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1E,MAAMsH,yBAAyB;QAACjG,cAAcC,IAAI;QAAE+F;KAAoB;IAExE,MAAME,iBAAiBrP,QAAQ,UAAUqP,cAAc;IAEvD,MAAMC,kCACJ,CAAC,CAAC5K,OAAO+C,YAAY,CAAC8H,mBAAmB,IAAI5K;IAE/C,MAAM6K,iBAAiB;QACrB,MAAMC,WAAWC,OAAOhP,QAAQC,GAAG,CAACgP,wBAAwB;QAC5D,IAAIL,iCAAiC;YACnC,IAAIG,UAAU;gBACZ9N,QAAQC,IAAI,CACV;YAEJ;YACA,OAAO;QACT;QACA,OAAO6N,YAAYvH;IACrB;IAEA,MAAM0H,kBACJ,CAACjL,OACD4B,YACA,IAAI,AACFvG,CAAAA,QAAQ,sDAAqD,EAC7D6P,eAAe,CACf,IAAIC,IACF;QACE;YAAC;YAAa/H;SAAa;QAC3B;YAAC;YAAY,CAAC,GAACrD,mBAAAA,OAAOsE,QAAQ,qBAAftE,iBAAiBqL,KAAK;SAAC;QACtC;YAAC;YAAuB,CAAC,GAACrL,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiBsL,gBAAgB;SAAC;QAC5D;YACE;YACA,CAAC,GAACtL,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiBuL,qBAAqB;SACzC;QACD;YACE;YACA,CAAC,EAACrL,6BAAAA,4BAAAA,SAAUsG,eAAe,qBAAzBtG,0BAA2BuG,sBAAsB;SACpD;QACD;YAAC;YAAoB,CAAC,GAACzG,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiBwL,aAAa;SAAC;QACtD;YAAC;YAAmB,CAAC,EAACtL,6BAAAA,6BAAAA,SAAUsG,eAAe,qBAAzBtG,2BAA2BuL,eAAe;SAAC;QACjE;YAAC;YAAc,CAAC,GAACzL,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiB0L,OAAO;SAAC;QAC1C;YAAC;YAAqB,CAAC,CAAC1L,OAAOgE,iBAAiB;SAAC;QACjD;YAAC;YAA8B,CAAC,CAAChE,OAAO2L,0BAA0B;SAAC;QACnE;YAAC;YAA6B,CAAC,CAAC3L,OAAO4L,yBAAyB;SAAC;QACjE;YAAC;YAAqB,CAAC,CAAC5L,OAAOsG,iBAAiB;SAAC;QACjD,+EAA+E;QAC/E;YAAC;YAAgBtG,OAAO+C,YAAY,CAAC8I,YAAY,KAAK;SAAK;QAC3DtI;KACD,CAAClH,MAAM,CAAqB+F;IAInC,IAAI7E,gBAAuC;QACzCuO,aAAahB;QACb,GAAI7I,eAAe;YAAE8J,kBAAkB;gBAAEC,MAAM;YAAK;QAAE,IAAI,CAAC,CAAC;QAC5D,aAAa;QACbC,WACEpK,YAAYE,eAER,8DAA8D;QAC9D,+CAA+C;QAC/C;YACE;eACIA,eACA;gBACE;oBACE,yBAAyB;oBACzB,2BAA2B;gBAC7B;gBACAxJ;gBACAC;aACD,GACD,EAAE;SACP,GACD;eACKmS;YACH,CAAC,EACCuB,OAAO,EACPC,OAAO,EACP3N,cAAc,EACd4N,WAAW,EACXC,UAAU,EAqBX,GACC9B,gBACE2B,SACAC,SACA3N,gBACA4N,YAAYE,WAAW,EACvB,CAACzG;oBACC,MAAM0G,kBAAkBF,WAAWxG;oBACnC,OAAO,CAAC2G,gBAAwBC,mBAC9B,IAAIC,QAAQ,CAAChP,SAASiP;4BACpBJ,gBACEC,gBACAC,kBACA,CAACG,KAAKC,QAAQC;oCAIRA;gCAHJ,IAAIF,KAAK,OAAOD,OAAOC;gCACvB,IAAI,CAACC,QAAQ,OAAOnP,QAAQ;oCAAC;oCAAM;iCAAM;gCACzC,MAAMqP,QAAQ,SAASrI,IAAI,CAACmI,UACxBC,CAAAA,gCAAAA,mCAAAA,YAAaE,mBAAmB,qBAAhCF,iCAAkChQ,IAAI,MACtC,WACA,UAAU4H,IAAI,CAACmI;gCACnBnP,QAAQ;oCAACmP;oCAAQE;iCAAM;4BACzB;wBAEJ;gBACJ;SAEL;QAEPE,cAAc;YACZC,cAAc,CAACjN;YACfkN,gBAAgB;YAChBC,SAAS;YAETC,aAAa,AAAC,CAAA;gBAGZ,kBAAkB;gBAClB,IAAIpN,KAAK;oBACP,IAAIgC,cAAc;wBAChB;;;;;YAKA,GACA,MAAMqL,wBAAwB,CAACC;4BAC7B,8FAA8F;4BAC9F,4EAA4E;4BAC5E,MAAMC,QACJ;4BACF,MAAMC,QAAQF,WAAWE,KAAK,CAACD;4BAC/B,OAAOC,QAAQA,KAAK,CAAC,EAAE,GAAG;wBAC5B;wBACA,OAAO;4BACLC,aAAa;gCACX,+FAA+F;gCAC/F,yDAAyD;gCACzDC,QAAQ;oCACNC,QAAQ;oCACRC,oBAAoB;oCACpBnJ,MAAM;oCACNoJ,SAAS;oCACTC,WAAW;oCACXC,kBAAkB;oCAClBC,oBAAoB;oCACpBrE,MAAM,CAAC/M;wCACL,MAAMqR,WAAWrR,OAAOsR,gBAAgB;wCACxC,MAAMC,aAAad,sBAAsBY;wCACzC,IAAIE,YAAY;4CACd,OAAO,CAAC,cAAc,EAAEA,YAAY;wCACtC,OAAO;4CACL,MAAMC,OAAOvX,OAAOwX,UAAU,CAAC,QAAQC,MAAM,CAACL;4CAC9CG,KAAKE,MAAM,CAACL;4CACZ,OAAO,CAAC,cAAc,EAAEG,KAAKG,MAAM,CAAC,QAAQ;wCAC9C;oCACF;gCACF;gCACA,mCAAmC;gCACnCC,SAAS;gCACTC,gBAAgB;4BAClB;wBACF;oBACF;oBAEA,OAAO;gBACT;gBAEA,IAAIzM,gBAAgBF,cAAc;oBAChC,OAAO;wBACL4M,UAAU,GAAG5M,eAAe,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC;wBAC1D6L,QAAQ;wBACRG,WAAW;oBACb;gBACF;gBAEA,MAAMa,sBAAsB;oBAC1BhB,QAAQ;oBACRhE,MAAM;oBACN,6DAA6D;oBAC7DiF,OAAOvX;oBACPoN,MAAK7H,MAAW;wBACd,MAAMiS,WAAWjS,OAAOsR,gBAAgB,oBAAvBtR,OAAOsR,gBAAgB,MAAvBtR;wBACjB,OAAOiS,WACH9F,uBAAuB9K,IAAI,CAAC,CAAC6Q,UAC3BD,SAASE,UAAU,CAACD,YAEtB;oBACN;oBACAE,UAAU;oBACV,mEAAmE;oBACnE,wCAAwC;oBACxCC,SAAS;gBACX;gBAEA,MAAMC,gBAAgB;oBACpBzK,MAAK7H,MAIJ;4BAEIA;wBADH,OACE,GAACA,eAAAA,OAAOC,IAAI,qBAAXD,aAAamS,UAAU,CAAC,WACzBnS,OAAOuS,IAAI,KAAK,UAChB,oBAAoB1K,IAAI,CAAC7H,OAAOsR,gBAAgB,MAAM;oBAE1D;oBACAvE,MAAK/M,MAKJ;wBACC,MAAMwR,OAAOvX,OAAOwX,UAAU,CAAC;wBAC/B,IAAI1R,YAAYC,SAAS;4BACvBA,OAAOwS,UAAU,CAAChB;wBACpB,OAAO;4BACL,IAAI,CAACxR,OAAOyS,QAAQ,EAAE;gCACpB,MAAM,qBAEL,CAFK,IAAI5T,MACR,CAAC,iCAAiC,EAAEmB,OAAOC,IAAI,CAAC,uBAAuB,CAAC,GADpE,qBAAA;2CAAA;gDAAA;kDAAA;gCAEN;4BACF;4BACAuR,KAAKE,MAAM,CAAC1R,OAAOyS,QAAQ,CAAC;gCAAEpD,SAASnM;4BAAI;wBAC7C;wBAEA,wFAAwF;wBACxF,yHAAyH;wBACzH,0CAA0C;wBAC1C,IAAIlD,OAAOgS,KAAK,EAAE;4BAChBR,KAAKE,MAAM,CAAC1R,OAAOgS,KAAK;wBAC1B;wBAEA,OAAOR,KAAKG,MAAM,CAAC,OAAOe,SAAS,CAAC,GAAG;oBACzC;oBACAN,UAAU;oBACVlB,WAAW;oBACXF,oBAAoB;gBACtB;gBAEA,kBAAkB;gBAClB,OAAO;oBACL,oDAAoD;oBACpD,qDAAqD;oBACrD,oDAAoD;oBACpD,0CAA0C;oBAC1CD,QAAQzL,WAEJ,YAAY;oBACZ,mCACA,CAACqN,QACC,CAAC,iCAAiC9K,IAAI,CAAC8K,MAAM5F,IAAI;oBAEvD,mDAAmD;oBACnD8D,aAAavL,WACT,CAAC,IACD;wBACEsN,WAAWb;wBACXc,KAAKP;oBACP;oBACJlB,oBAAoB;oBACpBH,SAAS;gBACX;YACF,CAAA;YACA6B,cAAc9N,WACV;gBAAE+H,MAAM/R;YAAoC,IAC5C2L;YAEJoM,UACE,CAAC3P,OACA4B,CAAAA,YACCE,gBACCE,gBAAgBjC,OAAO+C,YAAY,CAAC8M,kBAAkB;YAC3DC,WAAW3N,WACP;gBACE,IAAKlH,CAAAA,eAAc,EAAE8U,0BAA0B,CAAE;oBAC/C,6BAA6B;oBAC7B,iEAAiE;oBACjEC,kBAAkB;wBAChBC,UAAU;4BACRC,QAAQ;4BACRC,aAAa;gCACX,mDAAmD;4BACrD;wBACF;wBACAC,QAAQ,CAAC9O,cAAc;4BAAE+O,UAAU;gCAAC;6BAAc;wBAAC;oBACrD;gBACF;gBACA,IAAKpV,CAAAA,eAAc,EAAEqV,iCAAiC,CAAE;oBACtD,8BAA8B;oBAC9BN,kBAAkB;wBAChBO,SAASlQ;oBACX;gBACF;aACD,GACD;gBACE,oBAAoB;gBACpB,CAACiE;oBACC,4BAA4B;oBAC5B,MAAM,EAAEkM,YAAY,EAAE,GACpBlV,QAAQ;oBACV,IAAIkV,aAAa;wBAAElP;oBAAW,GAAGmP,KAAK,CAACnM;gBACzC;gBACA,aAAa;gBACb,CAACA;oBACC,MAAM,EACJoM,kBAAkB,EACnB,GAAGpV,QAAQ;oBACZ,IAAIoV,mBAAmB;wBACrBC,gBAAgB;4BACdtG,KAAK;gCACH,+DAA+D;gCAC/D,+CAA+C;gCAC/C6F,QAAQ;gCACR,6DAA6D;gCAC7D,4DAA4D;gCAC5DU,YAAY;4BACd;wBACF;oBACF,GAAGH,KAAK,CAACnM;gBACX;aACD;QACP;QACA4H,SAASnM;QACT,8CAA8C;QAC9C8Q,OAAO;YACL,OAAO;gBACL,GAAIrI,gBAAgBA,gBAAgB,CAAC,CAAC;gBACtC,GAAG5H,WAAW;YAChB;QACF;QACAkQ,cAActU,OAAOC,MAAM,CAAC;YAC1B,GAAGF,gBAAgB;YACnBwU,IAAI,GAAE/Q,uBAAAA,OAAO8Q,YAAY,qBAAnB9Q,qBAAqBgR,cAAc;QAC3C;QACAC,QAAQ;YACN,sEAAsE;YACtE,kCAAkC;YAClCC,YAAY,GACVlR,OAAOmR,WAAW,GACdnR,OAAOmR,WAAW,CAACC,QAAQ,CAAC,OAC1BpR,OAAOmR,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,KAC7BrR,OAAOmR,WAAW,GACpB,GACL,OAAO,CAAC;YACTna,MAAM,CAACiJ,OAAOgC,eAAejL,KAAKoM,IAAI,CAACkF,YAAY,YAAYA;YAC/D,oCAAoC;YACpCqG,UAAUpM,0BACNtC,OAAO8B,eACL,CAAC,SAAS,CAAC,GACX,CAAC,YAAY,CAAC,GAChB,CAAC,cAAc,EAAElB,gBAAgB,cAAc,GAAG,MAAM,EACtDZ,MAAM,KAAKmB,SAAS,iBAAiB,iBACtC,GAAG,CAAC;YACTkQ,SAASzP,YAAYE,eAAe,SAASyB;YAC7C+N,eAAe1P,YAAYE,eAAe,WAAW;YACrDyP,wBAAwB;YACxBC,uBACE;YACF,uDAAuD;YACvDC,eAAenP,0BACX,cACA,CAAC,cAAc,EAAE1B,gBAAgB,cAAc,KAC7CZ,MAAM,WAAW,uBAClB,GAAG,CAAC;YACT0R,+BAA+B;YAC/BC,oBAAoB7H;YACpB,iEAAiE;YACjE,mGAAmG;YACnG,iEAAiE;YACjE,oGAAoG;YACpG,2FAA2F;YAC3F8H,+BAA+B5R,MAC3B,6BACAuD;YACJsO,2BAA2B;YAC3BC,cAAc;YACdC,kBAAkB;QACpB;QACAC,aAAa;QACbvU,SAASgL;QACTwJ,eAAe;YACb,+BAA+B;YAC/BxS,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAACyS,MAAM,CACN,CAACzS,OAAOkG;gBACN,4DAA4D;gBAC5DlG,KAAK,CAACkG,OAAO,GAAG5O,KAAKoM,IAAI,CAACgP,WAAW,WAAW,WAAWxM;gBAE3D,OAAOlG;YACT,GACA,CAAC;YAEHjB,SAAS;gBACP;mBACG1C;aACJ;YACD6M,SAAS,EAAE;QACb;QACA/L,QAAQ;YACNc,OAAO;gBACL,+EAA+E;gBAC/E;oBACE2O,aAAa;wBACX3H,IAAI;+BACCzN,eAAemb,KAAK,CAACC,UAAU;+BAC/Bpb,eAAemb,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA7U,SAAS;wBACP,6CAA6C;wBAC7CgC,OAAOnF,kCAAkC;oBAC3C;gBACF;gBACA;oBACE+R,aAAa;wBACXkG,KAAK;+BACAtb,eAAemb,KAAK,CAACC,UAAU;+BAC/Bpb,eAAemb,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA7U,SAAS;wBACP,6CAA6C;wBAC7CgC,OAAOnF,kCAAkC;oBAC3C;gBACF;gBACA,mEAAmE;gBACnE;oBACEmK,MAAM;wBACJ;wBACA;qBACD;oBACDkB,QAAQ;oBACR0G,aAAa;wBACX3H,IAAIzN,eAAemb,KAAK,CAACC,UAAU;oBACrC;oBACAzM,SAAS;wBACP4M,SACE;oBACJ;gBACF;gBACA;oBACE/N,MAAM;wBACJ;wBACA;qBACD;oBACDkB,QAAQ;oBACR0G,aAAa;wBACXkG,KAAK;+BACAtb,eAAemb,KAAK,CAACC,UAAU;+BAC/Bpb,eAAemb,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA1M,SAAS;wBACP4M,SACE;oBACJ;gBACF;gBACA,yFAAyF;gBACzF,iFAAiF;gBACjF,oCAAoC;gBACpC;oBACE/N,MAAM;wBACJ;wBACA;qBACD;oBACDkB,QAAQ;oBACR0G,aAAa;wBACX3H,IAAIzN,eAAemb,KAAK,CAACE,aAAa;oBACxC;gBACF;mBACItQ,eACA,EAAE,GACF;oBACE;wBACEyC,MAAM;wBACNkB,QAAQ;oBACV;iBACD;mBACDhD,YACA;oBACE;wBACE,uFAAuF;wBACvF,UAAU;wBACViM,OAAO3X,eAAewb,MAAM;wBAC5BhO,MAAM5I;oBACR;oBACA,4CAA4C;oBAC5C;wBACE6W,eAAe,IAAIvI,OACjBjT,yBAAyByb,aAAa;wBAExC/D,OAAO3X,eAAe8P,qBAAqB;oBAC7C;oBACA;wBACE,gEAAgE;wBAChE,2CAA2C;wBAC3C6H,OAAO3X,eAAeiQ,mBAAmB;wBACzCzC,MAAM;oBACR;oBACA;wBACE4H,aAAalV;wBACbsG,SAAS;4BACPgC,OAAOjF;wBACT;oBACF;oBACA;wBACE6R,aAAa/U;wBACbmG,SAAS;4BACPgC,OAAOhF,0BAA0B;wBACnC;oBACF;oBACA;wBACE4R,aAAajV;wBACbqG,SAAS;4BACPgC,OAAOhF,0BAA0B;wBACnC;oBACF;iBACD,GACD,EAAE;mBACFkI,aAAa,CAACf,WACd;oBACE;wBACEyK,aAAa/U;wBACbmN,MAAM;4BACJ,8DAA8D;4BAC9D,yBAAyB;4BACzBmO,KAAK;gCACHnI;gCACA;oCACE8H,KAAK;wCAACrI;wCAA4BrO;qCAAmB;gCACvD;6BACD;wBACH;wBACA6W,eAAe;4BACb,8DAA8D;4BAC9D,8DAA8D;4BAC9D,6DAA6D;4BAC7D,8DAA8D;4BAC9D,WAAW;4BACXH,KAAK;gCACH,IAAIpI,OAAOjT,yBAAyB2b,QAAQ;gCAC5C,IAAI1I,OAAOjT,yBAAyB4b,iBAAiB;6BACtD;wBACH;wBACArV,SAAS;4BACPwB,YAAY/E,aAAawG,cAAc;4BACvC9B,gBAAgB0J;4BAChB,mFAAmF;4BACnF,kFAAkF;4BAClF,8BAA8B;4BAC9B7I,OAAOlF,iBAAiByI,qBAAqB;gCAC3C,iCAAiC;gCACjClC;gCACA8N,OAAO3X,eAAe8P,qBAAqB;gCAC3CjF;4BACF;wBACF;wBACAhE,KAAK;oBACP;iBACD,GACD,EAAE;gBACN,kDAAkD;gBAClD,yDAAyD;mBACrD,CAACiC,OAAO+C,YAAY,CAAC1D,cAAc,GACnC;oBACE;wBACEqF,MAAM;wBACNhH,SAAS;4BACP2B,gBAAgB;wBAClB;oBACF;iBACD,GACD,EAAE;mBACFuD,aAAab,eACb;oBACE,sEAAsE;oBACtE,mEAAmE;oBACnE,oCAAoC;oBACpC;wBACE4Q,eAAe,IAAIvI,OACjBjT,yBAAyB6b,YAAY;wBAEvCnE,OAAO3X,eAAe8P,qBAAqB;oBAC7C;iBACD,GACD,EAAE;mBACFpE,YACA;oBACE;wBACE,8CAA8C;wBAC9C,kEAAkE;wBAClEqQ,OAAO;4BACL;gCACE3G,aAAa/U;gCACbmN,MAAM;oCACJ,8DAA8D;oCAC9D,yBAAyB;oCACzBmO,KAAK;wCACHnI;wCACA;4CACE8H,KAAK;gDAACrI;gDAA4BrO;6CAAmB;wCACvD;qCACD;gCACH;gCACA4B,SAAS;oCACP,8DAA8D;oCAC9D,4DAA4D;oCAC5DgC,OAAOlF,iBAAiByI,qBAAqB;wCAC3ClC;wCACA8N,OAAO3X,eAAe8P,qBAAqB;wCAC3CjF;oCACF;gCACF;4BACF;4BACA;gCACE2C,MAAMgG;gCACN4B,aAAapV,eAAeiQ,mBAAmB;gCAC/CzJ,SAAS;oCACPgC,OAAOlF,iBAAiByI,qBAAqB;wCAC3ClC;wCACA8N,OAAO3X,eAAeiQ,mBAAmB;wCACzCpF;oCACF;gCACF;4BACF;yBACD;oBACH;oBACA;wBACE2C,MAAMgG;wBACN4B,aAAapV,eAAemQ,eAAe;wBAC3C3J,SAAS;4BACPgC,OAAOlF,iBAAiByI,qBAAqB;gCAC3ClC;gCACA8N,OAAO3X,eAAemQ,eAAe;gCACrCtF;4BACF;wBACF;oBACF;iBACD,GACD,EAAE;gBACN,iFAAiF;mBAC7Ea,aAAa3C,OAAO4B,WACpB;oBACE;wBACE6C,MAAMD,cAAcC,IAAI;wBACxBG,SAAS;4BACP,+CAA+C;4BAC/CJ,cAAcI,OAAO;4BACrByF;4BACAzO;yBACD;wBACDyQ,aAAapV,eAAemQ,eAAe;wBAC3CtJ,KAAK8J;wBACLnK,SAAS;4BACPwB,YAAY/E,aAAawG,cAAc;wBACzC;oBACF;iBACD,GACD,EAAE;gBACN;oBACEsS,OAAO;wBACL;4BACE,GAAGxO,aAAa;4BAChB6H,aAAapV,eAAekR,OAAO;4BACnCrK,KAAKoK;4BACL,kDAAkD;4BAClD,8DAA8D;4BAC9D+K,QAAQ;gCACNC,KAAK;4BACP;wBACF;wBACA;4BACE,GAAG1O,aAAa;4BAChB6H,aAAapV,eAAekc,OAAO;4BACnCrV,KAAKoK;wBAGP;wBACA;4BACEzD,MAAMD,cAAcC,IAAI;4BACxB4H,aAAapV,eAAe0Q,UAAU;4BACtC7J,KAAK4J;4BACLjK,SAAS;gCACPwB,YAAY/E,aAAawG,cAAc;gCACvC9B,gBAAgB0J;gCAChB7I,OAAOlF,iBAAiByI,qBAAqB;oCAC3ClC;oCACA8N,OAAO3X,eAAe0Q,UAAU;oCAChC7F;gCACF;4BACF;wBACF;wBACA;4BACE2C,MAAMD,cAAcC,IAAI;4BACxB4H,aAAapV,eAAemc,UAAU;4BACtCtV,KAAK2J;4BACLhK,SAAS;gCACPwB,YAAY/E,aAAawG,cAAc;gCACvC9B,gBAAgB0J;gCAChB7I,OAAOlF,iBAAiByI,qBAAqB;oCAC3ClC;oCACA8N,OAAO3X,eAAemc,UAAU;oCAChCtR;gCACF;4BACF;wBACF;2BACIa,YACA;4BACE;gCACE8B,MAAMD,cAAcC,IAAI;gCACxB4H,aAAa/U;gCACbsN,SAAS/I;gCACTiC,KAAK0J;4BACP;4BACA;gCACE/C,MAAMD,cAAcC,IAAI;gCACxBiO,eAAe,IAAIvI,OACjBjT,yBAAyB6b,YAAY;gCAEvCjV,KAAK0J;4BACP;4BACA;gCACE/C,MAAMD,cAAcC,IAAI;gCACxB4H,aAAapV,eAAemQ,eAAe;gCAC3C,uEAAuE;gCACvExC,SAASjJ;gCACTmC,KAAKkK;gCACLvK,SAAS;oCACPwB,YAAY/E,aAAawG,cAAc;gCACzC;4BACF;4BACA;gCACE+D,MAAMD,cAAcC,IAAI;gCACxB4H,aAAapV,eAAeiQ,mBAAmB;gCAC/CtC,SAAS/I;gCACTiC,KAAKmK;gCACLxK,SAAS;oCACPwB,YAAY/E,aAAawG,cAAc;gCACzC;4BACF;yBACD,GACD,EAAE;wBACN;4BACE,GAAG8D,aAAa;4BAChB1G,KAAK;mCACA8J;gCACHN,eAAeC,KAAK;gCACpBtC;6BACD,CAAC7I,MAAM,CAAC+F;wBACX;qBACD;gBACH;mBAEI,CAACpC,OAAOsT,MAAM,CAACC,mBAAmB,GAClC;oBACE;wBACE7O,MAAM7E;wBACN+F,QAAQ;wBACR4N,QAAQ;4BAAEhB,KAAKxZ;wBAAa;wBAC5Bya,YAAY;4BAAEjB,KAAK;gCAAC;6BAAM;wBAAC;wBAC3BG,eAAe;4BACbH,KAAK;gCACH,IAAIpI,OAAOjT,yBAAyB2b,QAAQ;gCAC5C,IAAI1I,OAAOjT,yBAAyByb,aAAa;gCACjD,IAAIxI,OAAOjT,yBAAyB4b,iBAAiB;6BACtD;wBACH;wBACAlN,SAAS;4BACP6N,OAAOzT;4BACPU;4BACAgT,UAAU3T,OAAO2T,QAAQ;4BACzBxC,aAAanR,OAAOmR,WAAW;wBACjC;oBACF;iBACD,GACD,EAAE;mBACFpP,eACA;oBACE;wBACErE,SAAS;4BACPgB,UAAU;gCACR1C,SAASV,QAAQoC,OAAO,CAAC;4BAC3B;wBACF;oBACF;iBACD,GACDmE,WACE;oBACE;wBACEnE,SAAS;4BACPgB,UACEsB,OAAO+C,YAAY,CAAC6Q,qBAAqB,KAAK,QAC1C;gCACEC,QAAQ;gCACRC,QAAQ;gCACRC,WAAW;gCACXjd,QAAQ;gCACRkd,QAAQ;gCACRC,MAAM;gCACNC,OAAO;gCACPC,IAAI;gCACJnd,MAAM;gCACNod,UAAU;gCACVpY,SAAS;gCACTqY,aAAa;gCACbC,QAAQ;gCACRC,gBAAgB;gCAChBC,KAAK;gCACLC,QAAQ;gCACRC,KAAK;gCACLC,MAAM;gCACNC,IAAI;gCACJC,MAAM;gCACNC,QAAQ;gCACRC,cAAc;4BAChB,IACA;gCACElB,QAAQvY,QAAQoC,OAAO,CACrB;gCAEFoW,QAAQxY,QAAQoC,OAAO,CACrB;gCAEFqW,WAAWzY,QAAQoC,OAAO,CACxB;gCAEF5G,QAAQwE,QAAQoC,OAAO,CACrB;gCAEFsW,QAAQ1Y,QAAQoC,OAAO,CACrB;gCAEFuW,MAAM3Y,QAAQoC,OAAO,CACnB;gCAEFwW,OAAO5Y,QAAQoC,OAAO,CACpB;gCAEFyW,IAAI7Y,QAAQoC,OAAO,CACjB;gCAEF1G,MAAMsE,QAAQoC,OAAO,CACnB;gCAEF0W,UAAU9Y,QAAQoC,OAAO,CACvB;gCAEF1B,SAASV,QAAQoC,OAAO,CAAC;gCACzB,4BAA4B;gCAC5B2W,aAAa/Y,QAAQoC,OAAO,CAC1B;gCAEF4W,QAAQhZ,QAAQoC,OAAO,CACrB;gCAEF6W,gBAAgBjZ,QAAQoC,OAAO,CAC7B;gCAEF8W,KAAKlZ,QAAQoC,OAAO,CAAC;gCACrB+W,QAAQnZ,QAAQoC,OAAO,CACrB;gCAEFgX,KAAKpZ,QAAQoC,OAAO,CAClB;gCAEF,4BAA4B;gCAC5B,+BAA+B;gCAC/BiX,MAAMrZ,QAAQoC,OAAO,CAAC;gCACtBkX,IAAItZ,QAAQoC,OAAO,CACjB;gCAEFmX,MAAMvZ,QAAQoC,OAAO,CACnB;gCAEFoX,QAAQxZ,QAAQoC,OAAO,CACrB;gCAEFqX,cAAczZ,QAAQoC,OAAO,CAC3B;4BAEJ;wBACR;oBACF;iBACD,GACD,EAAE;gBACR;oBACE,oEAAoE;oBACpE,6BAA6B;oBAC7BgH,MAAM;oBACNsQ,aAAa;gBACf;gBACA,0EAA0E;gBAC1E,yEAAyE;gBACzE,4GAA4G;gBAC5G,gEAAgE;gBAChE,0DAA0D;gBAC1D,iFAAiF;gBACjF,iFAAiF;gBACjF;oBACEtQ,MAAM;oBACNsQ,aAAa;gBACf;gBACA;oBACE,uEAAuE;oBACvE,uEAAuE;oBACvE,mDAAmD;oBACnD,iEAAiE;oBACjE,mEAAmE;oBACnE,qEAAqE;oBACrE,4DAA4D;oBAC5DtQ,MAAM;oBACN3G,KAAK,CAAC,EAAE4U,aAAa,EAA6B;4BAE9CA;wBADF,MAAMsC,QAAQ,AACZtC,CAAAA,EAAAA,uBAAAA,cAAclF,KAAK,CAAC,uCAApBkF,oBAAwC,CAAC,EAAE,KAAI,EAAC,EAChDxW,KAAK,CAAC;wBAER,OAAO;4BACL;gCACEyJ,QAAQ;gCACRC,SAAS;oCACPoP;oCACAhP,aAAajP,KAAKoM,IAAI,CACpBrD,KACAC,CAAAA,0BAAAA,OAAQmD,OAAO,KAAI,SACnB,SACA;gCAEJ;gCACA,gEAAgE;gCAChE,2DAA2D;gCAC3D,gBAAgB;gCAChB+R,OAAO,wBAAwBvC;4BACjC;yBACD;oBACH;gBACF;gBACA;oBACEjV,SAAS;wBACPgC,OAAO;4BACLyV,MAAMpa;wBACR;oBACF;gBACF;aACD;QACH;QACA6N,SAAS;YACP3G,gBACE,IAAIL,QAAQwT,6BAA6B,CACvC,6BACA,SAAUtG,QAAQ;gBAChB,MAAMuG,aAAare,KAAKse,QAAQ,CAC9BxG,SAAS3C,OAAO,EAChB;gBAEF,MAAM0C,QAAQC,SAAS1C,WAAW,CAACE,WAAW;gBAC9C,IAAIiJ;gBAEJ,OAAQ1G;oBACN,KAAK3X,eAAeiQ,mBAAmB;oBACvC,KAAKjQ,eAAe8P,qBAAqB;oBACzC,KAAK9P,eAAemQ,eAAe;oBACnC,KAAKnQ,eAAese,aAAa;wBAC/BD,UAAU;wBACV;oBACF;wBACEA,UAAU;gBACd;gBACAzG,SAAS3C,OAAO,GAAG,CAAC,+BAA+B,EAAEoJ,QAAQ,mBAAmB,EAAEF,YAAY;YAChG;YAEJpV,OAAO,IAAIpG,wBAAwB;gBAAE4b,gBAAgB;YAAE;YACvDxV,OACE4B,YACCM,CAAAA,WAEG,IAAKjH,CAAAA,uBAAsB,MAC3B,IAAIvE,0BAA0BI,QAAO;YAC3C,6GAA6G;YAC5G8K,CAAAA,YAAYE,YAAW,KACtB,IAAIH,QAAQ8T,aAAa,CAAC;gBACxB,0CAA0C;gBAC1CC,QAAQ;oBAACra,QAAQoC,OAAO,CAAC;oBAAW;iBAAS;gBAC7C,sDAAsD;gBACtD,GAAImE,YAAY;oBAAE7F,SAAS;wBAACV,QAAQoC,OAAO,CAAC;qBAAW;gBAAC,CAAC;YAC3D;YACF1D,mBAAmB;gBACjB4b,aAAa;gBACb5V;gBACAC;gBACAkD;gBACA3B;gBACAgB;gBACAX;gBACAE;gBACAQ;gBACAN;gBACAZ;gBACAwU,sBAAsBnU;YACxB;YACAG,YACE,IAAI/I,oBAAoB;gBACtB6V,UAAU5W;gBACV+I;gBACAM;gBACA0U,cAAc,CAAC,OAAO,EAAEhe,mCAAmC,GAAG,CAAC;gBAC/DmI;YACF;YACF,oDAAoD;YACpD,CAACkC,YAAaN,CAAAA,YAAYE,YAAW,KAAM,IAAIpJ;YAC/CsJ,gBACE,CAAChC,OACD,IAAK3E,CAAAA,QAAQ,kDAAiD,EAC3Dya,sBAAsB,CACvB;gBACEhQ,SAAShG;gBACTqB,QAAQA;gBACRN,UAAUA;gBACV+K,cAAc7L,OAAO+C,YAAY,CAAC8I,YAAY;gBAC9CmK,uBAAuBhW,OAAOgW,qBAAqB;gBACnDC,eAAerT;gBACfsT,cAAc,EAAE;gBAChBvV;YACF;YAEJ,4EAA4E;YAC5E,yEAAyE;YACzE,0EAA0E;YAC1E,kEAAkE;YAClEX,OAAOmW,2BAA2B,IAChC,IAAIvU,QAAQwU,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;eACErW,MACA,AAAC,CAAA;gBACC,0FAA0F;gBAC1F,qGAAqG;gBACrG,MAAM,EAAEsW,6BAA6B,EAAE,GACrCjb,QAAQ;gBACV,MAAMkb,aAAoB;oBACxB,IAAID,8BAA8B;wBAChCzP,kBAAkBlE;oBACpB;iBACD;gBAED,IAAIf,YAAYE,cAAc;oBAC5ByU,WAAWnS,IAAI,CAAC,IAAIzC,QAAQ6U,0BAA0B;gBACxD;gBAEA,OAAOD;YACT,CAAA,MACA,EAAE;YACN,CAACvW,OACC,IAAI2B,QAAQwU,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;YACF/T,2BACE,IAAI3J,oBAAoB;gBACtBqH;gBACAgW,eAAerT;gBACf8T,eAAe3U;gBACfoB,SAAS,CAAClD,MAAMkD,UAAUK;YAC5B;YACF,iEAAiE;YACjE,wDAAwD;YACxDzB,gBACE,IAAIzJ,iBAAiB;gBACnB2H;gBACA0W,YAAY,CAAC1W,OAAO,CAAC,GAACD,2BAAAA,OAAO+C,YAAY,CAAC6T,GAAG,qBAAvB5W,yBAAyB6W,SAAS;gBACxD7V;gBACA8V,kBAAkB;oBAChBC,iBAAiBtW;oBACjBuW,oCAAoCtW;oBACpC,GAAGe,gBAAgB;gBACrB;YACF;YACFI,YACE,IAAIpJ,oBAAoB;gBACtBgI;gBACAO;gBACAH;gBACAoV,eAAerT;gBACfrB;YACF;YACFY,WACI,IAAIhH,sBAAsB;gBAAEgG;YAAe,KAC3C,IAAItI,gBAAgB;gBAAEsI;gBAAgB4E,SAAShG;YAAI;YACvD,IAAIhH;YACJ8I,YACE,IAAI5I,eAAe;gBACjB,yDAAyD;gBACzDge,UAAU3b,QAAQoC,OAAO,CAAC;gBAC1BwZ,UAAUlb,QAAQC,GAAG,CAACkb,cAAc;gBACpCvN,MAAM,CAAC,uBAAuB,EAAE3J,MAAM,KAAK,UAAU,GAAG,CAAC;gBACzD2P,UAAU;gBACVhM,MAAM;oBACJ,CAACjM,6CAA6C,EAAE;oBAChD,gCAAgC;oBAChCyf,WAAW;gBACb;YACF;YACFxU,aAAaf,YAAY,IAAIpI,uBAAuB;gBAAEwG;YAAI;YAC1D2C,aACGf,CAAAA,WACG,IAAI3I,8BAA8B;gBAChC+G;gBACAmB;gBACAiW,uBAAuB,CAAC,CAACrX,OAAO+C,YAAY,CAACuU,SAAS;YACxD,KACA,IAAIne,wBAAwB;gBAC1BiI;gBACAnB;gBACA8B;gBACArB;YACF,EAAC;YACPkC,aACE,CAACf,YACD,IAAIvI,gBAAgB;gBAClByG;gBACAoD,SAASnD,OAAOmD,OAAO;gBACvB/B;gBACAnB;gBACA8B;gBACAsG,gBAAgBrI,OAAOqI,cAAc;gBACrCrF,aAAaF;gBACbyU,iBAAiBvX,OAAO+C,YAAY,CAACyU,SAAS;gBAC9CvW;gBACAC;YACF;YACF,CAACjB,OACC4B,YACA,CAAC,GAAC7B,4BAAAA,OAAO+C,YAAY,CAAC6T,GAAG,qBAAvB5W,0BAAyB6W,SAAS,KACpC,IAAInd,2BAA2BsG,OAAO+C,YAAY,CAAC6T,GAAG,CAACC,SAAS;YAClEhV,YACE,IAAIlI,uBAAuB;gBACzByH;YACF;YACF,CAACe,YACC,CAAClC,OACD4B,YACA7B,OAAO+C,YAAY,CAAC0U,WAAW,IAC/B,IAAI7c,kBAAkBoF,OAAO+C,YAAY,CAAC0U,WAAW,KAAK;YAC5DvM;YACA,CAACjL,OACCgC,gBACA,IAAI,AACF3G,CAAAA,QAAQ,sDAAqD,EAC7D6P,eAAe,CAAC,IAAIC;YACxBR,mCACE,IAAI,AACFtP,CAAAA,QAAQ,iDAAgD,EACxDmT,OAAO,CAAC;gBACR9N;gBACA,GAAGX,OAAO+C,YAAY,CAAC8H,mBAAmB;YAC5C;SACH,CAACxO,MAAM,CAAC+F;IACX;IAEA,wCAAwC;IACxC,mEAAmE;IACnE,IAAIhC,mBAAmB,CAACA,gBAAgBsX,UAAU,EAAE;YAClDna,gCAAAA;SAAAA,0BAAAA,cAAcG,OAAO,sBAArBH,iCAAAA,wBAAuBkB,OAAO,qBAA9BlB,+BAAgC8G,IAAI,CAACjE,gBAAgBuX,OAAO;IAC9D;KAIApa,yBAAAA,cAAcG,OAAO,sBAArBH,iCAAAA,uBAAuBqL,OAAO,qBAA9BrL,+BAAgCqa,OAAO,CACrC,IAAIlf,oBACFwH,CAAAA,6BAAAA,6BAAAA,SAAUsG,eAAe,qBAAzBtG,2BAA2BoJ,KAAK,KAAI,CAAC,GACrClJ;IAIJ,MAAMuB,iBAAiBpE;IAEvB,IAAIwE,cAAc;YAChBJ,8BAAAA,wBAMAA,+BAAAA,yBAMAA,+BAAAA;SAZAA,yBAAAA,eAAe9E,MAAM,sBAArB8E,+BAAAA,uBAAuBhE,KAAK,qBAA5BgE,6BAA8BiW,OAAO,CAAC;YACpClT,MAAM;YACNkB,QAAQ;YACR9I,MAAM;YACN6V,eAAe;QACjB;SACAhR,0BAAAA,eAAe9E,MAAM,sBAArB8E,gCAAAA,wBAAuBhE,KAAK,qBAA5BgE,8BAA8BiW,OAAO,CAAC;YACpCnE,YAAY;YACZ7N,QAAQ;YACR9I,MAAM;YACN+R,OAAO3X,eAAe2gB,SAAS;QACjC;SACAlW,0BAAAA,eAAe9E,MAAM,sBAArB8E,gCAAAA,wBAAuBhE,KAAK,qBAA5BgE,8BAA8BiW,OAAO,CAAC;YACpCtL,aAAapV,eAAe2gB,SAAS;YACrC/a,MAAM;QACR;IACF;IAEA6E,eAAemW,WAAW,GAAG;QAC3BC,QAAQ;QACRC,iBAAiB;QACjBC,WAAWja,MAAMC,OAAO,CAAC+B,OAAO+C,YAAY,CAACmV,UAAU,IACnD;YACEC,aAAanY,OAAO+C,YAAY,CAACmV,UAAU;YAC3CE,eAAephB,KAAKoM,IAAI,CAACrD,KAAK;YAC9BsY,kBAAkBrhB,KAAKoM,IAAI,CAACrD,KAAK;QACnC,IACAC,OAAO+C,YAAY,CAACmV,UAAU,GAC5B;YACEE,eAAephB,KAAKoM,IAAI,CAACrD,KAAK;YAC9BsY,kBAAkBrhB,KAAKoM,IAAI,CAACrD,KAAK;YACjC,GAAGC,OAAO+C,YAAY,CAACmV,UAAU;QACnC,IACA1U;IACR;IAEA7B,eAAe9E,MAAM,CAAEqW,MAAM,GAAG;QAC9BoF,YAAY;YACVnF,KAAK;QACP;IACF;IACAxR,eAAe9E,MAAM,CAAE0b,SAAS,GAAG;QACjCC,OAAO;YACL7J,UAAU;QACZ;IACF;IAEA,IAAI,CAAChN,eAAesP,MAAM,EAAE;QAC1BtP,eAAesP,MAAM,GAAG,CAAC;IAC3B;IACA,IAAIpP,UAAU;QACZF,eAAesP,MAAM,CAACwH,YAAY,GAAG;IACvC;IAEA,IAAI5W,YAAYE,cAAc;QAC5BJ,eAAesP,MAAM,CAACyH,mBAAmB,GAAG;YAAC;SAAS;IACxD;IAEA,iDAAiD;IACjD,wDAAwD;IACxD,oDAAoD;IACpD/W,eAAegX,QAAQ,GAAG,CAAC;IAC3B,IAAI3c,QAAQoK,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCxE,eAAegX,QAAQ,CAACC,YAAY,GAAG;YACrC;SACD;IACH,OAAO;QACLjX,eAAegX,QAAQ,CAACC,YAAY,GAAG;YAAC;SAA+B;IACzE;IACA,IAAI5c,QAAQoK,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCxE,eAAegX,QAAQ,CAACE,cAAc,GAAG;YACvC;SACD;IACH;IAEA,IAAI5Y,KAAK;QACP,IAAI,CAAC0B,eAAesL,YAAY,EAAE;YAChCtL,eAAesL,YAAY,GAAG,CAAC;QACjC;QAEA,2EAA2E;QAC3E,2CAA2C;QAC3C,IAAI,CAACrK,WAAW;YACdjB,eAAesL,YAAY,CAAC6L,eAAe,GAAG;QAChD;QACAnX,eAAesL,YAAY,CAAC8L,WAAW,GAAG;IAC5C;IAEA,MAAMC,aAAaC,KAAKC,SAAS,CAAC;QAChC/U,sBAAsB,EAAEnE,2BAAAA,wBAAAA,OAAQ+C,YAAY,qBAApB/C,sBAAsBmE,sBAAsB;QACpE4F,aAAa/J,OAAO+J,WAAW;QAC/B1B,gBAAgBA;QAChB8Q,eAAenZ,OAAOmZ,aAAa;QACnCC,uBACEpZ,OAAOqZ,aAAa,KAAK,QACrB7V,YACAxD,OAAOqZ,aAAa,CAACC,QAAQ;QACnCC,6BAA6B,CAAC,CAACvZ,OAAOuZ,2BAA2B;QACjEC,iBAAiBxZ,OAAOwZ,eAAe;QACvCC,aAAazZ,OAAO+C,YAAY,CAAC0W,WAAW;QAC5CC,mBAAmB1Z,OAAO+C,YAAY,CAAC2W,iBAAiB;QACxDC,mBAAmB3Z,OAAO+C,YAAY,CAAC4W,iBAAiB;QACxD3W,aAAahD,OAAO+C,YAAY,CAACC,WAAW;QAC5C2Q,UAAU3T,OAAO2T,QAAQ;QACzBwC,6BAA6BnW,OAAOmW,2BAA2B;QAC/DhF,aAAanR,OAAOmR,WAAW;QAC/BtO;QACA6T,eAAe3U;QACfhB;QACAhK,SAAS,CAAC,CAACiJ,OAAOjJ,OAAO;QACzByL;QACAoX,WAAWvW;QACXmI,aAAa,GAAExL,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiBwL,aAAa;QAC7CD,qBAAqB,GAAEvL,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiBuL,qBAAqB;QAC7DD,gBAAgB,GAAEtL,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiBsL,gBAAgB;QACnDD,KAAK,GAAErL,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiBqL,KAAK;QAC7BK,OAAO,GAAE1L,oBAAAA,OAAOsE,QAAQ,qBAAftE,kBAAiB0L,OAAO;QACjCpF,mBAAmBtG,OAAOsG,iBAAiB;QAC3CuT,iBAAiB7Z,OAAOsT,MAAM,CAACwG,UAAU;QACzCC,qBAAqB/Z,OAAO+C,YAAY,CAACgX,mBAAmB;QAC5DC,kBAAkBha,OAAO+C,YAAY,CAACiX,gBAAgB;QACtD9T,yBAAyBxF;IAC3B;IAEA,MAAMuZ,QAAa;QACjBnd,MAAM;QACN,mFAAmF;QACnFod,sBAAsBja,MAAM,IAAIka;QAChC,YAAY;QACZ,kHAAkH;QAClH,qBAAqB;QACrB,iDAAiD;QACjD1e,SAAS,GAAG2W,UAAU,CAAC,EAAEpW,QAAQC,GAAG,CAACkb,cAAc,CAAC,CAAC,EAAE6B,YAAY;QACnEoB,gBAAgBpjB,KAAKoM,IAAI,CAACD,SAAS,SAAS;QAC5C,gIAAgI;QAChI,8GAA8G;QAC9G,yGAAyG;QACzG,kEAAkE;QAClEkX,aAAapa,MAAM,SAAS;IAC9B;IAEA,oFAAoF;IACpF,IAAID,OAAOjJ,OAAO,IAAIiJ,OAAO8I,UAAU,EAAE;QACvCmR,MAAMK,iBAAiB,GAAG;YACxBta,QAAQ;gBAACA,OAAO8I,UAAU;aAAC;YAC3B,uGAAuG;YACvGyR,gBAAgB,EAAE;QACpB;IACF,OAAO;QACLN,MAAMK,iBAAiB,GAAG;YACxB,uGAAuG;YACvGC,gBAAgB,EAAE;QACpB;IACF;KACA5Y,0BAAAA,eAAeiH,OAAO,qBAAtBjH,wBAAwB0C,IAAI,CAAC,CAACC;QAC5BA,SAASkW,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,2BAA2B,CAACC;YAClD,MAAML,oBAAoBK,MAAMC,WAAW,CAACN,iBAAiB;YAC7D,MAAMO,cAAc7jB,KAAK8jB,OAAO,CAACxf,QAAQoC,OAAO,CAAC;YACjD,sFAAsF;YACtF,2EAA2E;YAC3E,KAAK,MAAMqd,OAAOT,kBAAmB;gBACnC,IAAIS,IAAI/L,UAAU,CAAC6L,cAAc;oBAC/BP,kBAAkBU,MAAM,CAACD;gBAC3B;YACF;QACF;IACF;IAEApZ,eAAesY,KAAK,GAAGA;IAEvB,IAAIje,QAAQC,GAAG,CAACgf,oBAAoB,EAAE;QACpC,MAAMC,QAAQlf,QAAQC,GAAG,CAACgf,oBAAoB,CAAC7W,QAAQ,CAAC;QACxD,MAAM+W,gBACJnf,QAAQC,GAAG,CAACgf,oBAAoB,CAAC7W,QAAQ,CAAC;QAC5C,MAAMgX,gBACJpf,QAAQC,GAAG,CAACgf,oBAAoB,CAAC7W,QAAQ,CAAC;QAC5C,MAAMiX,gBACJrf,QAAQC,GAAG,CAACgf,oBAAoB,CAAC7W,QAAQ,CAAC;QAC5C,MAAMkX,gBACJtf,QAAQC,GAAG,CAACgf,oBAAoB,CAAC7W,QAAQ,CAAC;QAE5C,MAAMmX,UACJ,AAACJ,iBAAiBtZ,YAAcuZ,iBAAiB7Y;QACnD,MAAMiZ,UACJ,AAACH,iBAAiBxZ,YAAcyZ,iBAAiB/Y;QAEnD,MAAMkZ,aAAa,CAACP,SAAS,CAACK,WAAW,CAACC;QAE1C,IAAIC,cAAcP,OAAO;YACvBvZ,eAAe+Z,qBAAqB,GAAG;gBACrCC,OAAO;gBACPC,OAAO;YACT;QACF;QAEA,IAAIH,cAAcF,SAAS;YACzB5Z,eAAeiH,OAAO,CAAEvE,IAAI,CAAC,CAACC;gBAC5BA,SAASkW,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/C1d,QAAQ4e,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbC,QAAQ;wBACRC,SAASP,aAAa,QAAQ;oBAChC;gBAEJ;YACF;QACF,OAAO,IAAID,SAAS;YAClB7Z,eAAeiH,OAAO,CAAEvE,IAAI,CAAC,CAACC;gBAC5BA,SAASkW,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/C1d,QAAQ4e,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbG,QAAQ;wBACRF,QAAQ;wBACRG,SAAS;oBACX;gBAEJ;YACF;QACF;QAEA,IAAIX,SAAS;YACX,MAAMY,iBACJplB,QAAQolB,cAAc;YACxBxa,eAAeiH,OAAO,CAAEvE,IAAI,CAC1B,IAAI8X,eAAe;gBACjBZ,SAAS;YACX;YAEF5Z,eAAe4Z,OAAO,GAAG;QAC3B;IACF;IAEAhe,gBAAgB,MAAMlF,mBAAmBkF,eAAe;QACtD8C;QACA+b,eAAerc;QACfsc,eAAevb,WACX,IAAIsJ,OAAOnT,mBAAmBD,KAAKoM,IAAI,CAACtC,UAAU,CAAC,IAAI,CAAC,MACxD0C;QACJZ;QACA0Z,eAAerc;QACf6F,UAAUvD;QACVmU,eAAe3U;QACfwa,WAAW1a,YAAYE;QACvBoP,aAAanR,OAAOmR,WAAW,IAAI;QACnCqL,aAAaxc,OAAOwc,WAAW;QAC/BjD,6BAA6BvZ,OAAOuZ,2BAA2B;QAC/DkD,QAAQzc,OAAOyc,MAAM;QACrB1Z,cAAc/C,OAAO+C,YAAY;QACjCwQ,qBAAqBvT,OAAOsT,MAAM,CAACC,mBAAmB;QACtDvP,mBAAmBhE,OAAOgE,iBAAiB;QAC3CgW,kBAAkBha,OAAO+C,YAAY,CAACiX,gBAAgB;IACxD;IAEA,0BAA0B;IAC1Bzc,cAAc0c,KAAK,CAACrQ,IAAI,GAAG,GAAGrM,cAAcqM,IAAI,CAAC,CAAC,EAAErM,cAAcmf,IAAI,GACpE7b,gBAAgB,cAAc,IAC9B;IAEF,IAAIZ,KAAK;QACP,IAAI1C,cAAcV,MAAM,EAAE;YACxBU,cAAcV,MAAM,CAAC8f,WAAW,GAAG,CAAC9f,SAClC,CAAC0D,mBAAmBmE,IAAI,CAAC7H,OAAOiS,QAAQ;QAC5C,OAAO;YACLvR,cAAcV,MAAM,GAAG;gBACrB8f,aAAa,CAAC9f,SAAgB,CAAC0D,mBAAmBmE,IAAI,CAAC7H,OAAOiS,QAAQ;YACxE;QACF;IACF;IAEA,IAAI8N,kBAAkBrf,cAAcP,OAAO;IAC3C,IAAI,OAAOgD,OAAOjJ,OAAO,KAAK,YAAY;YACdwG,wBA0CtBoE,6BAKKA;QA/CT,MAAMkb,qBAAoBtf,yBAAAA,cAAcqL,OAAO,qBAArBrL,uBAAuBmF,MAAM;QAEvDnF,gBAAgByC,OAAOjJ,OAAO,CAACwG,eAAe;YAC5CwC;YACAE;YACA6F,UAAUvD;YACV9B;YACAT;YACAuH;YACAuV,YAAYtgB,OAAOqN,IAAI,CAACjJ,aAAa8B,MAAM;YAC3C3L;YACA,GAAIwL,0BACA;gBACEwa,aAAahb,eAAe,SAAS;YACvC,IACA,CAAC,CAAC;QACR;QAEA,IAAImJ,mBAAmB2R,mBAAmB;gBACftf;YAAzB,MAAMyf,oBAAmBzf,0BAAAA,cAAcqL,OAAO,qBAArBrL,wBAAuBmF,MAAM;YACtD,IAAIsa,kBAAkB;gBACpB,MAAMC,iBAAiBD,qBAAqBH;gBAC5C3R,gBAAgBgS,QAAQ,CAAC,kBAAkBD,iBAAiB,IAAI;YAClE;QACF;QAEA,IAAI,CAAC1f,eAAe;YAClB,MAAM,qBAGL,CAHK,IAAI7B,MACR,CAAC,6GAA6G,EAAEsE,OAAOmd,cAAc,CAAC,GAAG,CAAC,GACxI,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIld,OAAO2c,oBAAoBrf,cAAcP,OAAO,EAAE;YACpDO,cAAcP,OAAO,GAAG4f;YACxB7f,qBAAqB6f;QACvB;QAEA,wDAAwD;QACxD,MAAMjb,iBAAiBpE;QAEvB,0EAA0E;QAC1E,IAAIoE,EAAAA,8BAAAA,eAAemW,WAAW,qBAA1BnW,4BAA4Byb,eAAe,MAAK,MAAM;YACxDzb,eAAemW,WAAW,CAACsF,eAAe,GAAG;gBAC3CC,SAAS;YACX;QACF,OAAO,IACL,SAAO1b,+BAAAA,eAAemW,WAAW,qBAA1BnW,6BAA4Byb,eAAe,MAAK,YACvDzb,eAAemW,WAAW,CAACsF,eAAe,CAACC,OAAO,KAAK,OACvD;YACA1b,eAAemW,WAAW,CAACsF,eAAe,CAACC,OAAO,GAAG;QACvD;QAEA,IAAI,OAAO,AAAC9f,cAAsB+f,IAAI,KAAK,YAAY;YACrDrgB,QAAQC,IAAI,CACV;QAEJ;IACF;IACA,MAAMS,QAAQJ,EAAAA,wBAAAA,cAAcV,MAAM,qBAApBU,sBAAsBI,KAAK,KAAI,EAAE;IAE/C,MAAM4f,gBAAgB5f,MAAM6f,IAAI,CAC9B,CAAC3f,OACC,AAACA,QACC,OAAOA,SAAS,YAChBA,KAAK+H,MAAM,KAAK,uBAChB,UAAU/H,QACVA,KAAK6G,IAAI,YAAY0F,UACrBvM,KAAK6G,IAAI,CAACA,IAAI,CAAC,WACjB;IAGJ,IAAI6Y,iBAAiB3a,WAAW;QAC9B,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1CjF,MAAM0G,IAAI,CAAC;YACTK,MAAM6Y,cAAc7Y,IAAI;YACxBuO,OAAO;gBACL/b,eAAe8P,qBAAqB;gBACpC9P,eAAeiQ,mBAAmB;gBAClCjQ,eAAemQ,eAAe;aAC/B,CAACgD,GAAG,CAAC,CAACwE,QAAW,CAAA;oBAChBvC,aAAauC;oBACbnR,SAAS;wBACPgC,OAAOlF,iBAAiByI,qBAAqB;4BAC3ClC;4BACA8N;4BACA9M;wBACF;oBACF;gBACF,CAAA;QACF;IACF;IAEA,IAAI,CAAC/B,OAAOsT,MAAM,CAACC,mBAAmB,EAAE;QACtC,MAAMkK,gBAAgB9f,MAAM6f,IAAI,CAC9B,CAAC3f,OACCA,QAAQ,OAAOA,SAAS,YAAYA,KAAK+H,MAAM,KAAK;QAExD,IAAI2X,iBAAiBE,iBAAiB,OAAOA,kBAAkB,UAAU;YACvE,uDAAuD;YACvD,wDAAwD;YACxD,8CAA8C;YAC9CA,cAAc/Y,IAAI,GAAG;QACvB;IACF;IAEA,IACE1E,OAAO+C,YAAY,CAAC2a,SAAS,MAC7BngB,yBAAAA,cAAcV,MAAM,qBAApBU,uBAAsBI,KAAK,KAC3BJ,cAAcqL,OAAO,EACrB;QACA,kEAAkE;QAClE,iEAAiE;QACjE,kJAAkJ;QAClJ,MAAM+U,oBAAoB;YAAC;SAA8B;QACzD,MAAMC,aAAa;YACjB/Y,SAAS8Y;YACTnK,QAAQmK;YACR7gB,MAAM;QACR;QAEA,MAAM+gB,WAAW,EAAE;QACnB,MAAMC,aAAa,EAAE;QAErB,KAAK,MAAMjgB,QAAQN,cAAcV,MAAM,CAACc,KAAK,CAAE;YAC7C,IAAI,CAACE,QAAQ,OAAOA,SAAS,UAAU;YACvC,IAAIA,KAAKH,OAAO,EAAE;gBAChBmgB,SAASxZ,IAAI,CAACxG;YAChB,OAAO;gBACL,IACEA,KAAKoV,KAAK,IACV,CAAEpV,CAAAA,KAAK6G,IAAI,IAAI7G,KAAKgH,OAAO,IAAIhH,KAAKiR,QAAQ,IAAIjR,KAAK2V,MAAM,AAAD,GAC1D;oBACA3V,KAAKoV,KAAK,CAACrV,OAAO,CAAC,CAACO,IAAM2f,WAAWzZ,IAAI,CAAClG;gBAC5C,OAAO;oBACL2f,WAAWzZ,IAAI,CAACxG;gBAClB;YACF;QACF;QAEAN,cAAcV,MAAM,CAACc,KAAK,GAAG;eACvBkgB;YACJ;gBACE5K,OAAO;uBAAI6K;oBAAYF;iBAAW;YACpC;SACD;IACH;IAEA,8DAA8D;IAC9D,IAAI,OAAO5d,OAAO+d,oBAAoB,KAAK,YAAY;QACrD,MAAMlY,UAAU7F,OAAO+d,oBAAoB,CAAC;YAC1CjN,cAAcvT,cAAcuT,YAAY;QAC1C;QACA,IAAIjL,QAAQiL,YAAY,EAAE;YACxBvT,cAAcuT,YAAY,GAAGjL,QAAQiL,YAAY;QACnD;IACF;IAEA,SAASkN,YAAYngB,IAA0C;QAC7D,IAAI,CAACA,MAAM;YACT,OAAO;QACT;QAEA,MAAMogB,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,IAAIpgB,gBAAgBuM,UAAU6T,UAAU/f,IAAI,CAAC,CAACggB,QAAUrgB,KAAK6G,IAAI,CAACwZ,SAAS;YACzE,OAAO;QACT;QAEA,IAAI,OAAOrgB,SAAS,YAAY;YAC9B,IACEogB,UAAU/f,IAAI,CAAC,CAACggB;gBACd,IAAI;oBACF,IAAIrgB,KAAKqgB,QAAQ;wBACf,OAAO;oBACT;gBACF,EAAE,OAAM,CAAC;gBACT,OAAO;YACT,IACA;gBACA,OAAO;YACT;QACF;QAEA,IAAIlgB,MAAMC,OAAO,CAACJ,SAASA,KAAKK,IAAI,CAAC8f,cAAc;YACjD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAMG,mBACJ5gB,EAAAA,yBAAAA,cAAcV,MAAM,sBAApBU,8BAAAA,uBAAsBI,KAAK,qBAA3BJ,4BAA6BW,IAAI,CAC/B,CAACL,OAAcmgB,YAAYngB,KAAK6G,IAAI,KAAKsZ,YAAYngB,KAAK+G,OAAO,OAC9D;IAEP,IAAIuZ,kBAAkB;YAYhB5gB,8BAAAA,wBAWAA,yBAMAA,uCAAAA;QA5BJ,kCAAkC;QAClC,IAAIgF,yBAAyB;YAC3BtF,QAAQC,IAAI,CACVtG,OAAOC,KAAK,gBACVA,KACE,8FAEF;QAEN;QAEA,KAAI0G,yBAAAA,cAAcV,MAAM,sBAApBU,+BAAAA,uBAAsBI,KAAK,qBAA3BJ,6BAA6BmF,MAAM,EAAE;YACvC,6BAA6B;YAC7BnF,cAAcV,MAAM,CAACc,KAAK,CAACC,OAAO,CAAC,CAACO;gBAClC,IAAI,CAACA,KAAK,OAAOA,MAAM,UAAU;gBACjC,IAAIH,MAAMC,OAAO,CAACE,EAAE8U,KAAK,GAAG;oBAC1B9U,EAAE8U,KAAK,GAAG9U,EAAE8U,KAAK,CAAC5W,MAAM,CACtB,CAAC+hB,IAAM,AAACA,CAAS,CAACC,OAAOC,GAAG,CAAC,qBAAqB,KAAK;gBAE3D;YACF;QACF;QACA,KAAI/gB,0BAAAA,cAAcqL,OAAO,qBAArBrL,wBAAuBmF,MAAM,EAAE;YACjC,gCAAgC;YAChCnF,cAAcqL,OAAO,GAAGrL,cAAcqL,OAAO,CAACvM,MAAM,CAClD,CAACC,IAAM,AAACA,EAAUiiB,iBAAiB,KAAK;QAE5C;QACA,KAAIhhB,8BAAAA,cAAc0P,YAAY,sBAA1B1P,wCAAAA,4BAA4BuS,SAAS,qBAArCvS,sCAAuCmF,MAAM,EAAE;YACjD,uBAAuB;YACvBnF,cAAc0P,YAAY,CAAC6C,SAAS,GAClCvS,cAAc0P,YAAY,CAAC6C,SAAS,CAACzT,MAAM,CACzC,CAACmiB,IAAM,AAACA,EAAUD,iBAAiB,KAAK;QAE9C;IACF;IAEA,yEAAyE;IACzE,oFAAoF;IACpF,IAAI,CAACpc,YAAYlC,OAAO4B,UAAU;QAChCvE,mBAAmBC,eAAegK,eAAeC,KAAK;IACxD;IAEA,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0BAA0B;IAC1B,MAAMiX,gBAAqBlhB,cAAcsT,KAAK;IAC9C,IAAI,OAAO4N,kBAAkB,aAAa;QACxC,MAAMC,eAAe;YACnB,MAAM7N,QACJ,OAAO4N,kBAAkB,aACrB,MAAMA,kBACNA;YACN,0CAA0C;YAC1C,IACEjW,iBACAxK,MAAMC,OAAO,CAAC4S,KAAK,CAAC,UAAU,KAC9BA,KAAK,CAAC,UAAU,CAACnO,MAAM,GAAG,GAC1B;gBACA,MAAMic,eAAenW,aAAa,CAChC/Q,iCACD;gBACDoZ,KAAK,CAACpZ,iCAAiC,GAAG;uBACrCoZ,KAAK,CAAC,UAAU;oBACnB8N;iBACD;YACH;YACA,OAAO9N,KAAK,CAAC,UAAU;YAEvB,KAAK,MAAMjH,QAAQpN,OAAOqN,IAAI,CAACgH,OAAQ;gBACrCA,KAAK,CAACjH,KAAK,GAAGzR,mBAAmB;oBAC/BymB,OAAO/N,KAAK,CAACjH,KAAK;oBAClBjJ;oBACAiJ;oBACAhH;gBACF;YACF;YAEA,OAAOiO;QACT;QACA,sCAAsC;QACtCtT,cAAcsT,KAAK,GAAG6N;IACxB;IAEA,IAAI,CAACze,OAAO,OAAO1C,cAAcsT,KAAK,KAAK,YAAY;QACrD,6BAA6B;QAC7BtT,cAAcsT,KAAK,GAAG,MAAMtT,cAAcsT,KAAK;IACjD;IAEA,OAAOtT;AACT"}