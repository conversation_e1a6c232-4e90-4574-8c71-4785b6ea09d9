{"version": 3, "sources": ["../../src/build/load-jsconfig.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport * as Log from './output/log'\nimport { getTypeScriptConfiguration } from '../lib/typescript/getTypeScriptConfiguration'\nimport { readFileSync } from 'fs'\nimport isError from '../lib/is-error'\nimport { hasNecessaryDependencies } from '../lib/has-necessary-dependencies'\n\nlet TSCONFIG_WARNED = false\n\nexport function parseJsonFile(filePath: string) {\n  const JSON5 = require('next/dist/compiled/json5')\n  const contents = readFileSync(filePath, 'utf8')\n\n  // Special case an empty file\n  if (contents.trim() === '') {\n    return {}\n  }\n\n  try {\n    return JSON5.parse(contents)\n  } catch (err) {\n    if (!isError(err)) throw err\n    const { codeFrameColumns } = require('next/dist/compiled/babel/code-frame')\n    const codeFrame = codeFrameColumns(\n      String(contents),\n      {\n        start: {\n          line: (err as Error & { lineNumber?: number }).lineNumber || 0,\n          column: (err as Error & { columnNumber?: number }).columnNumber || 0,\n        },\n      },\n      { message: err.message, highlightCode: true }\n    )\n    throw new Error(`Failed to parse \"${filePath}\":\\n${codeFrame}`)\n  }\n}\n\nexport type ResolvedBaseUrl =\n  | { baseUrl: string; isImplicit: boolean }\n  | undefined\n\nexport type JsConfig = { compilerOptions: Record<string, any> } | undefined\n\nexport default async function loadJsConfig(\n  dir: string,\n  config: NextConfigComplete\n): Promise<{\n  useTypeScript: boolean\n  jsConfig: JsConfig\n  jsConfigPath?: string\n  resolvedBaseUrl: ResolvedBaseUrl\n}> {\n  let typeScriptPath: string | undefined\n  try {\n    const deps = await hasNecessaryDependencies(dir, [\n      {\n        pkg: 'typescript',\n        file: 'typescript/lib/typescript.js',\n        exportsRestrict: true,\n      },\n    ])\n    typeScriptPath = deps.resolved.get('typescript')\n  } catch {}\n  const tsConfigPath = path.join(dir, config.typescript.tsconfigPath)\n  const useTypeScript = Boolean(typeScriptPath && fs.existsSync(tsConfigPath))\n\n  let implicitBaseurl\n  let jsConfig: { compilerOptions: Record<string, any> } | undefined\n  // jsconfig is a subset of tsconfig\n  if (useTypeScript) {\n    if (\n      config.typescript.tsconfigPath !== 'tsconfig.json' &&\n      TSCONFIG_WARNED === false\n    ) {\n      TSCONFIG_WARNED = true\n      Log.info(`Using tsconfig file: ${config.typescript.tsconfigPath}`)\n    }\n\n    const ts = (await Promise.resolve(\n      require(typeScriptPath!)\n    )) as typeof import('typescript')\n    const tsConfig = await getTypeScriptConfiguration(ts, tsConfigPath, true)\n    jsConfig = { compilerOptions: tsConfig.options }\n    implicitBaseurl = path.dirname(tsConfigPath)\n  }\n\n  const jsConfigPath = path.join(dir, 'jsconfig.json')\n  if (!useTypeScript && fs.existsSync(jsConfigPath)) {\n    jsConfig = parseJsonFile(jsConfigPath)\n    implicitBaseurl = path.dirname(jsConfigPath)\n  }\n\n  let resolvedBaseUrl: ResolvedBaseUrl\n  if (jsConfig?.compilerOptions?.baseUrl) {\n    resolvedBaseUrl = {\n      baseUrl: path.resolve(dir, jsConfig.compilerOptions.baseUrl),\n      isImplicit: false,\n    }\n  } else {\n    if (implicitBaseurl) {\n      resolvedBaseUrl = {\n        baseUrl: implicitBaseurl,\n        isImplicit: true,\n      }\n    }\n  }\n\n  return {\n    useTypeScript,\n    jsConfig,\n    resolvedBaseUrl,\n    jsConfigPath: useTypeScript\n      ? tsConfigPath\n      : fs.existsSync(jsConfigPath)\n        ? jsConfigPath\n        : undefined,\n  }\n}\n"], "names": ["path", "fs", "Log", "getTypeScriptConfiguration", "readFileSync", "isError", "hasNecessaryDependencies", "TSCONFIG_WARNED", "parseJsonFile", "filePath", "JSON5", "require", "contents", "trim", "parse", "err", "codeFrameColumns", "codeFrame", "String", "start", "line", "lineNumber", "column", "columnNumber", "message", "highlightCode", "Error", "loadJsConfig", "dir", "config", "jsConfig", "typeScriptPath", "deps", "pkg", "file", "exportsRestrict", "resolved", "get", "tsConfigPath", "join", "typescript", "tsconfigPath", "useTypeScript", "Boolean", "existsSync", "implicit<PERSON><PERSON><PERSON>l", "info", "ts", "Promise", "resolve", "tsConfig", "compilerOptions", "options", "dirname", "jsConfigPath", "resolvedBaseUrl", "baseUrl", "isImplicit", "undefined"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,KAAI;AAEnB,YAAYC,SAAS,eAAc;AACnC,SAASC,0BAA0B,QAAQ,+CAA8C;AACzF,SAASC,YAAY,QAAQ,KAAI;AACjC,OAAOC,aAAa,kBAAiB;AACrC,SAASC,wBAAwB,QAAQ,oCAAmC;AAE5E,IAAIC,kBAAkB;AAEtB,OAAO,SAASC,cAAcC,QAAgB;IAC5C,MAAMC,QAAQC,QAAQ;IACtB,MAAMC,WAAWR,aAAaK,UAAU;IAExC,6BAA6B;IAC7B,IAAIG,SAASC,IAAI,OAAO,IAAI;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOH,MAAMI,KAAK,CAACF;IACrB,EAAE,OAAOG,KAAK;QACZ,IAAI,CAACV,QAAQU,MAAM,MAAMA;QACzB,MAAM,EAAEC,gBAAgB,EAAE,GAAGL,QAAQ;QACrC,MAAMM,YAAYD,iBAChBE,OAAON,WACP;YACEO,OAAO;gBACLC,MAAM,AAACL,IAAwCM,UAAU,IAAI;gBAC7DC,QAAQ,AAACP,IAA0CQ,YAAY,IAAI;YACrE;QACF,GACA;YAAEC,SAAST,IAAIS,OAAO;YAAEC,eAAe;QAAK;QAE9C,MAAM,qBAAyD,CAAzD,IAAIC,MAAM,CAAC,iBAAiB,EAAEjB,SAAS,IAAI,EAAEQ,WAAW,GAAxD,qBAAA;mBAAA;wBAAA;0BAAA;QAAwD;IAChE;AACF;AAQA,eAAe,eAAeU,aAC5BC,GAAW,EACXC,MAA0B;QAgDtBC;IAzCJ,IAAIC;IACJ,IAAI;QACF,MAAMC,OAAO,MAAM1B,yBAAyBsB,KAAK;YAC/C;gBACEK,KAAK;gBACLC,MAAM;gBACNC,iBAAiB;YACnB;SACD;QACDJ,iBAAiBC,KAAKI,QAAQ,CAACC,GAAG,CAAC;IACrC,EAAE,OAAM,CAAC;IACT,MAAMC,eAAetC,KAAKuC,IAAI,CAACX,KAAKC,OAAOW,UAAU,CAACC,YAAY;IAClE,MAAMC,gBAAgBC,QAAQZ,kBAAkB9B,GAAG2C,UAAU,CAACN;IAE9D,IAAIO;IACJ,IAAIf;IACJ,mCAAmC;IACnC,IAAIY,eAAe;QACjB,IACEb,OAAOW,UAAU,CAACC,YAAY,KAAK,mBACnClC,oBAAoB,OACpB;YACAA,kBAAkB;YAClBL,IAAI4C,IAAI,CAAC,CAAC,qBAAqB,EAAEjB,OAAOW,UAAU,CAACC,YAAY,EAAE;QACnE;QAEA,MAAMM,KAAM,MAAMC,QAAQC,OAAO,CAC/BtC,QAAQoB;QAEV,MAAMmB,WAAW,MAAM/C,2BAA2B4C,IAAIT,cAAc;QACpER,WAAW;YAAEqB,iBAAiBD,SAASE,OAAO;QAAC;QAC/CP,kBAAkB7C,KAAKqD,OAAO,CAACf;IACjC;IAEA,MAAMgB,eAAetD,KAAKuC,IAAI,CAACX,KAAK;IACpC,IAAI,CAACc,iBAAiBzC,GAAG2C,UAAU,CAACU,eAAe;QACjDxB,WAAWtB,cAAc8C;QACzBT,kBAAkB7C,KAAKqD,OAAO,CAACC;IACjC;IAEA,IAAIC;IACJ,IAAIzB,6BAAAA,4BAAAA,SAAUqB,eAAe,qBAAzBrB,0BAA2B0B,OAAO,EAAE;QACtCD,kBAAkB;YAChBC,SAASxD,KAAKiD,OAAO,CAACrB,KAAKE,SAASqB,eAAe,CAACK,OAAO;YAC3DC,YAAY;QACd;IACF,OAAO;QACL,IAAIZ,iBAAiB;YACnBU,kBAAkB;gBAChBC,SAASX;gBACTY,YAAY;YACd;QACF;IACF;IAEA,OAAO;QACLf;QACAZ;QACAyB;QACAD,cAAcZ,gBACVJ,eACArC,GAAG2C,UAAU,CAACU,gBACZA,eACAI;IACR;AACF"}