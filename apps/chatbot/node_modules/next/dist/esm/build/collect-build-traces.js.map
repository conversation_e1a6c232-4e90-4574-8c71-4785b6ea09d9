{"version": 3, "sources": ["../../src/build/collect-build-traces.ts"], "sourcesContent": ["import { Span } from '../trace'\nimport type { NextConfigComplete } from '../server/config-shared'\n\nimport {\n  TRACE_IGNORES,\n  type BuildTraceContext,\n  getFilesMapFromReasons,\n} from './webpack/plugins/next-trace-entrypoints-plugin'\n\nimport path from 'path'\nimport fs from 'fs/promises'\nimport { nonNullable } from '../lib/non-nullable'\nimport * as ciEnvironment from '../server/ci-info'\nimport debugOriginal from 'next/dist/compiled/debug'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport { defaultOverrides } from '../server/require-hook'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport isError from '../lib/is-error'\nimport type { NodeFileTraceReasons } from '@vercel/nft'\nimport type { RoutesUsingEdgeRuntime } from './utils'\n\nconst debug = debugOriginal('next:build:build-traces')\n\nfunction shouldIgnore(\n  file: string,\n  serverIgnoreFn: (file: string) => boolean,\n  reasons: NodeFileTraceReasons,\n  cachedIgnoreFiles: Map<string, boolean>,\n  children: Set<string> = new Set()\n) {\n  if (cachedIgnoreFiles.has(file)) {\n    return cachedIgnoreFiles.get(file)\n  }\n\n  if (serverIgnoreFn(file)) {\n    cachedIgnoreFiles.set(file, true)\n    return true\n  }\n  children.add(file)\n\n  const reason = reasons.get(file)\n  if (!reason || reason.parents.size === 0 || reason.type.includes('initial')) {\n    cachedIgnoreFiles.set(file, false)\n    return false\n  }\n\n  // if all parents are ignored the child file\n  // should be ignored as well\n  let allParentsIgnored = true\n\n  for (const parent of reason.parents.values()) {\n    if (!children.has(parent)) {\n      children.add(parent)\n      if (\n        !shouldIgnore(\n          parent,\n          serverIgnoreFn,\n          reasons,\n          cachedIgnoreFiles,\n          children\n        )\n      ) {\n        allParentsIgnored = false\n        break\n      }\n    }\n  }\n\n  cachedIgnoreFiles.set(file, allParentsIgnored)\n  return allParentsIgnored\n}\n\nexport async function collectBuildTraces({\n  dir,\n  config,\n  distDir,\n  edgeRuntimeRoutes,\n  staticPages,\n  nextBuildSpan = new Span({ name: 'build' }),\n  hasSsrAmpPages,\n  buildTraceContext,\n  outputFileTracingRoot,\n}: {\n  dir: string\n  distDir: string\n  staticPages: string[]\n  hasSsrAmpPages: boolean\n  outputFileTracingRoot: string\n  // pageInfos is serialized when this function runs in a worker.\n  edgeRuntimeRoutes: RoutesUsingEdgeRuntime\n  nextBuildSpan?: Span\n  config: NextConfigComplete\n  buildTraceContext?: BuildTraceContext\n}) {\n  const startTime = Date.now()\n  debug('starting build traces')\n\n  const { outputFileTracingIncludes = {}, outputFileTracingExcludes = {} } =\n    config\n  const excludeGlobKeys = Object.keys(outputFileTracingExcludes)\n  const includeGlobKeys = Object.keys(outputFileTracingIncludes)\n\n  await nextBuildSpan\n    .traceChild('node-file-trace-build', {\n      isTurbotrace: 'false', // TODO(arlyon): remove this\n    })\n    .traceAsyncFn(async () => {\n      const nextServerTraceOutput = path.join(\n        distDir,\n        'next-server.js.nft.json'\n      )\n      const nextMinimalTraceOutput = path.join(\n        distDir,\n        'next-minimal-server.js.nft.json'\n      )\n      const root = outputFileTracingRoot\n\n      // Under standalone mode, we need to trace the extra IPC server and\n      // worker files.\n      const isStandalone = config.output === 'standalone'\n      const sharedEntriesSet = Object.keys(defaultOverrides).map((value) =>\n        require.resolve(value, {\n          paths: [require.resolve('next/dist/server/require-hook')],\n        })\n      )\n\n      const { cacheHandler } = config\n      const { cacheHandlers } = config.experimental\n\n      // ensure we trace any dependencies needed for custom\n      // incremental cache handler\n      if (cacheHandler) {\n        sharedEntriesSet.push(\n          require.resolve(\n            path.isAbsolute(cacheHandler)\n              ? cacheHandler\n              : path.join(dir, cacheHandler)\n          )\n        )\n      }\n\n      if (cacheHandlers) {\n        for (const handlerPath of Object.values(cacheHandlers)) {\n          if (handlerPath) {\n            sharedEntriesSet.push(\n              require.resolve(\n                path.isAbsolute(handlerPath)\n                  ? handlerPath\n                  : path.join(dir, handlerPath)\n              )\n            )\n          }\n        }\n      }\n\n      const serverEntries = [\n        ...sharedEntriesSet,\n        ...(isStandalone\n          ? [\n              require.resolve('next/dist/server/lib/start-server'),\n              require.resolve('next/dist/server/next'),\n              require.resolve('next/dist/server/require-hook'),\n            ]\n          : []),\n        require.resolve('next/dist/server/next-server'),\n      ].filter(Boolean) as string[]\n\n      const minimalServerEntries = [\n        ...sharedEntriesSet,\n        require.resolve('next/dist/compiled/next-server/server.runtime.prod'),\n      ].filter(Boolean)\n\n      const additionalIgnores = new Set<string>()\n\n      for (const glob of excludeGlobKeys) {\n        if (picomatch(glob)('next-server')) {\n          outputFileTracingExcludes[glob].forEach((exclude) => {\n            additionalIgnores.add(exclude)\n          })\n        }\n      }\n\n      const makeIgnoreFn = (ignores: string[]) => {\n        // pre compile the ignore globs\n        const isMatch = picomatch(ignores, {\n          contains: true,\n          dot: true,\n        })\n\n        return (pathname: string) => {\n          if (path.isAbsolute(pathname) && !pathname.startsWith(root)) {\n            return true\n          }\n\n          return isMatch(pathname)\n        }\n      }\n\n      const sharedIgnores = [\n        '**/next/dist/compiled/next-server/**/*.dev.js',\n        ...(isStandalone ? [] : ['**/next/dist/compiled/jest-worker/**/*']),\n        '**/next/dist/compiled/webpack/*',\n        '**/node_modules/webpack5/**/*',\n        '**/next/dist/server/lib/route-resolver*',\n        'next/dist/compiled/semver/semver/**/*.js',\n\n        ...(ciEnvironment.hasNextSupport\n          ? [\n              // only ignore image-optimizer code when\n              // this is being handled outside of next-server\n              '**/next/dist/server/image-optimizer.js',\n            ]\n          : []),\n\n        ...(!hasSsrAmpPages\n          ? ['**/next/dist/compiled/@ampproject/toolbox-optimizer/**/*']\n          : []),\n\n        ...(isStandalone ? [] : TRACE_IGNORES),\n        ...additionalIgnores,\n      ]\n\n      const sharedIgnoresFn = makeIgnoreFn(sharedIgnores)\n\n      const serverIgnores = [\n        ...sharedIgnores,\n        '**/node_modules/react{,-dom,-dom-server-turbopack}/**/*.development.js',\n        '**/*.d.ts',\n        '**/*.map',\n        '**/next/dist/pages/**/*',\n        ...(ciEnvironment.hasNextSupport\n          ? ['**/node_modules/sharp/**/*', '**/@img/sharp-libvips*/**/*']\n          : []),\n      ].filter(nonNullable)\n      const serverIgnoreFn = makeIgnoreFn(serverIgnores)\n\n      const minimalServerIgnores = [\n        ...serverIgnores,\n        '**/next/dist/compiled/edge-runtime/**/*',\n        '**/next/dist/server/web/sandbox/**/*',\n        '**/next/dist/server/post-process.js',\n      ]\n      const minimalServerIgnoreFn = makeIgnoreFn(minimalServerIgnores)\n\n      const routesIgnores = [\n        ...sharedIgnores,\n        // server chunks are provided via next-trace-entrypoints-plugin plugin\n        // as otherwise all chunks are traced here and included for all pages\n        // whether they are needed or not\n        '**/.next/server/chunks/**',\n        '**/next/dist/server/optimize-amp.js',\n        '**/next/dist/server/post-process.js',\n      ].filter(nonNullable)\n\n      const routeIgnoreFn = makeIgnoreFn(routesIgnores)\n\n      const serverTracedFiles = new Set<string>()\n      const minimalServerTracedFiles = new Set<string>()\n\n      function addToTracedFiles(base: string, file: string, dest: Set<string>) {\n        dest.add(\n          path.relative(distDir, path.join(base, file)).replace(/\\\\/g, '/')\n        )\n      }\n\n      if (isStandalone) {\n        addToTracedFiles(\n          '',\n          require.resolve('next/dist/compiled/jest-worker/processChild'),\n          serverTracedFiles\n        )\n        addToTracedFiles(\n          '',\n          require.resolve('next/dist/compiled/jest-worker/threadChild'),\n          serverTracedFiles\n        )\n      }\n\n      {\n        const chunksToTrace: string[] = [\n          ...(buildTraceContext?.chunksTrace?.action.input || []),\n          ...serverEntries,\n          ...minimalServerEntries,\n        ]\n        const result = await nodeFileTrace(chunksToTrace, {\n          base: outputFileTracingRoot,\n          processCwd: dir,\n          mixedModules: true,\n          async readFile(p) {\n            try {\n              return await fs.readFile(p, 'utf8')\n            } catch (e) {\n              if (isError(e) && (e.code === 'ENOENT' || e.code === 'EISDIR')) {\n                // since tracing runs in parallel with static generation server\n                // files might be removed from that step so tolerate ENOENT\n                // errors gracefully\n                return ''\n              }\n              throw e\n            }\n          },\n          async readlink(p) {\n            try {\n              return await fs.readlink(p)\n            } catch (e) {\n              if (\n                isError(e) &&\n                (e.code === 'EINVAL' ||\n                  e.code === 'ENOENT' ||\n                  e.code === 'UNKNOWN')\n              ) {\n                return null\n              }\n              throw e\n            }\n          },\n          async stat(p) {\n            try {\n              return await fs.stat(p)\n            } catch (e) {\n              if (isError(e) && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {\n                return null\n              }\n              throw e\n            }\n          },\n          // handle shared ignores at top-level as it\n          // avoids over-tracing when we don't need to\n          // and speeds up total trace time\n          ignore(p) {\n            if (sharedIgnoresFn(p)) {\n              return true\n            }\n\n            // if a chunk is attempting to be traced that isn't\n            // in our initial list we need to ignore it to prevent\n            // over tracing as webpack needs to be the source of\n            // truth for which chunks should be included for each entry\n            if (\n              p.includes('.next/server/chunks') &&\n              !chunksToTrace.includes(path.join(outputFileTracingRoot, p))\n            ) {\n              return true\n            }\n            return false\n          },\n        })\n        const reasons = result.reasons\n        const fileList = result.fileList\n        for (const file of result.esmFileList) {\n          fileList.add(file)\n        }\n\n        const parentFilesMap = getFilesMapFromReasons(fileList, reasons)\n        const cachedLookupIgnore = new Map<string, boolean>()\n        const cachedLookupIgnoreMinimal = new Map<string, boolean>()\n\n        for (const [entries, tracedFiles] of [\n          [serverEntries, serverTracedFiles],\n          [minimalServerEntries, minimalServerTracedFiles],\n        ] as Array<[string[], Set<string>]>) {\n          for (const file of entries) {\n            const curFiles = [\n              ...(parentFilesMap\n                .get(path.relative(outputFileTracingRoot, file))\n                ?.keys() || []),\n            ]\n            tracedFiles.add(path.relative(distDir, file).replace(/\\\\/g, '/'))\n\n            for (const curFile of curFiles || []) {\n              const filePath = path.join(outputFileTracingRoot, curFile)\n\n              if (\n                !shouldIgnore(\n                  curFile,\n                  tracedFiles === minimalServerTracedFiles\n                    ? minimalServerIgnoreFn\n                    : serverIgnoreFn,\n                  reasons,\n                  tracedFiles === minimalServerTracedFiles\n                    ? cachedLookupIgnoreMinimal\n                    : cachedLookupIgnore\n                )\n              ) {\n                tracedFiles.add(\n                  path.relative(distDir, filePath).replace(/\\\\/g, '/')\n                )\n              }\n            }\n          }\n        }\n\n        const { entryNameFilesMap } = buildTraceContext?.chunksTrace || {}\n\n        const cachedLookupIgnoreRoutes = new Map<string, boolean>()\n\n        await Promise.all(\n          [\n            ...(entryNameFilesMap\n              ? Object.entries(entryNameFilesMap)\n              : new Map()),\n          ].map(async ([entryName, entryNameFiles]) => {\n            const isApp = entryName.startsWith('app/')\n            const isPages = entryName.startsWith('pages/')\n            let route = entryName\n            if (isApp) {\n              route = normalizeAppPath(route.substring('app'.length))\n            }\n            if (isPages) {\n              route = normalizePagePath(route.substring('pages'.length))\n            }\n\n            // we don't need to trace for automatically statically optimized\n            // pages as they don't have server bundles, note there is\n            // the caveat with flying shuttle mode as it needs this for\n            // detecting changed entries\n            if (staticPages.includes(route)) {\n              return\n            }\n            const entryOutputPath = path.join(\n              distDir,\n              'server',\n              `${entryName}.js`\n            )\n            const traceOutputPath = `${entryOutputPath}.nft.json`\n            const existingTrace = JSON.parse(\n              await fs.readFile(traceOutputPath, 'utf8')\n            ) as {\n              version: number\n              files: string[]\n              fileHashes: Record<string, string>\n            }\n            const traceOutputDir = path.dirname(traceOutputPath)\n            const curTracedFiles = new Set<string>()\n\n            for (const file of [...entryNameFiles, entryOutputPath]) {\n              const curFiles = [\n                ...(parentFilesMap\n                  .get(path.relative(outputFileTracingRoot, file))\n                  ?.keys() || []),\n              ]\n              for (const curFile of curFiles || []) {\n                if (\n                  !shouldIgnore(\n                    curFile,\n                    routeIgnoreFn,\n                    reasons,\n                    cachedLookupIgnoreRoutes\n                  )\n                ) {\n                  const filePath = path.join(outputFileTracingRoot, curFile)\n                  const outputFile = path\n                    .relative(traceOutputDir, filePath)\n                    .replace(/\\\\/g, '/')\n                  curTracedFiles.add(outputFile)\n                }\n              }\n            }\n\n            for (const file of existingTrace.files || []) {\n              curTracedFiles.add(file)\n            }\n\n            await fs.writeFile(\n              traceOutputPath,\n              JSON.stringify({\n                ...existingTrace,\n                files: [...curTracedFiles].sort(),\n              })\n            )\n          })\n        )\n      }\n\n      const moduleTypes = ['app-page', 'pages']\n\n      for (const type of moduleTypes) {\n        const modulePath = require.resolve(\n          `next/dist/server/route-modules/${type}/module.compiled`\n        )\n        const relativeModulePath = path.relative(root, modulePath)\n\n        const contextDir = path.join(\n          path.dirname(modulePath),\n          'vendored',\n          'contexts'\n        )\n\n        for (const item of await fs.readdir(contextDir)) {\n          const itemPath = path.relative(root, path.join(contextDir, item))\n          if (!serverIgnoreFn(itemPath)) {\n            addToTracedFiles(root, itemPath, serverTracedFiles)\n            addToTracedFiles(root, itemPath, minimalServerTracedFiles)\n          }\n        }\n        addToTracedFiles(root, relativeModulePath, serverTracedFiles)\n        addToTracedFiles(root, relativeModulePath, minimalServerTracedFiles)\n      }\n\n      await Promise.all([\n        fs.writeFile(\n          nextServerTraceOutput,\n          JSON.stringify({\n            version: 1,\n            files: Array.from(serverTracedFiles),\n          } as {\n            version: number\n            files: string[]\n          })\n        ),\n        fs.writeFile(\n          nextMinimalTraceOutput,\n          JSON.stringify({\n            version: 1,\n            files: Array.from(minimalServerTracedFiles),\n          } as {\n            version: number\n            files: string[]\n          })\n        ),\n      ])\n    })\n\n  // apply outputFileTracingIncludes/outputFileTracingExcludes after runTurbotrace\n  const includeExcludeSpan = nextBuildSpan.traceChild('apply-include-excludes')\n  await includeExcludeSpan.traceAsyncFn(async () => {\n    const globOrig =\n      require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n    const glob = (pattern: string): Promise<string[]> => {\n      return new Promise((resolve, reject) => {\n        globOrig(\n          pattern,\n          { cwd: dir, nodir: true, dot: true },\n          (err, files) => {\n            if (err) {\n              return reject(err)\n            }\n            resolve(files)\n          }\n        )\n      })\n    }\n\n    const { entryNameFilesMap } = buildTraceContext?.chunksTrace || {}\n\n    await Promise.all(\n      [\n        ...(entryNameFilesMap ? Object.entries(entryNameFilesMap) : new Map()),\n      ].map(async ([entryName]) => {\n        const isApp = entryName.startsWith('app/')\n        const isPages = entryName.startsWith('pages/')\n        let route = entryName\n        if (isApp) {\n          route = normalizeAppPath(entryName)\n        }\n        if (isPages) {\n          route = normalizePagePath(entryName)\n        }\n\n        if (staticPages.includes(route)) {\n          return\n        }\n\n        // edge routes have no trace files\n        if (edgeRuntimeRoutes.hasOwnProperty(route)) {\n          return\n        }\n\n        const combinedIncludes = new Set<string>()\n        const combinedExcludes = new Set<string>()\n        for (const curGlob of includeGlobKeys) {\n          const isMatch = picomatch(curGlob, { dot: true, contains: true })\n          if (isMatch(route)) {\n            for (const include of outputFileTracingIncludes[curGlob]) {\n              combinedIncludes.add(include.replace(/\\\\/g, '/'))\n            }\n          }\n        }\n\n        for (const curGlob of excludeGlobKeys) {\n          const isMatch = picomatch(curGlob, { dot: true, contains: true })\n          if (isMatch(route)) {\n            for (const exclude of outputFileTracingExcludes[curGlob]) {\n              combinedExcludes.add(exclude)\n            }\n          }\n        }\n\n        if (!combinedIncludes?.size && !combinedExcludes?.size) {\n          return\n        }\n\n        const traceFile = path.join(\n          distDir,\n          `server`,\n          `${entryName}.js.nft.json`\n        )\n        const pageDir = path.dirname(traceFile)\n        const traceContent = JSON.parse(await fs.readFile(traceFile, 'utf8'))\n        const includes: string[] = []\n        const resolvedTraceIncludes = new Map<string, string[]>()\n\n        if (combinedIncludes?.size) {\n          await Promise.all(\n            [...combinedIncludes].map(async (includeGlob) => {\n              const results = await glob(includeGlob)\n              const resolvedInclude = resolvedTraceIncludes.get(\n                includeGlob\n              ) || [\n                ...results.map((file) => {\n                  return path.relative(pageDir, path.join(dir, file))\n                }),\n              ]\n              includes.push(...resolvedInclude)\n              resolvedTraceIncludes.set(includeGlob, resolvedInclude)\n            })\n          )\n        }\n        const combined = new Set([...traceContent.files, ...includes])\n\n        if (combinedExcludes?.size) {\n          const resolvedGlobs = [...combinedExcludes].map((exclude) =>\n            path.join(dir, exclude)\n          )\n\n          // pre compile before forEach\n          const isMatch = picomatch(resolvedGlobs, {\n            dot: true,\n            contains: true,\n          })\n\n          combined.forEach((file) => {\n            if (isMatch(path.join(pageDir, file))) {\n              combined.delete(file)\n            }\n          })\n        }\n\n        // overwrite trace file with custom includes/excludes\n        await fs.writeFile(\n          traceFile,\n          JSON.stringify({\n            version: traceContent.version,\n            files: [...combined],\n          })\n        )\n      })\n    )\n  })\n\n  debug(`finished build tracing ${Date.now() - startTime}ms`)\n}\n"], "names": ["Span", "TRACE_IGNORES", "getFilesMapFromReasons", "path", "fs", "nonNullable", "ciEnvironment", "debugOriginal", "picomatch", "defaultOverrides", "nodeFileTrace", "normalizePagePath", "normalizeAppPath", "isError", "debug", "shouldIgnore", "file", "serverIgnoreFn", "reasons", "cachedIgnoreFiles", "children", "Set", "has", "get", "set", "add", "reason", "parents", "size", "type", "includes", "allParentsIgnored", "parent", "values", "collectBuildTraces", "dir", "config", "distDir", "edgeRuntimeRoutes", "staticPages", "nextBuildSpan", "name", "hasSsrAmpPages", "buildTraceContext", "outputFileTracingRoot", "startTime", "Date", "now", "outputFileTracingIncludes", "outputFileTracingExcludes", "excludeGlobKeys", "Object", "keys", "includeGlobKeys", "<PERSON><PERSON><PERSON><PERSON>", "isTurbotrace", "traceAsyncFn", "nextServerTraceOutput", "join", "nextMinimalTraceOutput", "root", "isStandalone", "output", "sharedEntriesSet", "map", "value", "require", "resolve", "paths", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "experimental", "push", "isAbsolute", "handler<PERSON><PERSON>", "serverEntries", "filter", "Boolean", "minimalServerEntries", "additionalIgnores", "glob", "for<PERSON>ach", "exclude", "makeIgnoreFn", "ignores", "isMatch", "contains", "dot", "pathname", "startsWith", "sharedIgnores", "hasNextSupport", "sharedIgnoresFn", "serverIgnores", "minimalServerIgnores", "minimalServerIgnoreFn", "routesIgnores", "routeIgnoreFn", "serverTracedFiles", "minimalServerTracedFiles", "addToTracedFiles", "base", "dest", "relative", "replace", "chunksToTrace", "chunksTrace", "action", "input", "result", "processCwd", "mixedModules", "readFile", "p", "e", "code", "readlink", "stat", "ignore", "fileList", "esmFileList", "parentFilesMap", "cachedLookupIgnore", "Map", "cachedLookupIgnoreMinimal", "entries", "tracedFiles", "curFiles", "curFile", "filePath", "entryNameFilesMap", "cachedLookupIgnoreRoutes", "Promise", "all", "entryName", "entryNameFiles", "isApp", "isPages", "route", "substring", "length", "entryOutputPath", "traceOutputPath", "existingTrace", "JSON", "parse", "traceOutputDir", "dirname", "curTracedFiles", "outputFile", "files", "writeFile", "stringify", "sort", "moduleTypes", "modulePath", "relativeModulePath", "contextDir", "item", "readdir", "itemPath", "version", "Array", "from", "includeExcludeSpan", "globOrig", "pattern", "reject", "cwd", "nodir", "err", "hasOwnProperty", "combinedIncludes", "combinedExcludes", "curGlob", "include", "traceFile", "pageDir", "traceContent", "resolvedTraceIncludes", "includeGlob", "results", "resolvedInclude", "combined", "resolvedGlobs", "delete"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAU;AAG/B,SACEC,aAAa,EAEbC,sBAAsB,QACjB,kDAAiD;AAExD,OAAOC,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,SAASC,WAAW,QAAQ,sBAAqB;AACjD,YAAYC,mBAAmB,oBAAmB;AAClD,OAAOC,mBAAmB,2BAA0B;AACpD,OAAOC,eAAe,+BAA8B;AACpD,SAASC,gBAAgB,QAAQ,yBAAwB;AACzD,SAASC,aAAa,QAAQ,iCAAgC;AAC9D,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,OAAOC,aAAa,kBAAiB;AAIrC,MAAMC,QAAQP,cAAc;AAE5B,SAASQ,aACPC,IAAY,EACZC,cAAyC,EACzCC,OAA6B,EAC7BC,iBAAuC,EACvCC,WAAwB,IAAIC,KAAK;IAEjC,IAAIF,kBAAkBG,GAAG,CAACN,OAAO;QAC/B,OAAOG,kBAAkBI,GAAG,CAACP;IAC/B;IAEA,IAAIC,eAAeD,OAAO;QACxBG,kBAAkBK,GAAG,CAACR,MAAM;QAC5B,OAAO;IACT;IACAI,SAASK,GAAG,CAACT;IAEb,MAAMU,SAASR,QAAQK,GAAG,CAACP;IAC3B,IAAI,CAACU,UAAUA,OAAOC,OAAO,CAACC,IAAI,KAAK,KAAKF,OAAOG,IAAI,CAACC,QAAQ,CAAC,YAAY;QAC3EX,kBAAkBK,GAAG,CAACR,MAAM;QAC5B,OAAO;IACT;IAEA,4CAA4C;IAC5C,4BAA4B;IAC5B,IAAIe,oBAAoB;IAExB,KAAK,MAAMC,UAAUN,OAAOC,OAAO,CAACM,MAAM,GAAI;QAC5C,IAAI,CAACb,SAASE,GAAG,CAACU,SAAS;YACzBZ,SAASK,GAAG,CAACO;YACb,IACE,CAACjB,aACCiB,QACAf,gBACAC,SACAC,mBACAC,WAEF;gBACAW,oBAAoB;gBACpB;YACF;QACF;IACF;IAEAZ,kBAAkBK,GAAG,CAACR,MAAMe;IAC5B,OAAOA;AACT;AAEA,OAAO,eAAeG,mBAAmB,EACvCC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,IAAIxC,KAAK;IAAEyC,MAAM;AAAQ,EAAE,EAC3CC,cAAc,EACdC,iBAAiB,EACjBC,qBAAqB,EAYtB;IACC,MAAMC,YAAYC,KAAKC,GAAG;IAC1BjC,MAAM;IAEN,MAAM,EAAEkC,4BAA4B,CAAC,CAAC,EAAEC,4BAA4B,CAAC,CAAC,EAAE,GACtEb;IACF,MAAMc,kBAAkBC,OAAOC,IAAI,CAACH;IACpC,MAAMI,kBAAkBF,OAAOC,IAAI,CAACJ;IAEpC,MAAMR,cACHc,UAAU,CAAC,yBAAyB;QACnCC,cAAc;IAChB,GACCC,YAAY,CAAC;QACZ,MAAMC,wBAAwBtD,KAAKuD,IAAI,CACrCrB,SACA;QAEF,MAAMsB,yBAAyBxD,KAAKuD,IAAI,CACtCrB,SACA;QAEF,MAAMuB,OAAOhB;QAEb,mEAAmE;QACnE,gBAAgB;QAChB,MAAMiB,eAAezB,OAAO0B,MAAM,KAAK;QACvC,MAAMC,mBAAmBZ,OAAOC,IAAI,CAAC3C,kBAAkBuD,GAAG,CAAC,CAACC,QAC1DC,QAAQC,OAAO,CAACF,OAAO;gBACrBG,OAAO;oBAACF,QAAQC,OAAO,CAAC;iBAAiC;YAC3D;QAGF,MAAM,EAAEE,YAAY,EAAE,GAAGjC;QACzB,MAAM,EAAEkC,aAAa,EAAE,GAAGlC,OAAOmC,YAAY;QAE7C,qDAAqD;QACrD,4BAA4B;QAC5B,IAAIF,cAAc;YAChBN,iBAAiBS,IAAI,CACnBN,QAAQC,OAAO,CACbhE,KAAKsE,UAAU,CAACJ,gBACZA,eACAlE,KAAKuD,IAAI,CAACvB,KAAKkC;QAGzB;QAEA,IAAIC,eAAe;YACjB,KAAK,MAAMI,eAAevB,OAAOlB,MAAM,CAACqC,eAAgB;gBACtD,IAAII,aAAa;oBACfX,iBAAiBS,IAAI,CACnBN,QAAQC,OAAO,CACbhE,KAAKsE,UAAU,CAACC,eACZA,cACAvE,KAAKuD,IAAI,CAACvB,KAAKuC;gBAGzB;YACF;QACF;QAEA,MAAMC,gBAAgB;eACjBZ;eACCF,eACA;gBACEK,QAAQC,OAAO,CAAC;gBAChBD,QAAQC,OAAO,CAAC;gBAChBD,QAAQC,OAAO,CAAC;aACjB,GACD,EAAE;YACND,QAAQC,OAAO,CAAC;SACjB,CAACS,MAAM,CAACC;QAET,MAAMC,uBAAuB;eACxBf;YACHG,QAAQC,OAAO,CAAC;SACjB,CAACS,MAAM,CAACC;QAET,MAAME,oBAAoB,IAAI1D;QAE9B,KAAK,MAAM2D,QAAQ9B,gBAAiB;YAClC,IAAI1C,UAAUwE,MAAM,gBAAgB;gBAClC/B,yBAAyB,CAAC+B,KAAK,CAACC,OAAO,CAAC,CAACC;oBACvCH,kBAAkBtD,GAAG,CAACyD;gBACxB;YACF;QACF;QAEA,MAAMC,eAAe,CAACC;YACpB,+BAA+B;YAC/B,MAAMC,UAAU7E,UAAU4E,SAAS;gBACjCE,UAAU;gBACVC,KAAK;YACP;YAEA,OAAO,CAACC;gBACN,IAAIrF,KAAKsE,UAAU,CAACe,aAAa,CAACA,SAASC,UAAU,CAAC7B,OAAO;oBAC3D,OAAO;gBACT;gBAEA,OAAOyB,QAAQG;YACjB;QACF;QAEA,MAAME,gBAAgB;YACpB;eACI7B,eAAe,EAAE,GAAG;gBAAC;aAAyC;YAClE;YACA;YACA;YACA;eAEIvD,cAAcqF,cAAc,GAC5B;gBACE,wCAAwC;gBACxC,+CAA+C;gBAC/C;aACD,GACD,EAAE;eAEF,CAACjD,iBACD;gBAAC;aAA2D,GAC5D,EAAE;eAEFmB,eAAe,EAAE,GAAG5D;eACrB8E;SACJ;QAED,MAAMa,kBAAkBT,aAAaO;QAErC,MAAMG,gBAAgB;eACjBH;YACH;YACA;YACA;YACA;eACIpF,cAAcqF,cAAc,GAC5B;gBAAC;gBAA8B;aAA8B,GAC7D,EAAE;SACP,CAACf,MAAM,CAACvE;QACT,MAAMY,iBAAiBkE,aAAaU;QAEpC,MAAMC,uBAAuB;eACxBD;YACH;YACA;YACA;SACD;QACD,MAAME,wBAAwBZ,aAAaW;QAE3C,MAAME,gBAAgB;eACjBN;YACH,sEAAsE;YACtE,qEAAqE;YACrE,iCAAiC;YACjC;YACA;YACA;SACD,CAACd,MAAM,CAACvE;QAET,MAAM4F,gBAAgBd,aAAaa;QAEnC,MAAME,oBAAoB,IAAI7E;QAC9B,MAAM8E,2BAA2B,IAAI9E;QAErC,SAAS+E,iBAAiBC,IAAY,EAAErF,IAAY,EAAEsF,IAAiB;YACrEA,KAAK7E,GAAG,CACNtB,KAAKoG,QAAQ,CAAClE,SAASlC,KAAKuD,IAAI,CAAC2C,MAAMrF,OAAOwF,OAAO,CAAC,OAAO;QAEjE;QAEA,IAAI3C,cAAc;YAChBuC,iBACE,IACAlC,QAAQC,OAAO,CAAC,gDAChB+B;YAEFE,iBACE,IACAlC,QAAQC,OAAO,CAAC,+CAChB+B;QAEJ;QAEA;gBAEQvD;YADN,MAAM8D,gBAA0B;mBAC1B9D,CAAAA,sCAAAA,iCAAAA,kBAAmB+D,WAAW,qBAA9B/D,+BAAgCgE,MAAM,CAACC,KAAK,KAAI,EAAE;mBACnDjC;mBACAG;aACJ;YACD,MAAM+B,SAAS,MAAMnG,cAAc+F,eAAe;gBAChDJ,MAAMzD;gBACNkE,YAAY3E;gBACZ4E,cAAc;gBACd,MAAMC,UAASC,CAAC;oBACd,IAAI;wBACF,OAAO,MAAM7G,GAAG4G,QAAQ,CAACC,GAAG;oBAC9B,EAAE,OAAOC,GAAG;wBACV,IAAIrG,QAAQqG,MAAOA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,QAAO,GAAI;4BAC9D,+DAA+D;4BAC/D,2DAA2D;4BAC3D,oBAAoB;4BACpB,OAAO;wBACT;wBACA,MAAMD;oBACR;gBACF;gBACA,MAAME,UAASH,CAAC;oBACd,IAAI;wBACF,OAAO,MAAM7G,GAAGgH,QAAQ,CAACH;oBAC3B,EAAE,OAAOC,GAAG;wBACV,IACErG,QAAQqG,MACPA,CAAAA,EAAEC,IAAI,KAAK,YACVD,EAAEC,IAAI,KAAK,YACXD,EAAEC,IAAI,KAAK,SAAQ,GACrB;4BACA,OAAO;wBACT;wBACA,MAAMD;oBACR;gBACF;gBACA,MAAMG,MAAKJ,CAAC;oBACV,IAAI;wBACF,OAAO,MAAM7G,GAAGiH,IAAI,CAACJ;oBACvB,EAAE,OAAOC,GAAG;wBACV,IAAIrG,QAAQqG,MAAOA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,SAAQ,GAAI;4BAC/D,OAAO;wBACT;wBACA,MAAMD;oBACR;gBACF;gBACA,2CAA2C;gBAC3C,4CAA4C;gBAC5C,iCAAiC;gBACjCI,QAAOL,CAAC;oBACN,IAAIrB,gBAAgBqB,IAAI;wBACtB,OAAO;oBACT;oBAEA,mDAAmD;oBACnD,sDAAsD;oBACtD,oDAAoD;oBACpD,2DAA2D;oBAC3D,IACEA,EAAEnF,QAAQ,CAAC,0BACX,CAAC2E,cAAc3E,QAAQ,CAAC3B,KAAKuD,IAAI,CAACd,uBAAuBqE,KACzD;wBACA,OAAO;oBACT;oBACA,OAAO;gBACT;YACF;YACA,MAAM/F,UAAU2F,OAAO3F,OAAO;YAC9B,MAAMqG,WAAWV,OAAOU,QAAQ;YAChC,KAAK,MAAMvG,QAAQ6F,OAAOW,WAAW,CAAE;gBACrCD,SAAS9F,GAAG,CAACT;YACf;YAEA,MAAMyG,iBAAiBvH,uBAAuBqH,UAAUrG;YACxD,MAAMwG,qBAAqB,IAAIC;YAC/B,MAAMC,4BAA4B,IAAID;YAEtC,KAAK,MAAM,CAACE,SAASC,YAAY,IAAI;gBACnC;oBAACnD;oBAAeuB;iBAAkB;gBAClC;oBAACpB;oBAAsBqB;iBAAyB;aACjD,CAAoC;gBACnC,KAAK,MAAMnF,QAAQ6G,QAAS;wBAEpBJ;oBADN,MAAMM,WAAW;2BACXN,EAAAA,sBAAAA,eACDlG,GAAG,CAACpB,KAAKoG,QAAQ,CAAC3D,uBAAuB5B,2BADxCyG,oBAEArE,IAAI,OAAM,EAAE;qBACjB;oBACD0E,YAAYrG,GAAG,CAACtB,KAAKoG,QAAQ,CAAClE,SAASrB,MAAMwF,OAAO,CAAC,OAAO;oBAE5D,KAAK,MAAMwB,WAAWD,YAAY,EAAE,CAAE;wBACpC,MAAME,WAAW9H,KAAKuD,IAAI,CAACd,uBAAuBoF;wBAElD,IACE,CAACjH,aACCiH,SACAF,gBAAgB3B,2BACZJ,wBACA9E,gBACJC,SACA4G,gBAAgB3B,2BACZyB,4BACAF,qBAEN;4BACAI,YAAYrG,GAAG,CACbtB,KAAKoG,QAAQ,CAAClE,SAAS4F,UAAUzB,OAAO,CAAC,OAAO;wBAEpD;oBACF;gBACF;YACF;YAEA,MAAM,EAAE0B,iBAAiB,EAAE,GAAGvF,CAAAA,qCAAAA,kBAAmB+D,WAAW,KAAI,CAAC;YAEjE,MAAMyB,2BAA2B,IAAIR;YAErC,MAAMS,QAAQC,GAAG,CACf;mBACMH,oBACA/E,OAAO0E,OAAO,CAACK,qBACf,IAAIP;aACT,CAAC3D,GAAG,CAAC,OAAO,CAACsE,WAAWC,eAAe;gBACtC,MAAMC,QAAQF,UAAU7C,UAAU,CAAC;gBACnC,MAAMgD,UAAUH,UAAU7C,UAAU,CAAC;gBACrC,IAAIiD,QAAQJ;gBACZ,IAAIE,OAAO;oBACTE,QAAQ9H,iBAAiB8H,MAAMC,SAAS,CAAC,MAAMC,MAAM;gBACvD;gBACA,IAAIH,SAAS;oBACXC,QAAQ/H,kBAAkB+H,MAAMC,SAAS,CAAC,QAAQC,MAAM;gBAC1D;gBAEA,gEAAgE;gBAChE,yDAAyD;gBACzD,2DAA2D;gBAC3D,4BAA4B;gBAC5B,IAAIrG,YAAYT,QAAQ,CAAC4G,QAAQ;oBAC/B;gBACF;gBACA,MAAMG,kBAAkB1I,KAAKuD,IAAI,CAC/BrB,SACA,UACA,GAAGiG,UAAU,GAAG,CAAC;gBAEnB,MAAMQ,kBAAkB,GAAGD,gBAAgB,SAAS,CAAC;gBACrD,MAAME,gBAAgBC,KAAKC,KAAK,CAC9B,MAAM7I,GAAG4G,QAAQ,CAAC8B,iBAAiB;gBAMrC,MAAMI,iBAAiB/I,KAAKgJ,OAAO,CAACL;gBACpC,MAAMM,iBAAiB,IAAI/H;gBAE3B,KAAK,MAAML,QAAQ;uBAAIuH;oBAAgBM;iBAAgB,CAAE;wBAEjDpB;oBADN,MAAMM,WAAW;2BACXN,EAAAA,sBAAAA,eACDlG,GAAG,CAACpB,KAAKoG,QAAQ,CAAC3D,uBAAuB5B,2BADxCyG,oBAEArE,IAAI,OAAM,EAAE;qBACjB;oBACD,KAAK,MAAM4E,WAAWD,YAAY,EAAE,CAAE;wBACpC,IACE,CAAChH,aACCiH,SACA/B,eACA/E,SACAiH,2BAEF;4BACA,MAAMF,WAAW9H,KAAKuD,IAAI,CAACd,uBAAuBoF;4BAClD,MAAMqB,aAAalJ,KAChBoG,QAAQ,CAAC2C,gBAAgBjB,UACzBzB,OAAO,CAAC,OAAO;4BAClB4C,eAAe3H,GAAG,CAAC4H;wBACrB;oBACF;gBACF;gBAEA,KAAK,MAAMrI,QAAQ+H,cAAcO,KAAK,IAAI,EAAE,CAAE;oBAC5CF,eAAe3H,GAAG,CAACT;gBACrB;gBAEA,MAAMZ,GAAGmJ,SAAS,CAChBT,iBACAE,KAAKQ,SAAS,CAAC;oBACb,GAAGT,aAAa;oBAChBO,OAAO;2BAAIF;qBAAe,CAACK,IAAI;gBACjC;YAEJ;QAEJ;QAEA,MAAMC,cAAc;YAAC;YAAY;SAAQ;QAEzC,KAAK,MAAM7H,QAAQ6H,YAAa;YAC9B,MAAMC,aAAazF,QAAQC,OAAO,CAChC,CAAC,+BAA+B,EAAEtC,KAAK,gBAAgB,CAAC;YAE1D,MAAM+H,qBAAqBzJ,KAAKoG,QAAQ,CAAC3C,MAAM+F;YAE/C,MAAME,aAAa1J,KAAKuD,IAAI,CAC1BvD,KAAKgJ,OAAO,CAACQ,aACb,YACA;YAGF,KAAK,MAAMG,QAAQ,CAAA,MAAM1J,GAAG2J,OAAO,CAACF,WAAU,EAAG;gBAC/C,MAAMG,WAAW7J,KAAKoG,QAAQ,CAAC3C,MAAMzD,KAAKuD,IAAI,CAACmG,YAAYC;gBAC3D,IAAI,CAAC7I,eAAe+I,WAAW;oBAC7B5D,iBAAiBxC,MAAMoG,UAAU9D;oBACjCE,iBAAiBxC,MAAMoG,UAAU7D;gBACnC;YACF;YACAC,iBAAiBxC,MAAMgG,oBAAoB1D;YAC3CE,iBAAiBxC,MAAMgG,oBAAoBzD;QAC7C;QAEA,MAAMiC,QAAQC,GAAG,CAAC;YAChBjI,GAAGmJ,SAAS,CACV9F,uBACAuF,KAAKQ,SAAS,CAAC;gBACbS,SAAS;gBACTX,OAAOY,MAAMC,IAAI,CAACjE;YACpB;YAKF9F,GAAGmJ,SAAS,CACV5F,wBACAqF,KAAKQ,SAAS,CAAC;gBACbS,SAAS;gBACTX,OAAOY,MAAMC,IAAI,CAAChE;YACpB;SAKH;IACH;IAEF,gFAAgF;IAChF,MAAMiE,qBAAqB5H,cAAcc,UAAU,CAAC;IACpD,MAAM8G,mBAAmB5G,YAAY,CAAC;QACpC,MAAM6G,WACJnG,QAAQ;QACV,MAAMc,OAAO,CAACsF;YACZ,OAAO,IAAIlC,QAAQ,CAACjE,SAASoG;gBAC3BF,SACEC,SACA;oBAAEE,KAAKrI;oBAAKsI,OAAO;oBAAMlF,KAAK;gBAAK,GACnC,CAACmF,KAAKpB;oBACJ,IAAIoB,KAAK;wBACP,OAAOH,OAAOG;oBAChB;oBACAvG,QAAQmF;gBACV;YAEJ;QACF;QAEA,MAAM,EAAEpB,iBAAiB,EAAE,GAAGvF,CAAAA,qCAAAA,kBAAmB+D,WAAW,KAAI,CAAC;QAEjE,MAAM0B,QAAQC,GAAG,CACf;eACMH,oBAAoB/E,OAAO0E,OAAO,CAACK,qBAAqB,IAAIP;SACjE,CAAC3D,GAAG,CAAC,OAAO,CAACsE,UAAU;YACtB,MAAME,QAAQF,UAAU7C,UAAU,CAAC;YACnC,MAAMgD,UAAUH,UAAU7C,UAAU,CAAC;YACrC,IAAIiD,QAAQJ;YACZ,IAAIE,OAAO;gBACTE,QAAQ9H,iBAAiB0H;YAC3B;YACA,IAAIG,SAAS;gBACXC,QAAQ/H,kBAAkB2H;YAC5B;YAEA,IAAI/F,YAAYT,QAAQ,CAAC4G,QAAQ;gBAC/B;YACF;YAEA,kCAAkC;YAClC,IAAIpG,kBAAkBqI,cAAc,CAACjC,QAAQ;gBAC3C;YACF;YAEA,MAAMkC,mBAAmB,IAAIvJ;YAC7B,MAAMwJ,mBAAmB,IAAIxJ;YAC7B,KAAK,MAAMyJ,WAAWzH,gBAAiB;gBACrC,MAAMgC,UAAU7E,UAAUsK,SAAS;oBAAEvF,KAAK;oBAAMD,UAAU;gBAAK;gBAC/D,IAAID,QAAQqD,QAAQ;oBAClB,KAAK,MAAMqC,WAAW/H,yBAAyB,CAAC8H,QAAQ,CAAE;wBACxDF,iBAAiBnJ,GAAG,CAACsJ,QAAQvE,OAAO,CAAC,OAAO;oBAC9C;gBACF;YACF;YAEA,KAAK,MAAMsE,WAAW5H,gBAAiB;gBACrC,MAAMmC,UAAU7E,UAAUsK,SAAS;oBAAEvF,KAAK;oBAAMD,UAAU;gBAAK;gBAC/D,IAAID,QAAQqD,QAAQ;oBAClB,KAAK,MAAMxD,WAAWjC,yBAAyB,CAAC6H,QAAQ,CAAE;wBACxDD,iBAAiBpJ,GAAG,CAACyD;oBACvB;gBACF;YACF;YAEA,IAAI,EAAC0F,oCAAAA,iBAAkBhJ,IAAI,KAAI,EAACiJ,oCAAAA,iBAAkBjJ,IAAI,GAAE;gBACtD;YACF;YAEA,MAAMoJ,YAAY7K,KAAKuD,IAAI,CACzBrB,SACA,CAAC,MAAM,CAAC,EACR,GAAGiG,UAAU,YAAY,CAAC;YAE5B,MAAM2C,UAAU9K,KAAKgJ,OAAO,CAAC6B;YAC7B,MAAME,eAAelC,KAAKC,KAAK,CAAC,MAAM7I,GAAG4G,QAAQ,CAACgE,WAAW;YAC7D,MAAMlJ,WAAqB,EAAE;YAC7B,MAAMqJ,wBAAwB,IAAIxD;YAElC,IAAIiD,oCAAAA,iBAAkBhJ,IAAI,EAAE;gBAC1B,MAAMwG,QAAQC,GAAG,CACf;uBAAIuC;iBAAiB,CAAC5G,GAAG,CAAC,OAAOoH;oBAC/B,MAAMC,UAAU,MAAMrG,KAAKoG;oBAC3B,MAAME,kBAAkBH,sBAAsB5J,GAAG,CAC/C6J,gBACG;2BACAC,QAAQrH,GAAG,CAAC,CAAChD;4BACd,OAAOb,KAAKoG,QAAQ,CAAC0E,SAAS9K,KAAKuD,IAAI,CAACvB,KAAKnB;wBAC/C;qBACD;oBACDc,SAAS0C,IAAI,IAAI8G;oBACjBH,sBAAsB3J,GAAG,CAAC4J,aAAaE;gBACzC;YAEJ;YACA,MAAMC,WAAW,IAAIlK,IAAI;mBAAI6J,aAAa5B,KAAK;mBAAKxH;aAAS;YAE7D,IAAI+I,oCAAAA,iBAAkBjJ,IAAI,EAAE;gBAC1B,MAAM4J,gBAAgB;uBAAIX;iBAAiB,CAAC7G,GAAG,CAAC,CAACkB,UAC/C/E,KAAKuD,IAAI,CAACvB,KAAK+C;gBAGjB,6BAA6B;gBAC7B,MAAMG,UAAU7E,UAAUgL,eAAe;oBACvCjG,KAAK;oBACLD,UAAU;gBACZ;gBAEAiG,SAAStG,OAAO,CAAC,CAACjE;oBAChB,IAAIqE,QAAQlF,KAAKuD,IAAI,CAACuH,SAASjK,QAAQ;wBACrCuK,SAASE,MAAM,CAACzK;oBAClB;gBACF;YACF;YAEA,qDAAqD;YACrD,MAAMZ,GAAGmJ,SAAS,CAChByB,WACAhC,KAAKQ,SAAS,CAAC;gBACbS,SAASiB,aAAajB,OAAO;gBAC7BX,OAAO;uBAAIiC;iBAAS;YACtB;QAEJ;IAEJ;IAEAzK,MAAM,CAAC,uBAAuB,EAAEgC,KAAKC,GAAG,KAAKF,UAAU,EAAE,CAAC;AAC5D"}