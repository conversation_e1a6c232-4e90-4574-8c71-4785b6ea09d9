{"version": 3, "sources": ["../../../src/server/app-render/work-unit-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { WorkUnitAsyncStorage } from './work-unit-async-storage.external'\n\nexport const workUnitAsyncStorageInstance: WorkUnitAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["createAsyncLocalStorage", "workUnitAsyncStorageInstance"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,wBAAuB;AAG/D,OAAO,MAAMC,+BACXD,0BAAyB"}