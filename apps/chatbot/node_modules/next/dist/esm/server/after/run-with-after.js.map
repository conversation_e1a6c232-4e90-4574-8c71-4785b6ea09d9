{"version": 3, "sources": ["../../../src/server/after/run-with-after.ts"], "sourcesContent": ["import { DetachedPromise } from '../../lib/detached-promise'\nimport { CloseController } from '../web/web-on-close'\nimport type { AfterContextOpts } from './after-context'\nimport { AwaiterOnce } from './awaiter'\n\ntype Ctx = {\n  waitUntil: NonNullable<AfterContextOpts['waitUntil']>\n  onClose: NonNullable<AfterContextOpts['onClose']>\n  onTaskError: NonNullable<AfterContextOpts['onTaskError']>\n}\n\nexport class AfterRunner {\n  private awaiter = new AwaiterOnce()\n  private closeController = new CloseController()\n  private finishedWithoutErrors = new DetachedPromise<void>()\n\n  readonly context: Ctx = {\n    waitUntil: this.awaiter.waitUntil.bind(this.awaiter),\n    onClose: this.closeController.onClose.bind(this.closeController),\n    onTaskError: (error) => this.finishedWithoutErrors.reject(error),\n  }\n\n  public async executeAfter() {\n    this.closeController.dispatchClose()\n    await this.awaiter.awaiting()\n\n    // if we got an error while running the callbacks,\n    // thenthis is a noop, because the promise is already rejected\n    this.finishedWithoutErrors.resolve()\n\n    return this.finishedWithoutErrors.promise\n  }\n}\n"], "names": ["Detached<PERSON>romise", "CloseController", "Awaiter<PERSON>nce", "After<PERSON><PERSON>ner", "executeAfter", "closeController", "dispatchClose", "awaiter", "awaiting", "finishedWithoutErrors", "resolve", "promise", "context", "waitUntil", "bind", "onClose", "onTaskError", "error", "reject"], "mappings": "AAAA,SAASA,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,WAAW,QAAQ,YAAW;AAQvC,OAAO,MAAMC;IAWX,MAAaC,eAAe;QAC1B,IAAI,CAACC,eAAe,CAACC,aAAa;QAClC,MAAM,IAAI,CAACC,OAAO,CAACC,QAAQ;QAE3B,kDAAkD;QAClD,8DAA8D;QAC9D,IAAI,CAACC,qBAAqB,CAACC,OAAO;QAElC,OAAO,IAAI,CAACD,qBAAqB,CAACE,OAAO;IAC3C;;aAnBQJ,UAAU,IAAIL;aACdG,kBAAkB,IAAIJ;aACtBQ,wBAAwB,IAAIT;aAE3BY,UAAe;YACtBC,WAAW,IAAI,CAACN,OAAO,CAACM,SAAS,CAACC,IAAI,CAAC,IAAI,CAACP,OAAO;YACnDQ,SAAS,IAAI,CAACV,eAAe,CAACU,OAAO,CAACD,IAAI,CAAC,IAAI,CAACT,eAAe;YAC/DW,aAAa,CAACC,QAAU,IAAI,CAACR,qBAAqB,CAACS,MAAM,CAACD;QAC5D;;AAYF"}