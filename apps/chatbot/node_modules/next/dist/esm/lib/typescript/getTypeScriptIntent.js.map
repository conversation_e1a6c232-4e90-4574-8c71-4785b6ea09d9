{"version": 3, "sources": ["../../../src/lib/typescript/getTypeScriptIntent.ts"], "sourcesContent": ["import { existsSync, promises as fs } from 'fs'\nimport path from 'path'\nimport { recursiveReadDir } from '../recursive-readdir'\n\nexport type TypeScriptIntent = { firstTimeSetup: boolean }\n\nexport async function getTypeScriptIntent(\n  baseDir: string,\n  intentDirs: string[],\n  tsconfigPath: string\n): Promise<TypeScriptIntent | false> {\n  const resolvedTsConfigPath = path.join(baseDir, tsconfigPath)\n\n  // The integration turns on if we find a `tsconfig.json` in the user's\n  // project.\n  const hasTypeScriptConfiguration = existsSync(resolvedTsConfigPath)\n  if (hasTypeScriptConfiguration) {\n    const content = await fs\n      .readFile(resolvedTsConfigPath, { encoding: 'utf8' })\n      .then(\n        (txt) => txt.trim(),\n        () => null\n      )\n    return { firstTimeSetup: content === '' || content === '{}' }\n  }\n\n  // Next.js also offers a friendly setup mode that bootstraps a TypeScript\n  // project for the user when we detect TypeScript files. So, we need to check\n  // the `pages/` directory for a TypeScript file.\n  // Checking all directories is too slow, so this is a happy medium.\n  const tsFilesRegex = /.*\\.(ts|tsx)$/\n  const excludedRegex = /(node_modules|.*\\.d\\.ts$)/\n  for (const dir of intentDirs) {\n    const typescriptFiles = await recursiveReadDir(dir, {\n      pathnameFilter: (name) => tsFilesRegex.test(name),\n      ignoreFilter: (name) => excludedRegex.test(name),\n    })\n    if (typescriptFiles.length) {\n      return { firstTimeSetup: true }\n    }\n  }\n\n  return false\n}\n"], "names": ["existsSync", "promises", "fs", "path", "recursiveReadDir", "getTypeScriptIntent", "baseDir", "intentDirs", "tsconfigPath", "resolvedTsConfigPath", "join", "hasTypeScriptConfiguration", "content", "readFile", "encoding", "then", "txt", "trim", "firstTimeSetup", "tsFilesRegex", "excludedRegex", "dir", "typescriptFiles", "pathnameFilter", "name", "test", "ignoreFilter", "length"], "mappings": "AAAA,SAASA,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,OAAOC,UAAU,OAAM;AACvB,SAASC,gBAAgB,QAAQ,uBAAsB;AAIvD,OAAO,eAAeC,oBACpBC,OAAe,EACfC,UAAoB,EACpBC,YAAoB;IAEpB,MAAMC,uBAAuBN,KAAKO,IAAI,CAACJ,SAASE;IAEhD,sEAAsE;IACtE,WAAW;IACX,MAAMG,6BAA6BX,WAAWS;IAC9C,IAAIE,4BAA4B;QAC9B,MAAMC,UAAU,MAAMV,GACnBW,QAAQ,CAACJ,sBAAsB;YAAEK,UAAU;QAAO,GAClDC,IAAI,CACH,CAACC,MAAQA,IAAIC,IAAI,IACjB,IAAM;QAEV,OAAO;YAAEC,gBAAgBN,YAAY,MAAMA,YAAY;QAAK;IAC9D;IAEA,yEAAyE;IACzE,6EAA6E;IAC7E,gDAAgD;IAChD,mEAAmE;IACnE,MAAMO,eAAe;IACrB,MAAMC,gBAAgB;IACtB,KAAK,MAAMC,OAAOd,WAAY;QAC5B,MAAMe,kBAAkB,MAAMlB,iBAAiBiB,KAAK;YAClDE,gBAAgB,CAACC,OAASL,aAAaM,IAAI,CAACD;YAC5CE,cAAc,CAACF,OAASJ,cAAcK,IAAI,CAACD;QAC7C;QACA,IAAIF,gBAAgBK,MAAM,EAAE;YAC1B,OAAO;gBAAET,gBAAgB;YAAK;QAChC;IACF;IAEA,OAAO;AACT"}