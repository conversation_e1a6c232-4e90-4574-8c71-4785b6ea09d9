{"version": 3, "sources": ["../../../src/lib/helpers/get-npx-command.ts"], "sourcesContent": ["import { execSync } from 'child_process'\nimport { getPkgManager } from './get-pkg-manager'\n\nexport function getNpxCommand(baseDir: string) {\n  const pkgManager = getPkgManager(baseDir)\n  let command = 'npx'\n  if (pkgManager === 'pnpm') {\n    command = 'pnpm dlx'\n  } else if (pkgManager === 'yarn') {\n    try {\n      execSync('yarn dlx --help', { stdio: 'ignore' })\n      command = 'yarn dlx'\n    } catch {}\n  }\n\n  return command\n}\n"], "names": ["execSync", "getPkgManager", "getNpxCommand", "baseDir", "pkgManager", "command", "stdio"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAe;AACxC,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,OAAO,SAASC,cAAcC,OAAe;IAC3C,MAAMC,aAAaH,cAAcE;IACjC,IAAIE,UAAU;IACd,IAAID,eAAe,QAAQ;QACzBC,UAAU;IACZ,OAAO,IAAID,eAAe,QAAQ;QAChC,IAAI;YACFJ,SAAS,mBAAmB;gBAAEM,OAAO;YAAS;YAC9CD,UAAU;QACZ,EAAE,OAAM,CAAC;IACX;IAEA,OAAOA;AACT"}