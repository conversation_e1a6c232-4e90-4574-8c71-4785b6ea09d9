{"version": 3, "sources": ["../../src/lib/inline-static-env.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport crypto from 'crypto'\nimport { promisify } from 'util'\nimport globOriginal from 'next/dist/compiled/glob'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { getNextConfigEnv, getStaticEnv } from './static-env'\n\nconst glob = promisify(globOriginal)\n\nexport async function inlineStaticEnv({\n  distDir,\n  config,\n}: {\n  distDir: string\n  config: NextConfigComplete\n}) {\n  const nextConfigEnv = getNextConfigEnv(config)\n  const staticEnv = getStaticEnv(config)\n\n  const serverDir = path.join(distDir, 'server')\n  const serverChunks = await glob('**/*.{js,json,js.map}', {\n    cwd: serverDir,\n  })\n  const clientDir = path.join(distDir, 'static')\n  const clientChunks = await glob('**/*.{js,json,js.map}', {\n    cwd: clientDir,\n  })\n  const manifestChunks = await glob('*.{js,json,js.map}', {\n    cwd: distDir,\n  })\n\n  const inlineSema = new Sema(8)\n  const nextConfigEnvKeys = Object.keys(nextConfigEnv).map((item) =>\n    item.split('process.env.').pop()\n  )\n\n  const builtRegEx = new RegExp(\n    `[\\\\w]{1,}(\\\\.env)?\\\\.(?:NEXT_PUBLIC_[\\\\w]{1,}${nextConfigEnvKeys.length ? '|' + nextConfigEnvKeys.join('|') : ''})`,\n    'g'\n  )\n  const changedClientFiles: Array<{ file: string; content: string }> = []\n  const filesToCheck = new Set<string>(\n    manifestChunks.map((f) => path.join(distDir, f))\n  )\n\n  for (const [parentDir, files] of [\n    [serverDir, serverChunks],\n    [clientDir, clientChunks],\n  ] as const) {\n    await Promise.all(\n      files.map(async (file) => {\n        await inlineSema.acquire()\n        const filepath = path.join(parentDir, file)\n        const content = await fs.promises.readFile(filepath, 'utf8')\n        const newContent = content.replace(builtRegEx, (match) => {\n          let normalizedMatch = `process.env.${match.split('.').pop()}`\n\n          if (staticEnv[normalizedMatch]) {\n            return JSON.stringify(staticEnv[normalizedMatch])\n          }\n          return match\n        })\n\n        await fs.promises.writeFile(filepath, newContent)\n\n        if (content !== newContent && parentDir === clientDir) {\n          changedClientFiles.push({ file, content: newContent })\n        }\n        filesToCheck.add(filepath)\n        inlineSema.release()\n      })\n    )\n  }\n  const hashChanges: Array<{\n    originalHash: string\n    newHash: string\n  }> = []\n\n  // hashes need updating for any changed client files\n  for (const { file, content } of changedClientFiles) {\n    // hash is 16 chars currently for all client chunks\n    const originalHash = file.match(/([a-z0-9]{16})\\./)?.[1] || ''\n\n    if (!originalHash) {\n      throw new Error(\n        `Invariant: client chunk changed but failed to detect hash ${file}`\n      )\n    }\n    const newHash = crypto\n      .createHash('sha256')\n      .update(content)\n      .digest('hex')\n      .substring(0, 16)\n\n    hashChanges.push({ originalHash, newHash })\n\n    const filepath = path.join(clientDir, file)\n    const newFilepath = filepath.replace(originalHash, newHash)\n\n    filesToCheck.delete(filepath)\n    filesToCheck.add(newFilepath)\n\n    await fs.promises.rename(filepath, newFilepath)\n  }\n\n  // update build-manifest and webpack-runtime with new hashes\n  for (let file of filesToCheck) {\n    const content = await fs.promises.readFile(file, 'utf-8')\n    let newContent = content\n\n    for (const { originalHash, newHash } of hashChanges) {\n      newContent = newContent.replaceAll(originalHash, newHash)\n    }\n    if (content !== newContent) {\n      await fs.promises.writeFile(file, newContent)\n    }\n  }\n}\n"], "names": ["fs", "path", "crypto", "promisify", "globOriginal", "<PERSON><PERSON>", "getNextConfigEnv", "getStaticEnv", "glob", "inlineStaticEnv", "distDir", "config", "nextConfigEnv", "staticEnv", "serverDir", "join", "serverChunks", "cwd", "clientDir", "clientChunks", "manifestChunks", "inlineSema", "nextConfigEnvKeys", "Object", "keys", "map", "item", "split", "pop", "builtRegEx", "RegExp", "length", "changedClientFiles", "filesToCheck", "Set", "f", "parentDir", "files", "Promise", "all", "file", "acquire", "filepath", "content", "promises", "readFile", "newContent", "replace", "match", "normalizedMatch", "JSON", "stringify", "writeFile", "push", "add", "release", "hashChanges", "originalHash", "Error", "newHash", "createHash", "update", "digest", "substring", "newFilepath", "delete", "rename", "replaceAll"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,OAAOC,YAAY,SAAQ;AAC3B,SAASC,SAAS,QAAQ,OAAM;AAChC,OAAOC,kBAAkB,0BAAyB;AAClD,SAASC,IAAI,QAAQ,gCAA+B;AAEpD,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,eAAc;AAE7D,MAAMC,OAAOL,UAAUC;AAEvB,OAAO,eAAeK,gBAAgB,EACpCC,OAAO,EACPC,MAAM,EAIP;IACC,MAAMC,gBAAgBN,iBAAiBK;IACvC,MAAME,YAAYN,aAAaI;IAE/B,MAAMG,YAAYb,KAAKc,IAAI,CAACL,SAAS;IACrC,MAAMM,eAAe,MAAMR,KAAK,yBAAyB;QACvDS,KAAKH;IACP;IACA,MAAMI,YAAYjB,KAAKc,IAAI,CAACL,SAAS;IACrC,MAAMS,eAAe,MAAMX,KAAK,yBAAyB;QACvDS,KAAKC;IACP;IACA,MAAME,iBAAiB,MAAMZ,KAAK,sBAAsB;QACtDS,KAAKP;IACP;IAEA,MAAMW,aAAa,IAAIhB,KAAK;IAC5B,MAAMiB,oBAAoBC,OAAOC,IAAI,CAACZ,eAAea,GAAG,CAAC,CAACC,OACxDA,KAAKC,KAAK,CAAC,gBAAgBC,GAAG;IAGhC,MAAMC,aAAa,IAAIC,OACrB,CAAC,6CAA6C,EAAER,kBAAkBS,MAAM,GAAG,MAAMT,kBAAkBP,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,EACpH;IAEF,MAAMiB,qBAA+D,EAAE;IACvE,MAAMC,eAAe,IAAIC,IACvBd,eAAeK,GAAG,CAAC,CAACU,IAAMlC,KAAKc,IAAI,CAACL,SAASyB;IAG/C,KAAK,MAAM,CAACC,WAAWC,MAAM,IAAI;QAC/B;YAACvB;YAAWE;SAAa;QACzB;YAACE;YAAWC;SAAa;KAC1B,CAAW;QACV,MAAMmB,QAAQC,GAAG,CACfF,MAAMZ,GAAG,CAAC,OAAOe;YACf,MAAMnB,WAAWoB,OAAO;YACxB,MAAMC,WAAWzC,KAAKc,IAAI,CAACqB,WAAWI;YACtC,MAAMG,UAAU,MAAM3C,GAAG4C,QAAQ,CAACC,QAAQ,CAACH,UAAU;YACrD,MAAMI,aAAaH,QAAQI,OAAO,CAAClB,YAAY,CAACmB;gBAC9C,IAAIC,kBAAkB,CAAC,YAAY,EAAED,MAAMrB,KAAK,CAAC,KAAKC,GAAG,IAAI;gBAE7D,IAAIf,SAAS,CAACoC,gBAAgB,EAAE;oBAC9B,OAAOC,KAAKC,SAAS,CAACtC,SAAS,CAACoC,gBAAgB;gBAClD;gBACA,OAAOD;YACT;YAEA,MAAMhD,GAAG4C,QAAQ,CAACQ,SAAS,CAACV,UAAUI;YAEtC,IAAIH,YAAYG,cAAcV,cAAclB,WAAW;gBACrDc,mBAAmBqB,IAAI,CAAC;oBAAEb;oBAAMG,SAASG;gBAAW;YACtD;YACAb,aAAaqB,GAAG,CAACZ;YACjBrB,WAAWkC,OAAO;QACpB;IAEJ;IACA,MAAMC,cAGD,EAAE;IAEP,oDAAoD;IACpD,KAAK,MAAM,EAAEhB,IAAI,EAAEG,OAAO,EAAE,IAAIX,mBAAoB;YAE7BQ;QADrB,mDAAmD;QACnD,MAAMiB,eAAejB,EAAAA,cAAAA,KAAKQ,KAAK,CAAC,wCAAXR,WAAgC,CAAC,EAAE,KAAI;QAE5D,IAAI,CAACiB,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,0DAA0D,EAAElB,MAAM,GAD/D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMmB,UAAUzD,OACb0D,UAAU,CAAC,UACXC,MAAM,CAAClB,SACPmB,MAAM,CAAC,OACPC,SAAS,CAAC,GAAG;QAEhBP,YAAYH,IAAI,CAAC;YAAEI;YAAcE;QAAQ;QAEzC,MAAMjB,WAAWzC,KAAKc,IAAI,CAACG,WAAWsB;QACtC,MAAMwB,cAActB,SAASK,OAAO,CAACU,cAAcE;QAEnD1B,aAAagC,MAAM,CAACvB;QACpBT,aAAaqB,GAAG,CAACU;QAEjB,MAAMhE,GAAG4C,QAAQ,CAACsB,MAAM,CAACxB,UAAUsB;IACrC;IAEA,4DAA4D;IAC5D,KAAK,IAAIxB,QAAQP,aAAc;QAC7B,MAAMU,UAAU,MAAM3C,GAAG4C,QAAQ,CAACC,QAAQ,CAACL,MAAM;QACjD,IAAIM,aAAaH;QAEjB,KAAK,MAAM,EAAEc,YAAY,EAAEE,OAAO,EAAE,IAAIH,YAAa;YACnDV,aAAaA,WAAWqB,UAAU,CAACV,cAAcE;QACnD;QACA,IAAIhB,YAAYG,YAAY;YAC1B,MAAM9C,GAAG4C,QAAQ,CAACQ,SAAS,CAACZ,MAAMM;QACpC;IACF;AACF"}