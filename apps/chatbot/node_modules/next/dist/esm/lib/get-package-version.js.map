{"version": 3, "sources": ["../../src/lib/get-package-version.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport findUp from 'next/dist/compiled/find-up'\nimport JSON5 from 'next/dist/compiled/json5'\nimport * as path from 'path'\n\ntype PackageJsonDependencies = {\n  dependencies: Record<string, string>\n  devDependencies: Record<string, string>\n}\n\nlet cachedDeps: Promise<PackageJsonDependencies>\n\nexport function getDependencies({\n  cwd,\n}: {\n  cwd: string\n}): Promise<PackageJsonDependencies> {\n  if (cachedDeps) {\n    return cachedDeps\n  }\n\n  return (cachedDeps = (async () => {\n    const configurationPath: string | undefined = await findUp('package.json', {\n      cwd,\n    })\n    if (!configurationPath) {\n      return { dependencies: {}, devDependencies: {} }\n    }\n\n    const content = await fs.readFile(configurationPath, 'utf-8')\n    const packageJson: any = JSON5.parse(content)\n\n    const { dependencies = {}, devDependencies = {} } = packageJson || {}\n    return { dependencies, devDependencies }\n  })())\n}\n\nexport async function getPackageVersion({\n  cwd,\n  name,\n}: {\n  cwd: string\n  name: string\n}): Promise<string | null> {\n  const { dependencies, devDependencies } = await getDependencies({ cwd })\n  if (!(dependencies[name] || devDependencies[name])) {\n    return null\n  }\n\n  const cwd2 =\n    cwd.endsWith(path.posix.sep) || cwd.endsWith(path.win32.sep)\n      ? cwd\n      : `${cwd}/`\n\n  try {\n    const targetPath = require.resolve(`${name}/package.json`, {\n      paths: [cwd2],\n    })\n    const targetContent = await fs.readFile(targetPath, 'utf-8')\n    return JSON5.parse(targetContent).version ?? null\n  } catch {\n    return null\n  }\n}\n"], "names": ["promises", "fs", "findUp", "JSON5", "path", "cachedDeps", "getDependencies", "cwd", "configurationPath", "dependencies", "devDependencies", "content", "readFile", "packageJson", "parse", "getPackageVersion", "name", "cwd2", "endsWith", "posix", "sep", "win32", "targetPath", "require", "resolve", "paths", "targetContent", "version"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,YAAY,6BAA4B;AAC/C,OAAOC,WAAW,2BAA0B;AAC5C,YAAYC,UAAU,OAAM;AAO5B,IAAIC;AAEJ,OAAO,SAASC,gBAAgB,EAC9BC,GAAG,EAGJ;IACC,IAAIF,YAAY;QACd,OAAOA;IACT;IAEA,OAAQA,aAAa,AAAC,CAAA;QACpB,MAAMG,oBAAwC,MAAMN,OAAO,gBAAgB;YACzEK;QACF;QACA,IAAI,CAACC,mBAAmB;YACtB,OAAO;gBAAEC,cAAc,CAAC;gBAAGC,iBAAiB,CAAC;YAAE;QACjD;QAEA,MAAMC,UAAU,MAAMV,GAAGW,QAAQ,CAACJ,mBAAmB;QACrD,MAAMK,cAAmBV,MAAMW,KAAK,CAACH;QAErC,MAAM,EAAEF,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGG,eAAe,CAAC;QACpE,OAAO;YAAEJ;YAAcC;QAAgB;IACzC,CAAA;AACF;AAEA,OAAO,eAAeK,kBAAkB,EACtCR,GAAG,EACHS,IAAI,EAIL;IACC,MAAM,EAAEP,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMJ,gBAAgB;QAAEC;IAAI;IACtE,IAAI,CAAEE,CAAAA,YAAY,CAACO,KAAK,IAAIN,eAAe,CAACM,KAAK,AAAD,GAAI;QAClD,OAAO;IACT;IAEA,MAAMC,OACJV,IAAIW,QAAQ,CAACd,KAAKe,KAAK,CAACC,GAAG,KAAKb,IAAIW,QAAQ,CAACd,KAAKiB,KAAK,CAACD,GAAG,IACvDb,MACA,GAAGA,IAAI,CAAC,CAAC;IAEf,IAAI;QACF,MAAMe,aAAaC,QAAQC,OAAO,CAAC,GAAGR,KAAK,aAAa,CAAC,EAAE;YACzDS,OAAO;gBAACR;aAAK;QACf;QACA,MAAMS,gBAAgB,MAAMzB,GAAGW,QAAQ,CAACU,YAAY;QACpD,OAAOnB,MAAMW,KAAK,CAACY,eAAeC,OAAO,IAAI;IAC/C,EAAE,OAAM;QACN,OAAO;IACT;AACF"}