{"version": 3, "sources": ["../../../src/lib/memory/gc-observer.ts"], "sourcesContent": ["import { PerformanceObserver } from 'perf_hooks'\nimport { warn } from '../../build/output/log'\nimport { bold } from '../picocolors'\n\nconst LONG_RUNNING_GC_THRESHOLD_MS = 15\n\nconst gcEvents: PerformanceEntry[] = []\nconst obs = new PerformanceObserver((list) => {\n  const entry = list.getEntries()[0]\n  gcEvents.push(entry)\n\n  if (entry.duration > LONG_RUNNING_GC_THRESHOLD_MS) {\n    warn(bold(`Long running GC detected: ${entry.duration.toFixed(2)}ms`))\n  }\n})\n\n/**\n * Starts recording garbage collection events in the process and warn on long\n * running GCs. To disable, call `stopObservingGc`.\n */\nexport function startObservingGc() {\n  obs.observe({ entryTypes: ['gc'] })\n}\n\nexport function stopObservingGc() {\n  obs.disconnect()\n}\n\n/**\n * Returns all recorded garbage collection events. This function will only\n * return information from when `startObservingGc` was enabled and before\n * `stopObservingGc` was called.\n */\nexport function getGcEvents() {\n  return gcEvents\n}\n"], "names": ["PerformanceObserver", "warn", "bold", "LONG_RUNNING_GC_THRESHOLD_MS", "gcEvents", "obs", "list", "entry", "getEntries", "push", "duration", "toFixed", "startObservingGc", "observe", "entryTypes", "stopObservingGc", "disconnect", "getGcEvents"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,aAAY;AAChD,SAASC,IAAI,QAAQ,yBAAwB;AAC7C,SAASC,IAAI,QAAQ,gBAAe;AAEpC,MAAMC,+BAA+B;AAErC,MAAMC,WAA+B,EAAE;AACvC,MAAMC,MAAM,IAAIL,oBAAoB,CAACM;IACnC,MAAMC,QAAQD,KAAKE,UAAU,EAAE,CAAC,EAAE;IAClCJ,SAASK,IAAI,CAACF;IAEd,IAAIA,MAAMG,QAAQ,GAAGP,8BAA8B;QACjDF,KAAKC,KAAK,CAAC,0BAA0B,EAAEK,MAAMG,QAAQ,CAACC,OAAO,CAAC,GAAG,EAAE,CAAC;IACtE;AACF;AAEA;;;CAGC,GACD,OAAO,SAASC;IACdP,IAAIQ,OAAO,CAAC;QAAEC,YAAY;YAAC;SAAK;IAAC;AACnC;AAEA,OAAO,SAASC;IACdV,IAAIW,UAAU;AAChB;AAEA;;;;CAIC,GACD,OAAO,SAASC;IACd,OAAOb;AACT"}