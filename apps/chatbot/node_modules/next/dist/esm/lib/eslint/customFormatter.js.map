{"version": 3, "sources": ["../../../src/lib/eslint/customFormatter.ts"], "sourcesContent": ["import { bold, cyan, gray, red, yellow } from '../picocolors'\nimport path from 'path'\n\n// eslint-disable-next-line no-shadow\nexport enum MessageSeverity {\n  Warning = 1,\n  Error = 2,\n}\n\ninterface LintMessage {\n  ruleId: string | null\n  severity: 1 | 2\n  message: string\n  line: number\n  column: number\n}\n\nexport interface LintResult {\n  filePath: string\n  messages: LintMessage[]\n  errorCount: number\n  warningCount: number\n  output?: string\n  source?: string\n}\n\nfunction pluginCount(messages: LintMessage[]): {\n  nextPluginErrorCount: number\n  nextPluginWarningCount: number\n} {\n  let nextPluginWarningCount = 0\n  let nextPluginErrorCount = 0\n\n  for (let i = 0; i < messages.length; i++) {\n    const { severity, ruleId } = messages[i]\n\n    if (ruleId?.includes('@next/next')) {\n      if (severity === MessageSeverity.Warning) {\n        nextPluginWarningCount += 1\n      } else {\n        nextPluginErrorCount += 1\n      }\n    }\n  }\n\n  return {\n    nextPluginErrorCount,\n    nextPluginWarningCount,\n  }\n}\n\nfunction formatMessage(\n  dir: string,\n  messages: LintMessage[],\n  filePath: string\n): string {\n  let fileName = path.posix.normalize(\n    path.relative(dir, filePath).replace(/\\\\/g, '/')\n  )\n\n  if (!fileName.startsWith('.')) {\n    fileName = './' + fileName\n  }\n\n  let output = '\\n' + cyan(fileName)\n\n  for (let i = 0; i < messages.length; i++) {\n    const { message, severity, line, column, ruleId } = messages[i]\n\n    output = output + '\\n'\n\n    if (line && column) {\n      output =\n        output +\n        yellow(line.toString()) +\n        ':' +\n        yellow(column.toString()) +\n        '  '\n    }\n\n    if (severity === MessageSeverity.Warning) {\n      output += yellow(bold('Warning')) + ': '\n    } else {\n      output += red(bold('Error')) + ': '\n    }\n\n    output += message\n\n    if (ruleId) {\n      output += '  ' + gray(bold(ruleId))\n    }\n  }\n\n  return output\n}\n\nexport async function formatResults(\n  baseDir: string,\n  results: LintResult[],\n  format: (r: LintResult[]) => string | Promise<string>\n): Promise<{\n  output: string\n  outputWithMessages: string\n  totalNextPluginErrorCount: number\n  totalNextPluginWarningCount: number\n}> {\n  let totalNextPluginErrorCount = 0\n  let totalNextPluginWarningCount = 0\n  let resultsWithMessages = results.filter(({ messages }) => messages?.length)\n\n  // Track number of Next.js plugin errors and warnings\n  resultsWithMessages.forEach(({ messages }) => {\n    const res = pluginCount(messages)\n    totalNextPluginErrorCount += res.nextPluginErrorCount\n    totalNextPluginWarningCount += res.nextPluginWarningCount\n  })\n\n  // Use user defined formatter or Next.js's built-in custom formatter\n  const output = format\n    ? await format(resultsWithMessages)\n    : resultsWithMessages\n        .map(({ messages, filePath }) =>\n          formatMessage(baseDir, messages, filePath)\n        )\n        .join('\\n')\n\n  return {\n    output: output,\n    outputWithMessages:\n      resultsWithMessages.length > 0\n        ? output +\n          `\\n\\n${cyan(\n            'info'\n          )}  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules`\n        : '',\n    totalNextPluginErrorCount,\n    totalNextPluginWarningCount,\n  }\n}\n"], "names": ["bold", "cyan", "gray", "red", "yellow", "path", "MessageSeverity", "pluginCount", "messages", "nextPluginWarningCount", "nextPluginErrorCount", "i", "length", "severity", "ruleId", "includes", "formatMessage", "dir", "filePath", "fileName", "posix", "normalize", "relative", "replace", "startsWith", "output", "message", "line", "column", "toString", "formatResults", "baseDir", "results", "format", "totalNextPluginErrorCount", "totalNextPluginWarningCount", "resultsWithMessages", "filter", "for<PERSON>ach", "res", "map", "join", "outputWithMessages"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,gBAAe;AAC7D,OAAOC,UAAU,OAAM;AAEvB,qCAAqC;AACrC,OAAO,IAAA,AAAKC,yCAAAA;;;WAAAA;MAGX;AAmBD,SAASC,YAAYC,QAAuB;IAI1C,IAAIC,yBAAyB;IAC7B,IAAIC,uBAAuB;IAE3B,IAAK,IAAIC,IAAI,GAAGA,IAAIH,SAASI,MAAM,EAAED,IAAK;QACxC,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGN,QAAQ,CAACG,EAAE;QAExC,IAAIG,0BAAAA,OAAQC,QAAQ,CAAC,eAAe;YAClC,IAAIF,gBAAsC;gBACxCJ,0BAA0B;YAC5B,OAAO;gBACLC,wBAAwB;YAC1B;QACF;IACF;IAEA,OAAO;QACLA;QACAD;IACF;AACF;AAEA,SAASO,cACPC,GAAW,EACXT,QAAuB,EACvBU,QAAgB;IAEhB,IAAIC,WAAWd,KAAKe,KAAK,CAACC,SAAS,CACjChB,KAAKiB,QAAQ,CAACL,KAAKC,UAAUK,OAAO,CAAC,OAAO;IAG9C,IAAI,CAACJ,SAASK,UAAU,CAAC,MAAM;QAC7BL,WAAW,OAAOA;IACpB;IAEA,IAAIM,SAAS,OAAOxB,KAAKkB;IAEzB,IAAK,IAAIR,IAAI,GAAGA,IAAIH,SAASI,MAAM,EAAED,IAAK;QACxC,MAAM,EAAEe,OAAO,EAAEb,QAAQ,EAAEc,IAAI,EAAEC,MAAM,EAAEd,MAAM,EAAE,GAAGN,QAAQ,CAACG,EAAE;QAE/Dc,SAASA,SAAS;QAElB,IAAIE,QAAQC,QAAQ;YAClBH,SACEA,SACArB,OAAOuB,KAAKE,QAAQ,MACpB,MACAzB,OAAOwB,OAAOC,QAAQ,MACtB;QACJ;QAEA,IAAIhB,gBAAsC;YACxCY,UAAUrB,OAAOJ,KAAK,cAAc;QACtC,OAAO;YACLyB,UAAUtB,IAAIH,KAAK,YAAY;QACjC;QAEAyB,UAAUC;QAEV,IAAIZ,QAAQ;YACVW,UAAU,OAAOvB,KAAKF,KAAKc;QAC7B;IACF;IAEA,OAAOW;AACT;AAEA,OAAO,eAAeK,cACpBC,OAAe,EACfC,OAAqB,EACrBC,MAAqD;IAOrD,IAAIC,4BAA4B;IAChC,IAAIC,8BAA8B;IAClC,IAAIC,sBAAsBJ,QAAQK,MAAM,CAAC,CAAC,EAAE7B,QAAQ,EAAE,GAAKA,4BAAAA,SAAUI,MAAM;IAE3E,qDAAqD;IACrDwB,oBAAoBE,OAAO,CAAC,CAAC,EAAE9B,QAAQ,EAAE;QACvC,MAAM+B,MAAMhC,YAAYC;QACxB0B,6BAA6BK,IAAI7B,oBAAoB;QACrDyB,+BAA+BI,IAAI9B,sBAAsB;IAC3D;IAEA,oEAAoE;IACpE,MAAMgB,SAASQ,SACX,MAAMA,OAAOG,uBACbA,oBACGI,GAAG,CAAC,CAAC,EAAEhC,QAAQ,EAAEU,QAAQ,EAAE,GAC1BF,cAAce,SAASvB,UAAUU,WAElCuB,IAAI,CAAC;IAEZ,OAAO;QACLhB,QAAQA;QACRiB,oBACEN,oBAAoBxB,MAAM,GAAG,IACzBa,SACA,CAAC,IAAI,EAAExB,KACL,QACA,+HAA+H,CAAC,GAClI;QACNiC;QACAC;IACF;AACF"}