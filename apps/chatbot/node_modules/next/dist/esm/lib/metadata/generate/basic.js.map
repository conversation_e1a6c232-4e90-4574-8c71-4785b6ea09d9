{"version": 3, "sources": ["../../../../src/lib/metadata/generate/basic.tsx"], "sourcesContent": ["import type {\n  ResolvedMetadata,\n  ResolvedViewport,\n  Viewport,\n} from '../types/metadata-interface'\nimport type { ViewportLayout } from '../types/extra-types'\n\nimport { Meta, MetaFilter, MultiMeta } from './meta'\nimport { ViewportMetaKeys } from '../constants'\nimport { getOrigin } from './utils'\n\n// convert viewport object to string for viewport meta tag\nfunction resolveViewportLayout(viewport: Viewport) {\n  let resolved: string | null = null\n\n  if (viewport && typeof viewport === 'object') {\n    resolved = ''\n    for (const viewportKey_ in ViewportMetaKeys) {\n      const viewportKey = viewportKey_ as keyof ViewportLayout\n      if (viewportKey in viewport) {\n        let value = viewport[viewportKey]\n        if (typeof value === 'boolean') {\n          value = value ? 'yes' : 'no'\n        } else if (!value && viewportKey === 'initialScale') {\n          value = undefined\n        }\n        if (value) {\n          if (resolved) resolved += ', '\n          resolved += `${ViewportMetaKeys[viewportKey]}=${value}`\n        }\n      }\n    }\n  }\n  return resolved\n}\n\nexport function ViewportMeta({ viewport }: { viewport: ResolvedViewport }) {\n  return MetaFilter([\n    <meta charSet=\"utf-8\" />,\n    Meta({ name: 'viewport', content: resolveViewportLayout(viewport) }),\n    ...(viewport.themeColor\n      ? viewport.themeColor.map((themeColor) =>\n          Meta({\n            name: 'theme-color',\n            content: themeColor.color,\n            media: themeColor.media,\n          })\n        )\n      : []),\n    Meta({ name: 'color-scheme', content: viewport.colorScheme }),\n  ])\n}\n\nexport function BasicMeta({ metadata }: { metadata: ResolvedMetadata }) {\n  const manifestOrigin = metadata.manifest\n    ? getOrigin(metadata.manifest)\n    : undefined\n\n  return MetaFilter([\n    metadata.title !== null && metadata.title.absolute ? (\n      <title>{metadata.title.absolute}</title>\n    ) : null,\n    Meta({ name: 'description', content: metadata.description }),\n    Meta({ name: 'application-name', content: metadata.applicationName }),\n    ...(metadata.authors\n      ? metadata.authors.map((author) => [\n          author.url ? (\n            <link rel=\"author\" href={author.url.toString()} />\n          ) : null,\n          Meta({ name: 'author', content: author.name }),\n        ])\n      : []),\n    metadata.manifest ? (\n      <link\n        rel=\"manifest\"\n        href={metadata.manifest.toString()}\n        // If it's same origin, and it's a preview deployment,\n        // including credentials for manifest request.\n        crossOrigin={\n          !manifestOrigin && process.env.VERCEL_ENV === 'preview'\n            ? 'use-credentials'\n            : undefined\n        }\n      />\n    ) : null,\n    Meta({ name: 'generator', content: metadata.generator }),\n    Meta({ name: 'keywords', content: metadata.keywords?.join(',') }),\n    Meta({ name: 'referrer', content: metadata.referrer }),\n    Meta({ name: 'creator', content: metadata.creator }),\n    Meta({ name: 'publisher', content: metadata.publisher }),\n    Meta({ name: 'robots', content: metadata.robots?.basic }),\n    Meta({ name: 'googlebot', content: metadata.robots?.googleBot }),\n    Meta({ name: 'abstract', content: metadata.abstract }),\n    ...(metadata.archives\n      ? metadata.archives.map((archive) => (\n          <link rel=\"archives\" href={archive} />\n        ))\n      : []),\n    ...(metadata.assets\n      ? metadata.assets.map((asset) => <link rel=\"assets\" href={asset} />)\n      : []),\n    ...(metadata.bookmarks\n      ? metadata.bookmarks.map((bookmark) => (\n          <link rel=\"bookmarks\" href={bookmark} />\n        ))\n      : []),\n    ...(metadata.pagination\n      ? [\n          metadata.pagination.previous ? (\n            <link rel=\"prev\" href={metadata.pagination.previous} />\n          ) : null,\n          metadata.pagination.next ? (\n            <link rel=\"next\" href={metadata.pagination.next} />\n          ) : null,\n        ]\n      : []),\n    Meta({ name: 'category', content: metadata.category }),\n    Meta({ name: 'classification', content: metadata.classification }),\n    ...(metadata.other\n      ? Object.entries(metadata.other).map(([name, content]) => {\n          if (Array.isArray(content)) {\n            return content.map((contentItem) =>\n              Meta({ name, content: contentItem })\n            )\n          } else {\n            return Meta({ name, content })\n          }\n        })\n      : []),\n  ])\n}\n\nexport function ItunesMeta({ itunes }: { itunes: ResolvedMetadata['itunes'] }) {\n  if (!itunes) return null\n  const { appId, appArgument } = itunes\n  let content = `app-id=${appId}`\n  if (appArgument) {\n    content += `, app-argument=${appArgument}`\n  }\n  return <meta name=\"apple-itunes-app\" content={content} />\n}\n\nexport function FacebookMeta({\n  facebook,\n}: {\n  facebook: ResolvedMetadata['facebook']\n}) {\n  if (!facebook) return null\n\n  const { appId, admins } = facebook\n\n  return MetaFilter([\n    appId ? <meta property=\"fb:app_id\" content={appId} /> : null,\n    ...(admins\n      ? admins.map((admin) => <meta property=\"fb:admins\" content={admin} />)\n      : []),\n  ])\n}\n\nexport function PinterestMeta({\n  pinterest,\n}: {\n  pinterest: ResolvedMetadata['pinterest']\n}) {\n  if (!pinterest || !pinterest.richPin) return null\n\n  const { richPin } = pinterest\n\n  return <meta property=\"pinterest-rich-pin\" content={richPin.toString()} />\n}\n\nconst formatDetectionKeys = [\n  'telephone',\n  'date',\n  'address',\n  'email',\n  'url',\n] as const\nexport function FormatDetectionMeta({\n  formatDetection,\n}: {\n  formatDetection: ResolvedMetadata['formatDetection']\n}) {\n  if (!formatDetection) return null\n  let content = ''\n  for (const key of formatDetectionKeys) {\n    if (key in formatDetection) {\n      if (content) content += ', '\n      content += `${key}=no`\n    }\n  }\n  return <meta name=\"format-detection\" content={content} />\n}\n\nexport function AppleWebAppMeta({\n  appleWebApp,\n}: {\n  appleWebApp: ResolvedMetadata['appleWebApp']\n}) {\n  if (!appleWebApp) return null\n\n  const { capable, title, startupImage, statusBarStyle } = appleWebApp\n\n  return MetaFilter([\n    capable ? Meta({ name: 'mobile-web-app-capable', content: 'yes' }) : null,\n    Meta({ name: 'apple-mobile-web-app-title', content: title }),\n    startupImage\n      ? startupImage.map((image) => (\n          <link\n            href={image.url}\n            media={image.media}\n            rel=\"apple-touch-startup-image\"\n          />\n        ))\n      : null,\n    statusBarStyle\n      ? Meta({\n          name: 'apple-mobile-web-app-status-bar-style',\n          content: statusBarStyle,\n        })\n      : null,\n  ])\n}\n\nexport function VerificationMeta({\n  verification,\n}: {\n  verification: ResolvedMetadata['verification']\n}) {\n  if (!verification) return null\n\n  return MetaFilter([\n    MultiMeta({\n      namePrefix: 'google-site-verification',\n      contents: verification.google,\n    }),\n    MultiMeta({ namePrefix: 'y_key', contents: verification.yahoo }),\n    MultiMeta({\n      namePrefix: 'yandex-verification',\n      contents: verification.yandex,\n    }),\n    MultiMeta({ namePrefix: 'me', contents: verification.me }),\n    ...(verification.other\n      ? Object.entries(verification.other).map(([key, value]) =>\n          MultiMeta({ namePrefix: key, contents: value })\n        )\n      : []),\n  ])\n}\n"], "names": ["Meta", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MultiMeta", "ViewportMetaKeys", "<PERSON><PERSON><PERSON><PERSON>", "resolveViewportLayout", "viewport", "resolved", "viewportKey_", "viewportKey", "value", "undefined", "ViewportMeta", "meta", "charSet", "name", "content", "themeColor", "map", "color", "media", "colorScheme", "BasicMeta", "metadata", "<PERSON><PERSON><PERSON><PERSON>", "manifest", "title", "absolute", "description", "applicationName", "authors", "author", "url", "link", "rel", "href", "toString", "crossOrigin", "process", "env", "VERCEL_ENV", "generator", "keywords", "join", "referrer", "creator", "publisher", "robots", "basic", "googleBot", "abstract", "archives", "archive", "assets", "asset", "bookmarks", "bookmark", "pagination", "previous", "next", "category", "classification", "other", "Object", "entries", "Array", "isArray", "contentItem", "ItunesMeta", "itunes", "appId", "appArgument", "FacebookMeta", "facebook", "admins", "property", "admin", "PinterestMeta", "pinterest", "rich<PERSON>in", "formatDetectionKeys", "FormatDetectionMeta", "formatDetection", "key", "AppleWebAppMeta", "appleWebApp", "capable", "startupImage", "statusBarStyle", "image", "VerificationMeta", "verification", "namePrefix", "contents", "google", "yahoo", "yandex", "me"], "mappings": ";AAOA,SAASA,IAAI,EAAEC,UAAU,EAAEC,SAAS,QAAQ,SAAQ;AACpD,SAASC,gBAAgB,QAAQ,eAAc;AAC/C,SAASC,SAAS,QAAQ,UAAS;AAEnC,0DAA0D;AAC1D,SAASC,sBAAsBC,QAAkB;IAC/C,IAAIC,WAA0B;IAE9B,IAAID,YAAY,OAAOA,aAAa,UAAU;QAC5CC,WAAW;QACX,IAAK,MAAMC,gBAAgBL,iBAAkB;YAC3C,MAAMM,cAAcD;YACpB,IAAIC,eAAeH,UAAU;gBAC3B,IAAII,QAAQJ,QAAQ,CAACG,YAAY;gBACjC,IAAI,OAAOC,UAAU,WAAW;oBAC9BA,QAAQA,QAAQ,QAAQ;gBAC1B,OAAO,IAAI,CAACA,SAASD,gBAAgB,gBAAgB;oBACnDC,QAAQC;gBACV;gBACA,IAAID,OAAO;oBACT,IAAIH,UAAUA,YAAY;oBAC1BA,YAAY,GAAGJ,gBAAgB,CAACM,YAAY,CAAC,CAAC,EAAEC,OAAO;gBACzD;YACF;QACF;IACF;IACA,OAAOH;AACT;AAEA,OAAO,SAASK,aAAa,EAAEN,QAAQ,EAAkC;IACvE,OAAOL,WAAW;sBAChB,KAACY;YAAKC,SAAQ;;QACdd,KAAK;YAAEe,MAAM;YAAYC,SAASX,sBAAsBC;QAAU;WAC9DA,SAASW,UAAU,GACnBX,SAASW,UAAU,CAACC,GAAG,CAAC,CAACD,aACvBjB,KAAK;gBACHe,MAAM;gBACNC,SAASC,WAAWE,KAAK;gBACzBC,OAAOH,WAAWG,KAAK;YACzB,MAEF,EAAE;QACNpB,KAAK;YAAEe,MAAM;YAAgBC,SAASV,SAASe,WAAW;QAAC;KAC5D;AACH;AAEA,OAAO,SAASC,UAAU,EAAEC,QAAQ,EAAkC;QAiChCA,oBAIFA,kBACGA;IArCrC,MAAMC,iBAAiBD,SAASE,QAAQ,GACpCrB,UAAUmB,SAASE,QAAQ,IAC3Bd;IAEJ,OAAOV,WAAW;QAChBsB,SAASG,KAAK,KAAK,QAAQH,SAASG,KAAK,CAACC,QAAQ,iBAChD,KAACD;sBAAOH,SAASG,KAAK,CAACC,QAAQ;aAC7B;QACJ3B,KAAK;YAAEe,MAAM;YAAeC,SAASO,SAASK,WAAW;QAAC;QAC1D5B,KAAK;YAAEe,MAAM;YAAoBC,SAASO,SAASM,eAAe;QAAC;WAC/DN,SAASO,OAAO,GAChBP,SAASO,OAAO,CAACZ,GAAG,CAAC,CAACa,SAAW;gBAC/BA,OAAOC,GAAG,iBACR,KAACC;oBAAKC,KAAI;oBAASC,MAAMJ,OAAOC,GAAG,CAACI,QAAQ;qBAC1C;gBACJpC,KAAK;oBAAEe,MAAM;oBAAUC,SAASe,OAAOhB,IAAI;gBAAC;aAC7C,IACD,EAAE;QACNQ,SAASE,QAAQ,iBACf,KAACQ;YACCC,KAAI;YACJC,MAAMZ,SAASE,QAAQ,CAACW,QAAQ;YAChC,sDAAsD;YACtD,8CAA8C;YAC9CC,aACE,CAACb,kBAAkBc,QAAQC,GAAG,CAACC,UAAU,KAAK,YAC1C,oBACA7B;aAGN;QACJX,KAAK;YAAEe,MAAM;YAAaC,SAASO,SAASkB,SAAS;QAAC;QACtDzC,KAAK;YAAEe,MAAM;YAAYC,OAAO,GAAEO,qBAAAA,SAASmB,QAAQ,qBAAjBnB,mBAAmBoB,IAAI,CAAC;QAAK;QAC/D3C,KAAK;YAAEe,MAAM;YAAYC,SAASO,SAASqB,QAAQ;QAAC;QACpD5C,KAAK;YAAEe,MAAM;YAAWC,SAASO,SAASsB,OAAO;QAAC;QAClD7C,KAAK;YAAEe,MAAM;YAAaC,SAASO,SAASuB,SAAS;QAAC;QACtD9C,KAAK;YAAEe,MAAM;YAAUC,OAAO,GAAEO,mBAAAA,SAASwB,MAAM,qBAAfxB,iBAAiByB,KAAK;QAAC;QACvDhD,KAAK;YAAEe,MAAM;YAAaC,OAAO,GAAEO,oBAAAA,SAASwB,MAAM,qBAAfxB,kBAAiB0B,SAAS;QAAC;QAC9DjD,KAAK;YAAEe,MAAM;YAAYC,SAASO,SAAS2B,QAAQ;QAAC;WAChD3B,SAAS4B,QAAQ,GACjB5B,SAAS4B,QAAQ,CAACjC,GAAG,CAAC,CAACkC,wBACrB,KAACnB;gBAAKC,KAAI;gBAAWC,MAAMiB;kBAE7B,EAAE;WACF7B,SAAS8B,MAAM,GACf9B,SAAS8B,MAAM,CAACnC,GAAG,CAAC,CAACoC,sBAAU,KAACrB;gBAAKC,KAAI;gBAASC,MAAMmB;kBACxD,EAAE;WACF/B,SAASgC,SAAS,GAClBhC,SAASgC,SAAS,CAACrC,GAAG,CAAC,CAACsC,yBACtB,KAACvB;gBAAKC,KAAI;gBAAYC,MAAMqB;kBAE9B,EAAE;WACFjC,SAASkC,UAAU,GACnB;YACElC,SAASkC,UAAU,CAACC,QAAQ,iBAC1B,KAACzB;gBAAKC,KAAI;gBAAOC,MAAMZ,SAASkC,UAAU,CAACC,QAAQ;iBACjD;YACJnC,SAASkC,UAAU,CAACE,IAAI,iBACtB,KAAC1B;gBAAKC,KAAI;gBAAOC,MAAMZ,SAASkC,UAAU,CAACE,IAAI;iBAC7C;SACL,GACD,EAAE;QACN3D,KAAK;YAAEe,MAAM;YAAYC,SAASO,SAASqC,QAAQ;QAAC;QACpD5D,KAAK;YAAEe,MAAM;YAAkBC,SAASO,SAASsC,cAAc;QAAC;WAC5DtC,SAASuC,KAAK,GACdC,OAAOC,OAAO,CAACzC,SAASuC,KAAK,EAAE5C,GAAG,CAAC,CAAC,CAACH,MAAMC,QAAQ;YACjD,IAAIiD,MAAMC,OAAO,CAAClD,UAAU;gBAC1B,OAAOA,QAAQE,GAAG,CAAC,CAACiD,cAClBnE,KAAK;wBAAEe;wBAAMC,SAASmD;oBAAY;YAEtC,OAAO;gBACL,OAAOnE,KAAK;oBAAEe;oBAAMC;gBAAQ;YAC9B;QACF,KACA,EAAE;KACP;AACH;AAEA,OAAO,SAASoD,WAAW,EAAEC,MAAM,EAA0C;IAC3E,IAAI,CAACA,QAAQ,OAAO;IACpB,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE,GAAGF;IAC/B,IAAIrD,UAAU,CAAC,OAAO,EAAEsD,OAAO;IAC/B,IAAIC,aAAa;QACfvD,WAAW,CAAC,eAAe,EAAEuD,aAAa;IAC5C;IACA,qBAAO,KAAC1D;QAAKE,MAAK;QAAmBC,SAASA;;AAChD;AAEA,OAAO,SAASwD,aAAa,EAC3BC,QAAQ,EAGT;IACC,IAAI,CAACA,UAAU,OAAO;IAEtB,MAAM,EAAEH,KAAK,EAAEI,MAAM,EAAE,GAAGD;IAE1B,OAAOxE,WAAW;QAChBqE,sBAAQ,KAACzD;YAAK8D,UAAS;YAAY3D,SAASsD;aAAY;WACpDI,SACAA,OAAOxD,GAAG,CAAC,CAAC0D,sBAAU,KAAC/D;gBAAK8D,UAAS;gBAAY3D,SAAS4D;kBAC1D,EAAE;KACP;AACH;AAEA,OAAO,SAASC,cAAc,EAC5BC,SAAS,EAGV;IACC,IAAI,CAACA,aAAa,CAACA,UAAUC,OAAO,EAAE,OAAO;IAE7C,MAAM,EAAEA,OAAO,EAAE,GAAGD;IAEpB,qBAAO,KAACjE;QAAK8D,UAAS;QAAqB3D,SAAS+D,QAAQ3C,QAAQ;;AACtE;AAEA,MAAM4C,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AACD,OAAO,SAASC,oBAAoB,EAClCC,eAAe,EAGhB;IACC,IAAI,CAACA,iBAAiB,OAAO;IAC7B,IAAIlE,UAAU;IACd,KAAK,MAAMmE,OAAOH,oBAAqB;QACrC,IAAIG,OAAOD,iBAAiB;YAC1B,IAAIlE,SAASA,WAAW;YACxBA,WAAW,GAAGmE,IAAI,GAAG,CAAC;QACxB;IACF;IACA,qBAAO,KAACtE;QAAKE,MAAK;QAAmBC,SAASA;;AAChD;AAEA,OAAO,SAASoE,gBAAgB,EAC9BC,WAAW,EAGZ;IACC,IAAI,CAACA,aAAa,OAAO;IAEzB,MAAM,EAAEC,OAAO,EAAE5D,KAAK,EAAE6D,YAAY,EAAEC,cAAc,EAAE,GAAGH;IAEzD,OAAOpF,WAAW;QAChBqF,UAAUtF,KAAK;YAAEe,MAAM;YAA0BC,SAAS;QAAM,KAAK;QACrEhB,KAAK;YAAEe,MAAM;YAA8BC,SAASU;QAAM;QAC1D6D,eACIA,aAAarE,GAAG,CAAC,CAACuE,sBAChB,KAACxD;gBACCE,MAAMsD,MAAMzD,GAAG;gBACfZ,OAAOqE,MAAMrE,KAAK;gBAClBc,KAAI;kBAGR;QACJsD,iBACIxF,KAAK;YACHe,MAAM;YACNC,SAASwE;QACX,KACA;KACL;AACH;AAEA,OAAO,SAASE,iBAAiB,EAC/BC,YAAY,EAGb;IACC,IAAI,CAACA,cAAc,OAAO;IAE1B,OAAO1F,WAAW;QAChBC,UAAU;YACR0F,YAAY;YACZC,UAAUF,aAAaG,MAAM;QAC/B;QACA5F,UAAU;YAAE0F,YAAY;YAASC,UAAUF,aAAaI,KAAK;QAAC;QAC9D7F,UAAU;YACR0F,YAAY;YACZC,UAAUF,aAAaK,MAAM;QAC/B;QACA9F,UAAU;YAAE0F,YAAY;YAAMC,UAAUF,aAAaM,EAAE;QAAC;WACpDN,aAAa7B,KAAK,GAClBC,OAAOC,OAAO,CAAC2B,aAAa7B,KAAK,EAAE5C,GAAG,CAAC,CAAC,CAACiE,KAAKzE,MAAM,GAClDR,UAAU;gBAAE0F,YAAYT;gBAAKU,UAAUnF;YAAM,MAE/C,EAAE;KACP;AACH"}