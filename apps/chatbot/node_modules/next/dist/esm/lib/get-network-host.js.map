{"version": 3, "sources": ["../../src/lib/get-network-host.ts"], "sourcesContent": ["import os from 'os'\n\nfunction getNetworkHosts(family: 'IPv4' | 'IPv6'): string[] {\n  const interfaces = os.networkInterfaces()\n  const hosts: string[] = []\n\n  Object.keys(interfaces).forEach((key) => {\n    interfaces[key]\n      ?.filter((networkInterface) => {\n        switch (networkInterface.family) {\n          case 'IPv6':\n            return (\n              family === 'IPv6' &&\n              networkInterface.scopeid === 0 &&\n              networkInterface.address !== '::1'\n            )\n          case 'IPv4':\n            return family === 'IPv4' && networkInterface.address !== '127.0.0.1'\n          default:\n            return false\n        }\n      })\n      .forEach((networkInterface) => {\n        if (networkInterface.address) {\n          hosts.push(networkInterface.address)\n        }\n      })\n  })\n\n  return hosts\n}\n\nexport function getNetworkHost(family: 'IPv4' | 'IPv6'): string | null {\n  const hosts = getNetworkHosts(family)\n  return hosts[0] ?? null\n}\n"], "names": ["os", "getNetworkHosts", "family", "interfaces", "networkInterfaces", "hosts", "Object", "keys", "for<PERSON>ach", "key", "filter", "networkInterface", "scopeid", "address", "push", "getNetworkHost"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AAEnB,SAASC,gBAAgBC,MAAuB;IAC9C,MAAMC,aAAaH,GAAGI,iBAAiB;IACvC,MAAMC,QAAkB,EAAE;IAE1BC,OAAOC,IAAI,CAACJ,YAAYK,OAAO,CAAC,CAACC;YAC/BN;SAAAA,kBAAAA,UAAU,CAACM,IAAI,qBAAfN,gBACIO,MAAM,CAAC,CAACC;YACR,OAAQA,iBAAiBT,MAAM;gBAC7B,KAAK;oBACH,OACEA,WAAW,UACXS,iBAAiBC,OAAO,KAAK,KAC7BD,iBAAiBE,OAAO,KAAK;gBAEjC,KAAK;oBACH,OAAOX,WAAW,UAAUS,iBAAiBE,OAAO,KAAK;gBAC3D;oBACE,OAAO;YACX;QACF,GACCL,OAAO,CAAC,CAACG;YACR,IAAIA,iBAAiBE,OAAO,EAAE;gBAC5BR,MAAMS,IAAI,CAACH,iBAAiBE,OAAO;YACrC;QACF;IACJ;IAEA,OAAOR;AACT;AAEA,OAAO,SAASU,eAAeb,MAAuB;IACpD,MAAMG,QAAQJ,gBAAgBC;IAC9B,OAAOG,KAAK,CAAC,EAAE,IAAI;AACrB"}