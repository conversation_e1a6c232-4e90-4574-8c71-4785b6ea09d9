{"version": 3, "sources": ["../../src/lib/find-config.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\nimport { readFile } from 'fs/promises'\nimport JSON5 from 'next/dist/compiled/json5'\nimport { pathToFileURL } from 'url'\n\ntype RecursivePartial<T> = {\n  [P in keyof T]?: RecursivePartial<T[P]>\n}\n\nexport function findConfigPath(\n  dir: string,\n  key: string\n): Promise<string | undefined> {\n  // If we didn't find the configuration in `package.json`, we should look for\n  // known filenames.\n  return findUp(\n    [\n      `.${key}rc.json`,\n      `${key}.config.json`,\n      `.${key}rc.js`,\n      `${key}.config.js`,\n      `${key}.config.mjs`,\n      `${key}.config.cjs`,\n    ],\n    {\n      cwd: dir,\n    }\n  )\n}\n\n// We'll allow configuration to be typed, but we force everything provided to\n// become optional. We do not perform any schema validation. We should maybe\n// force all the types to be `unknown` as well.\nexport async function findConfig<T>(\n  directory: string,\n  key: string,\n  _returnFile?: boolean\n): Promise<RecursivePartial<T> | null> {\n  // `package.json` configuration always wins. Let's check that first.\n  const packageJsonPath = await findUp('package.json', { cwd: directory })\n  let isESM = false\n\n  if (packageJsonPath) {\n    try {\n      const packageJsonStr = await readFile(packageJsonPath, 'utf8')\n      const packageJson = JSON.parse(packageJsonStr) as {\n        [key: string]: string\n      }\n\n      if (typeof packageJson !== 'object') {\n        throw new Error() // Stop processing and continue\n      }\n\n      if (packageJson.type === 'module') {\n        isESM = true\n      }\n\n      if (packageJson[key] != null && typeof packageJson[key] === 'object') {\n        return packageJson[key]\n      }\n    } catch {\n      // Ignore error and continue\n    }\n  }\n\n  const filePath = await findConfigPath(directory, key)\n\n  const esmImport = (path: string) => {\n    // Skip mapping to absolute url with pathToFileURL on windows if it's jest\n    // https://github.com/nodejs/node/issues/31710#issuecomment-587345749\n    if (process.platform === 'win32' && !process.env.JEST_WORKER_ID) {\n      // on windows import(\"C:\\\\path\\\\to\\\\file\") is not valid, so we need to\n      // use file:// URLs\n      return import(pathToFileURL(path).toString())\n    } else {\n      return import(path)\n    }\n  }\n\n  if (filePath) {\n    if (filePath.endsWith('.js')) {\n      if (isESM) {\n        return (await esmImport(filePath)).default\n      } else {\n        return require(filePath)\n      }\n    } else if (filePath.endsWith('.mjs')) {\n      return (await esmImport(filePath)).default\n    } else if (filePath.endsWith('.cjs')) {\n      return require(filePath)\n    }\n\n    // We load JSON contents with JSON5 to allow users to comment in their\n    // configuration file. This pattern was popularized by TypeScript.\n    const fileContents = await readFile(filePath, 'utf8')\n    return JSON5.parse(fileContents)\n  }\n\n  return null\n}\n"], "names": ["findUp", "readFile", "JSON5", "pathToFileURL", "findConfigPath", "dir", "key", "cwd", "findConfig", "directory", "_returnFile", "packageJsonPath", "isESM", "packageJsonStr", "packageJson", "JSON", "parse", "Error", "type", "filePath", "esmImport", "path", "process", "platform", "env", "JEST_WORKER_ID", "toString", "endsWith", "default", "require", "fileContents"], "mappings": "AAAA,OAAOA,YAAY,6BAA4B;AAC/C,SAASC,QAAQ,QAAQ,cAAa;AACtC,OAAOC,WAAW,2BAA0B;AAC5C,SAASC,aAAa,QAAQ,MAAK;AAMnC,OAAO,SAASC,eACdC,GAAW,EACXC,GAAW;IAEX,4EAA4E;IAC5E,mBAAmB;IACnB,OAAON,OACL;QACE,CAAC,CAAC,EAAEM,IAAI,OAAO,CAAC;QAChB,GAAGA,IAAI,YAAY,CAAC;QACpB,CAAC,CAAC,EAAEA,IAAI,KAAK,CAAC;QACd,GAAGA,IAAI,UAAU,CAAC;QAClB,GAAGA,IAAI,WAAW,CAAC;QACnB,GAAGA,IAAI,WAAW,CAAC;KACpB,EACD;QACEC,KAAKF;IACP;AAEJ;AAEA,6EAA6E;AAC7E,4EAA4E;AAC5E,+CAA+C;AAC/C,OAAO,eAAeG,WACpBC,SAAiB,EACjBH,GAAW,EACXI,WAAqB;IAErB,oEAAoE;IACpE,MAAMC,kBAAkB,MAAMX,OAAO,gBAAgB;QAAEO,KAAKE;IAAU;IACtE,IAAIG,QAAQ;IAEZ,IAAID,iBAAiB;QACnB,IAAI;YACF,MAAME,iBAAiB,MAAMZ,SAASU,iBAAiB;YACvD,MAAMG,cAAcC,KAAKC,KAAK,CAACH;YAI/B,IAAI,OAAOC,gBAAgB,UAAU;gBACnC,MAAM,IAAIG,QAAQ,+BAA+B;;YACnD;YAEA,IAAIH,YAAYI,IAAI,KAAK,UAAU;gBACjCN,QAAQ;YACV;YAEA,IAAIE,WAAW,CAACR,IAAI,IAAI,QAAQ,OAAOQ,WAAW,CAACR,IAAI,KAAK,UAAU;gBACpE,OAAOQ,WAAW,CAACR,IAAI;YACzB;QACF,EAAE,OAAM;QACN,4BAA4B;QAC9B;IACF;IAEA,MAAMa,WAAW,MAAMf,eAAeK,WAAWH;IAEjD,MAAMc,YAAY,CAACC;QACjB,0EAA0E;QAC1E,qEAAqE;QACrE,IAAIC,QAAQC,QAAQ,KAAK,WAAW,CAACD,QAAQE,GAAG,CAACC,cAAc,EAAE;YAC/D,sEAAsE;YACtE,mBAAmB;YACnB,OAAO,MAAM,CAACtB,cAAckB,MAAMK,QAAQ;QAC5C,OAAO;YACL,OAAO,MAAM,CAACL;QAChB;IACF;IAEA,IAAIF,UAAU;QACZ,IAAIA,SAASQ,QAAQ,CAAC,QAAQ;YAC5B,IAAIf,OAAO;gBACT,OAAO,AAAC,CAAA,MAAMQ,UAAUD,SAAQ,EAAGS,OAAO;YAC5C,OAAO;gBACL,OAAOC,QAAQV;YACjB;QACF,OAAO,IAAIA,SAASQ,QAAQ,CAAC,SAAS;YACpC,OAAO,AAAC,CAAA,MAAMP,UAAUD,SAAQ,EAAGS,OAAO;QAC5C,OAAO,IAAIT,SAASQ,QAAQ,CAAC,SAAS;YACpC,OAAOE,QAAQV;QACjB;QAEA,sEAAsE;QACtE,kEAAkE;QAClE,MAAMW,eAAe,MAAM7B,SAASkB,UAAU;QAC9C,OAAOjB,MAAMc,KAAK,CAACc;IACrB;IAEA,OAAO;AACT"}