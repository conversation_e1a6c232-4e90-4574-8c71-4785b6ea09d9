{"version": 3, "sources": ["../../src/lib/require-instrumentation-client.ts"], "sourcesContent": ["/**\n * This module imports the client instrumentation hook from the project root.\n *\n * The `private-next-instrumentation-client` module is automatically aliased to\n * the `instrumentation-client.ts` file in the project root by webpack or turbopack.\n */\nif (process.env.NODE_ENV === 'development') {\n  const measureName = 'Client Instrumentation Hook'\n  const startTime = performance.now()\n  require('private-next-instrumentation-client')\n  const endTime = performance.now()\n\n  const duration = endTime - startTime\n  performance.measure(measureName, {\n    start: startTime,\n    end: endTime,\n    detail: 'Client instrumentation initialization',\n  })\n\n  // Using 16ms threshold as it represents one frame (1000ms/60fps)\n  // This helps identify if the instrumentation hook initialization\n  // could potentially cause frame drops during development.\n  const THRESHOLD = 16\n  if (duration > THRESHOLD) {\n    console.log(\n      `[${measureName}] Slow execution detected: ${duration.toFixed(0)}ms (Note: Code download overhead is not included in this measurement)`\n    )\n  }\n} else {\n  require('private-next-instrumentation-client')\n}\n"], "names": ["process", "env", "NODE_ENV", "measureName", "startTime", "performance", "now", "require", "endTime", "duration", "measure", "start", "end", "detail", "THRESHOLD", "console", "log", "toFixed"], "mappings": "AAAA;;;;;CAKC,GACD,IAAIA,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,cAAc;IACpB,MAAMC,YAAYC,YAAYC,GAAG;IACjCC,QAAQ;IACR,MAAMC,UAAUH,YAAYC,GAAG;IAE/B,MAAMG,WAAWD,UAAUJ;IAC3BC,YAAYK,OAAO,CAACP,aAAa;QAC/BQ,OAAOP;QACPQ,KAAKJ;QACLK,QAAQ;IACV;IAEA,iEAAiE;IACjE,iEAAiE;IACjE,0DAA0D;IAC1D,MAAMC,YAAY;IAClB,IAAIL,WAAWK,WAAW;QACxBC,QAAQC,GAAG,CACT,CAAC,CAAC,EAAEb,YAAY,2BAA2B,EAAEM,SAASQ,OAAO,CAAC,GAAG,qEAAqE,CAAC;IAE3I;AACF,OAAO;IACLV,QAAQ;AACV"}