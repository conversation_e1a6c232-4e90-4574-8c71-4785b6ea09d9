{"version": 3, "sources": ["../../../../../src/client/dev/dev-build-indicator/internal/initialize.ts"], "sourcesContent": ["/*\n * Singleton store to track whether the app is currently being built\n * Used by the dev tools indicator of the new overlay to show build status\n */\n\nimport { devBuildIndicator } from './dev-build-indicator'\nimport { useSyncExternalStore } from 'react'\n\nlet isVisible = false\nlet listeners: Array<() => void> = []\n\nconst subscribe = (listener: () => void) => {\n  listeners.push(listener)\n  return () => {\n    listeners = listeners.filter((l) => l !== listener)\n  }\n}\n\nconst getSnapshot = () => isVisible\n\nexport function useIsDevBuilding() {\n  return useSyncExternalStore(subscribe, getSnapshot)\n}\n\nexport function initialize() {\n  devBuildIndicator.show = () => {\n    isVisible = true\n    listeners.forEach((listener) => listener())\n  }\n\n  devBuildIndicator.hide = () => {\n    isVisible = false\n    listeners.forEach((listener) => listener())\n  }\n}\n"], "names": ["devBuildIndicator", "useSyncExternalStore", "isVisible", "listeners", "subscribe", "listener", "push", "filter", "l", "getSnapshot", "useIsDevBuilding", "initialize", "show", "for<PERSON>ach", "hide"], "mappings": "AAAA;;;CAGC,GAED,SAASA,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,oBAAoB,QAAQ,QAAO;AAE5C,IAAIC,YAAY;AAChB,IAAIC,YAA+B,EAAE;AAErC,MAAMC,YAAY,CAACC;IACjBF,UAAUG,IAAI,CAACD;IACf,OAAO;QACLF,YAAYA,UAAUI,MAAM,CAAC,CAACC,IAAMA,MAAMH;IAC5C;AACF;AAEA,MAAMI,cAAc,IAAMP;AAE1B,OAAO,SAASQ;IACd,OAAOT,qBAAqBG,WAAWK;AACzC;AAEA,OAAO,SAASE;IACdX,kBAAkBY,IAAI,GAAG;QACvBV,YAAY;QACZC,UAAUU,OAAO,CAAC,CAACR,WAAaA;IAClC;IAEAL,kBAAkBc,IAAI,GAAG;QACvBZ,YAAY;QACZC,UAAUU,OAAO,CAAC,CAACR,WAAaA;IAClC;AACF"}