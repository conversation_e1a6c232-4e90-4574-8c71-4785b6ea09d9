{"version": 3, "sources": ["../../../src/client/lib/console.ts"], "sourcesContent": ["import isError from '../../lib/is-error'\n\nfunction formatObject(arg: unknown, depth: number) {\n  switch (typeof arg) {\n    case 'object':\n      if (arg === null) {\n        return 'null'\n      } else if (Array.isArray(arg)) {\n        let result = '['\n        if (depth < 1) {\n          for (let i = 0; i < arg.length; i++) {\n            if (result !== '[') {\n              result += ','\n            }\n            if (Object.prototype.hasOwnProperty.call(arg, i)) {\n              result += formatObject(arg[i], depth + 1)\n            }\n          }\n        } else {\n          result += arg.length > 0 ? '...' : ''\n        }\n        result += ']'\n        return result\n      } else if (arg instanceof Error) {\n        return arg + ''\n      } else {\n        const keys = Object.keys(arg)\n        let result = '{'\n        if (depth < 1) {\n          for (let i = 0; i < keys.length; i++) {\n            const key = keys[i]\n            const desc = Object.getOwnPropertyDescriptor(arg, 'key')\n            if (desc && !desc.get && !desc.set) {\n              const jsonKey = JSON.stringify(key)\n              if (jsonKey !== '\"' + key + '\"') {\n                result += jsonKey + ': '\n              } else {\n                result += key + ': '\n              }\n              result += formatObject(desc.value, depth + 1)\n            }\n          }\n        } else {\n          result += keys.length > 0 ? '...' : ''\n        }\n        result += '}'\n        return result\n      }\n    case 'string':\n      return JSON.stringify(arg)\n    default:\n      return String(arg)\n  }\n}\n\nexport function formatConsoleArgs(args: unknown[]): string {\n  let message: string\n  let idx: number\n  if (typeof args[0] === 'string') {\n    message = args[0]\n    idx = 1\n  } else {\n    message = ''\n    idx = 0\n  }\n  let result = ''\n  let startQuote = false\n  for (let i = 0; i < message.length; ++i) {\n    const char = message[i]\n    if (char !== '%' || i === message.length - 1 || idx >= args.length) {\n      result += char\n      continue\n    }\n\n    const code = message[++i]\n    switch (code) {\n      case 'c': {\n        // TODO: We should colorize with HTML instead of turning into a string.\n        // Ignore for now.\n        result = startQuote ? `${result}]` : `[${result}`\n        startQuote = !startQuote\n        idx++\n        break\n      }\n      case 'O':\n      case 'o': {\n        result += formatObject(args[idx++], 0)\n        break\n      }\n      case 'd':\n      case 'i': {\n        result += parseInt(args[idx++] as any, 10)\n        break\n      }\n      case 'f': {\n        result += parseFloat(args[idx++] as any)\n        break\n      }\n      case 's': {\n        result += String(args[idx++])\n        break\n      }\n      default:\n        result += '%' + code\n    }\n  }\n\n  for (; idx < args.length; idx++) {\n    result += (idx > 0 ? ' ' : '') + formatObject(args[idx], 0)\n  }\n\n  return result\n}\n\nexport function parseConsoleArgs(args: unknown[]): {\n  environmentName: string | null\n  error: Error | null\n} {\n  // See\n  // https://github.com/facebook/react/blob/65a56d0e99261481c721334a3ec4561d173594cd/packages/react-devtools-shared/src/backend/flight/renderer.js#L88-L93\n  //\n  // Logs replayed from the server look like this:\n  // [\n  //   \"%c%s%c %o\\n\\n%s\\n\\n%s\\n\",\n  //   \"background: #e6e6e6; ...\",\n  //   \" Server \", // can also be e.g. \" Prerender \"\n  //   \"\",\n  //   Error,\n  //   \"The above error occurred in the <Page> component.\",\n  //   ...\n  // ]\n  if (\n    args.length > 3 &&\n    typeof args[0] === 'string' &&\n    args[0].startsWith('%c%s%c ') &&\n    typeof args[1] === 'string' &&\n    typeof args[2] === 'string' &&\n    typeof args[3] === 'string'\n  ) {\n    const environmentName = args[2]\n    const maybeError = args[4]\n\n    return {\n      environmentName: environmentName.trim(),\n      error: isError(maybeError) ? maybeError : null,\n    }\n  }\n\n  return {\n    environmentName: null,\n    error: null,\n  }\n}\n"], "names": ["isError", "formatObject", "arg", "depth", "Array", "isArray", "result", "i", "length", "Object", "prototype", "hasOwnProperty", "call", "Error", "keys", "key", "desc", "getOwnPropertyDescriptor", "get", "set", "jsonKey", "JSON", "stringify", "value", "String", "formatConsoleArgs", "args", "message", "idx", "startQuote", "char", "code", "parseInt", "parseFloat", "parseConsoleArgs", "startsWith", "environmentName", "maybeError", "trim", "error"], "mappings": "AAAA,OAAOA,aAAa,qBAAoB;AAExC,SAASC,aAAaC,GAAY,EAAEC,KAAa;IAC/C,OAAQ,OAAOD;QACb,KAAK;YACH,IAAIA,QAAQ,MAAM;gBAChB,OAAO;YACT,OAAO,IAAIE,MAAMC,OAAO,CAACH,MAAM;gBAC7B,IAAII,SAAS;gBACb,IAAIH,QAAQ,GAAG;oBACb,IAAK,IAAII,IAAI,GAAGA,IAAIL,IAAIM,MAAM,EAAED,IAAK;wBACnC,IAAID,WAAW,KAAK;4BAClBA,UAAU;wBACZ;wBACA,IAAIG,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACV,KAAKK,IAAI;4BAChDD,UAAUL,aAAaC,GAAG,CAACK,EAAE,EAAEJ,QAAQ;wBACzC;oBACF;gBACF,OAAO;oBACLG,UAAUJ,IAAIM,MAAM,GAAG,IAAI,QAAQ;gBACrC;gBACAF,UAAU;gBACV,OAAOA;YACT,OAAO,IAAIJ,eAAeW,OAAO;gBAC/B,OAAOX,MAAM;YACf,OAAO;gBACL,MAAMY,OAAOL,OAAOK,IAAI,CAACZ;gBACzB,IAAII,SAAS;gBACb,IAAIH,QAAQ,GAAG;oBACb,IAAK,IAAII,IAAI,GAAGA,IAAIO,KAAKN,MAAM,EAAED,IAAK;wBACpC,MAAMQ,MAAMD,IAAI,CAACP,EAAE;wBACnB,MAAMS,OAAOP,OAAOQ,wBAAwB,CAACf,KAAK;wBAClD,IAAIc,QAAQ,CAACA,KAAKE,GAAG,IAAI,CAACF,KAAKG,GAAG,EAAE;4BAClC,MAAMC,UAAUC,KAAKC,SAAS,CAACP;4BAC/B,IAAIK,YAAY,MAAML,MAAM,KAAK;gCAC/BT,UAAUc,UAAU;4BACtB,OAAO;gCACLd,UAAUS,MAAM;4BAClB;4BACAT,UAAUL,aAAae,KAAKO,KAAK,EAAEpB,QAAQ;wBAC7C;oBACF;gBACF,OAAO;oBACLG,UAAUQ,KAAKN,MAAM,GAAG,IAAI,QAAQ;gBACtC;gBACAF,UAAU;gBACV,OAAOA;YACT;QACF,KAAK;YACH,OAAOe,KAAKC,SAAS,CAACpB;QACxB;YACE,OAAOsB,OAAOtB;IAClB;AACF;AAEA,OAAO,SAASuB,kBAAkBC,IAAe;IAC/C,IAAIC;IACJ,IAAIC;IACJ,IAAI,OAAOF,IAAI,CAAC,EAAE,KAAK,UAAU;QAC/BC,UAAUD,IAAI,CAAC,EAAE;QACjBE,MAAM;IACR,OAAO;QACLD,UAAU;QACVC,MAAM;IACR;IACA,IAAItB,SAAS;IACb,IAAIuB,aAAa;IACjB,IAAK,IAAItB,IAAI,GAAGA,IAAIoB,QAAQnB,MAAM,EAAE,EAAED,EAAG;QACvC,MAAMuB,OAAOH,OAAO,CAACpB,EAAE;QACvB,IAAIuB,SAAS,OAAOvB,MAAMoB,QAAQnB,MAAM,GAAG,KAAKoB,OAAOF,KAAKlB,MAAM,EAAE;YAClEF,UAAUwB;YACV;QACF;QAEA,MAAMC,OAAOJ,OAAO,CAAC,EAAEpB,EAAE;QACzB,OAAQwB;YACN,KAAK;gBAAK;oBACR,uEAAuE;oBACvE,kBAAkB;oBAClBzB,SAASuB,aAAa,AAAC,KAAEvB,SAAO,MAAK,AAAC,MAAGA;oBACzCuB,aAAa,CAACA;oBACdD;oBACA;gBACF;YACA,KAAK;YACL,KAAK;gBAAK;oBACRtB,UAAUL,aAAayB,IAAI,CAACE,MAAM,EAAE;oBACpC;gBACF;YACA,KAAK;YACL,KAAK;gBAAK;oBACRtB,UAAU0B,SAASN,IAAI,CAACE,MAAM,EAAS;oBACvC;gBACF;YACA,KAAK;gBAAK;oBACRtB,UAAU2B,WAAWP,IAAI,CAACE,MAAM;oBAChC;gBACF;YACA,KAAK;gBAAK;oBACRtB,UAAUkB,OAAOE,IAAI,CAACE,MAAM;oBAC5B;gBACF;YACA;gBACEtB,UAAU,MAAMyB;QACpB;IACF;IAEA,MAAOH,MAAMF,KAAKlB,MAAM,EAAEoB,MAAO;QAC/BtB,UAAU,AAACsB,CAAAA,MAAM,IAAI,MAAM,EAAC,IAAK3B,aAAayB,IAAI,CAACE,IAAI,EAAE;IAC3D;IAEA,OAAOtB;AACT;AAEA,OAAO,SAAS4B,iBAAiBR,IAAe;IAI9C,MAAM;IACN,wJAAwJ;IACxJ,EAAE;IACF,gDAAgD;IAChD,IAAI;IACJ,+BAA+B;IAC/B,gCAAgC;IAChC,kDAAkD;IAClD,QAAQ;IACR,WAAW;IACX,yDAAyD;IACzD,QAAQ;IACR,IAAI;IACJ,IACEA,KAAKlB,MAAM,GAAG,KACd,OAAOkB,IAAI,CAAC,EAAE,KAAK,YACnBA,IAAI,CAAC,EAAE,CAACS,UAAU,CAAC,cACnB,OAAOT,IAAI,CAAC,EAAE,KAAK,YACnB,OAAOA,IAAI,CAAC,EAAE,KAAK,YACnB,OAAOA,IAAI,CAAC,EAAE,KAAK,UACnB;QACA,MAAMU,kBAAkBV,IAAI,CAAC,EAAE;QAC/B,MAAMW,aAAaX,IAAI,CAAC,EAAE;QAE1B,OAAO;YACLU,iBAAiBA,gBAAgBE,IAAI;YACrCC,OAAOvC,QAAQqC,cAAcA,aAAa;QAC5C;IACF;IAEA,OAAO;QACLD,iBAAiB;QACjBG,OAAO;IACT;AACF"}