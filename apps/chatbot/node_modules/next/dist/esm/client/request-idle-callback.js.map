{"version": 3, "sources": ["../../src/client/request-idle-callback.ts"], "sourcesContent": ["export const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (cb: IdleRequestCallback): number {\n    let start = Date.now()\n    return self.setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: number) {\n    return clearTimeout(id)\n  }\n"], "names": ["requestIdleCallback", "self", "bind", "window", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "cancelIdleCallback", "id", "clearTimeout"], "mappings": "AAAA,OAAO,MAAMA,sBACX,AAAC,OAAOC,SAAS,eACfA,KAAKD,mBAAmB,IACxBC,KAAKD,mBAAmB,CAACE,IAAI,CAACC,WAChC,SAAUC,EAAuB;IAC/B,IAAIC,QAAQC,KAAKC,GAAG;IACpB,OAAON,KAAKO,UAAU,CAAC;QACrBJ,GAAG;YACDK,YAAY;YACZC,eAAe;gBACb,OAAOC,KAAKC,GAAG,CAAC,GAAG,KAAMN,CAAAA,KAAKC,GAAG,KAAKF,KAAI;YAC5C;QACF;IACF,GAAG;AACL,EAAC;AAEH,OAAO,MAAMQ,qBACX,AAAC,OAAOZ,SAAS,eACfA,KAAKY,kBAAkB,IACvBZ,KAAKY,kBAAkB,CAACX,IAAI,CAACC,WAC/B,SAAUW,EAAU;IAClB,OAAOC,aAAaD;AACtB,EAAC"}