{"version": 3, "sources": ["../../../src/client/react-client-callbacks/on-recoverable-error.ts"], "sourcesContent": ["// This module can be shared between both pages router and app router\n\nimport type { HydrationOptions } from 'react-dom/client'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { reportGlobalError } from './report-global-error'\nimport { getReactStitchedError } from '../components/errors/stitched-error'\nimport isError from '../../lib/is-error'\n\nexport const onRecoverableError: HydrationOptions['onRecoverableError'] = (\n  error,\n  errorInfo\n) => {\n  // x-ref: https://github.com/facebook/react/pull/28736\n  const cause = isError(error) && 'cause' in error ? error.cause : error\n  const stitchedError = getReactStitchedError(cause)\n  // In development mode, pass along the component stack to the error\n  if (process.env.NODE_ENV === 'development' && errorInfo.componentStack) {\n    ;(stitchedError as any)._componentStack = errorInfo.componentStack\n  }\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(cause)) return\n\n  reportGlobalError(stitchedError)\n}\n"], "names": ["isBailoutToCSRError", "reportGlobalError", "getReactStitchedError", "isError", "onRecoverableError", "error", "errorInfo", "cause", "stitchedError", "process", "env", "NODE_ENV", "componentStack", "_componentStack"], "mappings": "AAAA,qEAAqE;AAGrE,SAASA,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,qBAAqB,QAAQ,sCAAqC;AAC3E,OAAOC,aAAa,qBAAoB;AAExC,OAAO,MAAMC,qBAA6D,CACxEC,OACAC;IAEA,sDAAsD;IACtD,MAAMC,QAAQJ,QAAQE,UAAU,WAAWA,QAAQA,MAAME,KAAK,GAAGF;IACjE,MAAMG,gBAAgBN,sBAAsBK;IAC5C,mEAAmE;IACnE,IAAIE,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBL,UAAUM,cAAc,EAAE;;QACpEJ,cAAsBK,eAAe,GAAGP,UAAUM,cAAc;IACpE;IACA,6EAA6E;IAC7E,IAAIZ,oBAAoBO,QAAQ;IAEhCN,kBAAkBO;AACpB,EAAC"}