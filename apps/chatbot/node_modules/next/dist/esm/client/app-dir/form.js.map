{"version": 3, "sources": ["../../../src/client/app-dir/form.tsx"], "sourcesContent": ["'use client'\n\nimport { useCallback, type FormEvent, useContext } from 'react'\nimport { addBasePath } from '../add-base-path'\nimport { useMergedRef } from '../use-merged-ref'\nimport {\n  AppRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport {\n  checkFormActionUrl,\n  createFormSubmitDestinationUrl,\n  DISALLOWED_FORM_PROPS,\n  hasReactClientActionAttributes,\n  hasUnsupportedSubmitterAttributes,\n  type FormProps,\n} from '../form-shared'\nimport {\n  mountFormInstance,\n  unmountPrefetchableInstance,\n} from '../components/links'\n\nexport type { FormProps }\n\nexport default function Form({\n  replace,\n  scroll,\n  prefetch: prefetchProp,\n  ref: externalRef,\n  ...props\n}: FormProps) {\n  const router = useContext(AppRouterContext)\n\n  const actionProp = props.action\n  const isNavigatingForm = typeof actionProp === 'string'\n\n  // Validate `action`\n  if (process.env.NODE_ENV === 'development') {\n    if (isNavigatingForm) {\n      checkFormActionUrl(actionProp, 'action')\n    }\n  }\n\n  // Validate `prefetch`\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      !(\n        prefetchProp === undefined ||\n        prefetchProp === false ||\n        prefetchProp === null\n      )\n    ) {\n      console.error('The `prefetch` prop of <Form> must be `false` or `null`')\n    }\n\n    if (prefetchProp !== undefined && !isNavigatingForm) {\n      console.error(\n        'Passing `prefetch` to a <Form> whose `action` is a function has no effect.'\n      )\n    }\n  }\n\n  const prefetch =\n    prefetchProp === false || prefetchProp === null ? prefetchProp : null\n\n  // Validate `scroll` and `replace`\n  if (process.env.NODE_ENV === 'development') {\n    if (!isNavigatingForm && (replace !== undefined || scroll !== undefined)) {\n      console.error(\n        'Passing `replace` or `scroll` to a <Form> whose `action` is a function has no effect.\\n' +\n          'See the relevant docs to learn how to control this behavior for navigations triggered from actions:\\n' +\n          '  `redirect()`       - https://nextjs.org/docs/app/api-reference/functions/redirect#parameters\\n' +\n          '  `router.replace()` - https://nextjs.org/docs/app/api-reference/functions/use-router#userouter\\n'\n      )\n    }\n  }\n\n  // Clean up any unsupported form props (and warn if present)\n  for (const key of DISALLOWED_FORM_PROPS) {\n    if (key in props) {\n      if (process.env.NODE_ENV === 'development') {\n        console.error(\n          `<Form> does not support changing \\`${key}\\`. ` +\n            (isNavigatingForm\n              ? `If you'd like to use it to perform a mutation, consider making \\`action\\` a function instead.\\n` +\n                `Learn more: https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations`\n              : '')\n        )\n      }\n      delete (props as Record<string, unknown>)[key]\n    }\n  }\n\n  const isPrefetchEnabled =\n    // if we don't have an action path, we can't prefetch anything.\n    !!router && isNavigatingForm && prefetch === null\n\n  const observeFormVisibilityOnMount = useCallback(\n    (element: HTMLFormElement) => {\n      if (isPrefetchEnabled && router !== null) {\n        mountFormInstance(element, actionProp, router, PrefetchKind.AUTO)\n      }\n      return () => {\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [isPrefetchEnabled, actionProp, router]\n  )\n\n  const mergedRef = useMergedRef(\n    observeFormVisibilityOnMount,\n    externalRef ?? null\n  )\n\n  if (!isNavigatingForm) {\n    return <form {...props} ref={mergedRef} />\n  }\n\n  const actionHref = addBasePath(actionProp)\n\n  return (\n    <form\n      {...props}\n      ref={mergedRef}\n      action={actionHref}\n      onSubmit={(event) =>\n        onFormSubmit(event, {\n          router,\n          actionHref,\n          replace,\n          scroll,\n          onSubmit: props.onSubmit,\n        })\n      }\n    />\n  )\n}\n\nfunction onFormSubmit(\n  event: FormEvent<HTMLFormElement>,\n  {\n    actionHref,\n    onSubmit,\n    replace,\n    scroll,\n    router,\n  }: {\n    actionHref: string\n    onSubmit: FormProps['onSubmit']\n    replace: FormProps['replace']\n    scroll: FormProps['scroll']\n    router: AppRouterInstance | null\n  }\n) {\n  if (typeof onSubmit === 'function') {\n    onSubmit(event)\n\n    // if the user called event.preventDefault(), do nothing.\n    // (this matches what Link does for `onClick`)\n    if (event.defaultPrevented) {\n      return\n    }\n  }\n\n  if (!router) {\n    // Form was somehow used outside of the router (but not in pages, the implementation is forked!).\n    // We can't perform a soft navigation, so let the native submit handling do its thing.\n    return\n  }\n\n  const formElement = event.currentTarget\n  const submitter = (event.nativeEvent as SubmitEvent).submitter\n\n  let action = actionHref\n\n  if (submitter) {\n    if (process.env.NODE_ENV === 'development') {\n      // the way server actions are encoded (e.g. `formMethod=\"post\")\n      // causes some unnecessary dev-mode warnings from `hasUnsupportedSubmitterAttributes`.\n      // we'd bail out anyway, but we just do it silently.\n      if (hasReactServerActionAttributes(submitter)) {\n        return\n      }\n    }\n\n    if (hasUnsupportedSubmitterAttributes(submitter)) {\n      return\n    }\n\n    // client actions have `formAction=\"javascript:...\"`. We obviously can't prefetch/navigate to that.\n    if (hasReactClientActionAttributes(submitter)) {\n      return\n    }\n\n    // If the submitter specified an alternate formAction,\n    // use that URL instead -- this is what a native form would do.\n    // NOTE: `submitter.formAction` is unreliable, because it will give us `location.href` if it *wasn't* set\n    // NOTE: this should not have `basePath` added, because we can't add it before hydration\n    const submitterFormAction = submitter.getAttribute('formAction')\n    if (submitterFormAction !== null) {\n      if (process.env.NODE_ENV === 'development') {\n        checkFormActionUrl(submitterFormAction, 'formAction')\n      }\n      action = submitterFormAction\n    }\n  }\n\n  const targetUrl = createFormSubmitDestinationUrl(action, formElement)\n\n  // Finally, no more reasons for bailing out.\n  event.preventDefault()\n\n  const method = replace ? 'replace' : 'push'\n  const targetHref = targetUrl.href\n  router[method](targetHref, { scroll })\n}\n\nfunction hasReactServerActionAttributes(submitter: HTMLElement) {\n  // https://github.com/facebook/react/blob/942eb80381b96f8410eab1bef1c539bed1ab0eb1/packages/react-client/src/ReactFlightReplyClient.js#L931-L934\n  const name = submitter.getAttribute('name')\n  return (\n    name && (name.startsWith('$ACTION_ID_') || name.startsWith('$ACTION_REF_'))\n  )\n}\n"], "names": ["useCallback", "useContext", "addBasePath", "useMergedRef", "AppRouterContext", "PrefetchKind", "checkFormActionUrl", "createFormSubmitDestinationUrl", "DISALLOWED_FORM_PROPS", "hasReactClientActionAttributes", "hasUnsupportedSubmitterAttributes", "mountFormInstance", "unmountPrefetchableInstance", "Form", "replace", "scroll", "prefetch", "prefetchProp", "ref", "externalRef", "props", "router", "actionProp", "action", "isNavigatingForm", "process", "env", "NODE_ENV", "undefined", "console", "error", "key", "isPrefetchEnabled", "observeFormVisibilityOnMount", "element", "AUTO", "mergedRef", "form", "actionHref", "onSubmit", "event", "onFormSubmit", "defaultPrevented", "formElement", "currentTarget", "submitter", "nativeEvent", "hasReactServerActionAttributes", "submitterFormAction", "getAttribute", "targetUrl", "preventDefault", "method", "targetHref", "href", "name", "startsWith"], "mappings": "AAAA;;AAEA,SAASA,WAAW,EAAkBC,UAAU,QAAQ,QAAO;AAC/D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,YAAY,QAAQ,oBAAmB;AAChD,SACEC,gBAAgB,QAEX,qDAAoD;AAC3D,SAASC,YAAY,QAAQ,oDAAmD;AAChF,SACEC,kBAAkB,EAClBC,8BAA8B,EAC9BC,qBAAqB,EACrBC,8BAA8B,EAC9BC,iCAAiC,QAE5B,iBAAgB;AACvB,SACEC,iBAAiB,EACjBC,2BAA2B,QACtB,sBAAqB;AAI5B,eAAe,SAASC,KAAK,KAMjB;IANiB,IAAA,EAC3BC,OAAO,EACPC,MAAM,EACNC,UAAUC,YAAY,EACtBC,KAAKC,WAAW,EAChB,GAAGC,OACO,GANiB;IAO3B,MAAMC,SAASpB,WAAWG;IAE1B,MAAMkB,aAAaF,MAAMG,MAAM;IAC/B,MAAMC,mBAAmB,OAAOF,eAAe;IAE/C,oBAAoB;IACpB,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IAAIH,kBAAkB;YACpBlB,mBAAmBgB,YAAY;QACjC;IACF;IAEA,sBAAsB;IACtB,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IACE,CACEV,CAAAA,iBAAiBW,aACjBX,iBAAiB,SACjBA,iBAAiB,IAAG,GAEtB;YACAY,QAAQC,KAAK,CAAC;QAChB;QAEA,IAAIb,iBAAiBW,aAAa,CAACJ,kBAAkB;YACnDK,QAAQC,KAAK,CACX;QAEJ;IACF;IAEA,MAAMd,WACJC,iBAAiB,SAASA,iBAAiB,OAAOA,eAAe;IAEnE,kCAAkC;IAClC,IAAIQ,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IAAI,CAACH,oBAAqBV,CAAAA,YAAYc,aAAab,WAAWa,SAAQ,GAAI;YACxEC,QAAQC,KAAK,CACX,4FACE,0GACA,qGACA;QAEN;IACF;IAEA,4DAA4D;IAC5D,KAAK,MAAMC,OAAOvB,sBAAuB;QACvC,IAAIuB,OAAOX,OAAO;YAChB,IAAIK,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;gBAC1CE,QAAQC,KAAK,CACX,AAAC,uCAAqCC,MAAI,QACvCP,CAAAA,mBACG,AAAC,kGACA,iHACD,EAAC;YAEX;YACA,OAAO,AAACJ,KAAiC,CAACW,IAAI;QAChD;IACF;IAEA,MAAMC,oBACJ,+DAA+D;IAC/D,CAAC,CAACX,UAAUG,oBAAoBR,aAAa;IAE/C,MAAMiB,+BAA+BjC,YACnC,CAACkC;QACC,IAAIF,qBAAqBX,WAAW,MAAM;YACxCV,kBAAkBuB,SAASZ,YAAYD,QAAQhB,aAAa8B,IAAI;QAClE;QACA,OAAO;YACLvB,4BAA4BsB;QAC9B;IACF,GACA;QAACF;QAAmBV;QAAYD;KAAO;IAGzC,MAAMe,YAAYjC,aAChB8B,8BACAd,sBAAAA,cAAe;IAGjB,IAAI,CAACK,kBAAkB;QACrB,qBAAO,KAACa;YAAM,GAAGjB,KAAK;YAAEF,KAAKkB;;IAC/B;IAEA,MAAME,aAAapC,YAAYoB;IAE/B,qBACE,KAACe;QACE,GAAGjB,KAAK;QACTF,KAAKkB;QACLb,QAAQe;QACRC,UAAU,CAACC,QACTC,aAAaD,OAAO;gBAClBnB;gBACAiB;gBACAxB;gBACAC;gBACAwB,UAAUnB,MAAMmB,QAAQ;YAC1B;;AAIR;AAEA,SAASE,aACPD,KAAiC,EACjC,KAYC;IAZD,IAAA,EACEF,UAAU,EACVC,QAAQ,EACRzB,OAAO,EACPC,MAAM,EACNM,MAAM,EAOP,GAZD;IAcA,IAAI,OAAOkB,aAAa,YAAY;QAClCA,SAASC;QAET,yDAAyD;QACzD,8CAA8C;QAC9C,IAAIA,MAAME,gBAAgB,EAAE;YAC1B;QACF;IACF;IAEA,IAAI,CAACrB,QAAQ;QACX,iGAAiG;QACjG,sFAAsF;QACtF;IACF;IAEA,MAAMsB,cAAcH,MAAMI,aAAa;IACvC,MAAMC,YAAY,AAACL,MAAMM,WAAW,CAAiBD,SAAS;IAE9D,IAAItB,SAASe;IAEb,IAAIO,WAAW;QACb,IAAIpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,+DAA+D;YAC/D,sFAAsF;YACtF,oDAAoD;YACpD,IAAIoB,+BAA+BF,YAAY;gBAC7C;YACF;QACF;QAEA,IAAInC,kCAAkCmC,YAAY;YAChD;QACF;QAEA,mGAAmG;QACnG,IAAIpC,+BAA+BoC,YAAY;YAC7C;QACF;QAEA,sDAAsD;QACtD,+DAA+D;QAC/D,yGAAyG;QACzG,wFAAwF;QACxF,MAAMG,sBAAsBH,UAAUI,YAAY,CAAC;QACnD,IAAID,wBAAwB,MAAM;YAChC,IAAIvB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;gBAC1CrB,mBAAmB0C,qBAAqB;YAC1C;YACAzB,SAASyB;QACX;IACF;IAEA,MAAME,YAAY3C,+BAA+BgB,QAAQoB;IAEzD,4CAA4C;IAC5CH,MAAMW,cAAc;IAEpB,MAAMC,SAAStC,UAAU,YAAY;IACrC,MAAMuC,aAAaH,UAAUI,IAAI;IACjCjC,MAAM,CAAC+B,OAAO,CAACC,YAAY;QAAEtC;IAAO;AACtC;AAEA,SAASgC,+BAA+BF,SAAsB;IAC5D,gJAAgJ;IAChJ,MAAMU,OAAOV,UAAUI,YAAY,CAAC;IACpC,OACEM,QAASA,CAAAA,KAAKC,UAAU,CAAC,kBAAkBD,KAAKC,UAAU,CAAC,eAAc;AAE7E"}