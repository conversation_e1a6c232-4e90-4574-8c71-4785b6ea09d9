import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export default function LightIcon() {
    return /*#__PURE__*/ _jsxs("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        children: [
            /*#__PURE__*/ _jsx("g", {
                clipPath: "url(#light_icon_clip_path)",
                children: /*#__PURE__*/ _jsx("path", {
                    fill: "currentColor",
                    fillRule: "evenodd",
                    d: "M8.75.75V0h-1.5v2h1.5V.75ZM3.26 4.32l-.53-.53-.354-.353-.53-.53 1.06-***********.354.354.53.53-1.06 1.06Zm8.42-1.06.53-.53.353-.354.53-.53 1.061 1.06-.53.53-.354.354-.53.53-1.06-1.06ZM8 11.25a3.25 3.25 0 1 0 0-6.5 3.25 3.25 0 0 0 0 6.5Zm0 1.5a4.75 4.75 0 1 0 0-9.5 4.75 4.75 0 0 0 0 9.5Zm6-5.5h2v1.5h-2v-1.5Zm-13.25 0H0v1.5h2v-1.5H.75Zm1.62 5.32-.53.53 1.06 1.06.53-.53.354-.353.53-.53-1.06-1.061-.53.53-.354.354Zm10.2 ********** 1.06-1.06-.53-.53-.354-.354-.53-.53-1.06 **********.353.354ZM8.75 14v2h-1.5v-2h1.5Z",
                    clipRule: "evenodd"
                })
            }),
            /*#__PURE__*/ _jsx("defs", {
                children: /*#__PURE__*/ _jsx("clipPath", {
                    id: "light_icon_clip_path",
                    children: /*#__PURE__*/ _jsx("path", {
                        fill: "currentColor",
                        d: "M0 0h16v16H0z"
                    })
                })
            })
        ]
    });
}

//# sourceMappingURL=light-icon.js.map