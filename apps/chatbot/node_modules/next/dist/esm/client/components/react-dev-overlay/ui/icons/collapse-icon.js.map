{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/icons/collapse-icon.tsx"], "sourcesContent": ["export function CollapseIcon({ collapsed }: { collapsed?: boolean } = {}) {\n  return (\n    <svg\n      data-nextjs-call-stack-chevron-icon\n      data-collapsed={collapsed}\n      width=\"16\"\n      height=\"16\"\n      fill=\"none\"\n      // rotate 90 degrees if not collapsed.\n      {...(typeof collapsed === 'boolean'\n        ? { style: { transform: collapsed ? undefined : 'rotate(90deg)' } }\n        : {})}\n    >\n      <path\n        style={{ fill: 'var(--color-font)' }}\n        fillRule=\"evenodd\"\n        d=\"m6.75 ********** 2.824 2.823a1 1 0 0 1 0 1.414L7.28 11.53l-.53.53L5.69 11l.53-.53L8.69 8 6.22 5.53 5.69 5l1.06-1.06Z\"\n        clipRule=\"evenodd\"\n      />\n    </svg>\n  )\n}\n"], "names": ["CollapseIcon", "collapsed", "svg", "data-nextjs-call-stack-chevron-icon", "data-collapsed", "width", "height", "fill", "style", "transform", "undefined", "path", "fillRule", "d", "clipRule"], "mappings": ";AAAA,OAAO,SAASA,aAAa;IAAA,IAAA,EAAEC,SAAS,EAA2B,GAAtC,mBAAyC,CAAC,IAA1C;IAC3B,qBACE,KAACC;QACCC,qCAAmC;QACnCC,kBAAgBH;QAChBI,OAAM;QACNC,QAAO;QACPC,MAAK;QAEJ,GAAI,OAAON,cAAc,YACtB;YAAEO,OAAO;gBAAEC,WAAWR,YAAYS,YAAY;YAAgB;QAAE,IAChE,CAAC,CAAC;kBAEN,cAAA,KAACC;YACCH,OAAO;gBAAED,MAAM;YAAoB;YACnCK,UAAS;YACTC,GAAE;YACFC,UAAS;;;AAIjB"}