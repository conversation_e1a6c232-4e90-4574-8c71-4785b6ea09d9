{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/terminal/editor-link.tsx"], "sourcesContent": ["import { useOpenInEditor } from '../../utils/use-open-in-editor'\n\ntype EditorLinkProps = {\n  file: string\n  isSourceFile: boolean\n  location?: {\n    line: number\n    column: number\n  }\n}\nexport function EditorLink({ file, location }: EditorLinkProps) {\n  const open = useOpenInEditor({\n    file,\n    lineNumber: location?.line ?? 1,\n    column: location?.column ?? 0,\n  })\n\n  return (\n    <div\n      data-with-open-in-editor-link\n      data-with-open-in-editor-link-import-trace\n      tabIndex={10}\n      role={'link'}\n      onClick={open}\n      title={'Click to open in your editor'}\n    >\n      {file}\n      {location ? `:${location.line}:${location.column}` : null}\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      >\n        <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n        <polyline points=\"15 3 21 3 21 9\"></polyline>\n        <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n      </svg>\n    </div>\n  )\n}\n\nexport const EDITOR_LINK_STYLES = `\n  [data-with-open-in-editor-link] svg {\n    width: auto;\n    height: var(--size-14);\n    margin-left: 8px;\n  }\n  [data-with-open-in-editor-link] {\n    cursor: pointer;\n  }\n  [data-with-open-in-editor-link]:hover {\n    text-decoration: underline dotted;\n  }\n  [data-with-open-in-editor-link-import-trace] {\n    margin-left: 16px;\n  }\n`\n"], "names": ["useOpenInEditor", "EditorLink", "file", "location", "open", "lineNumber", "line", "column", "div", "data-with-open-in-editor-link", "data-with-open-in-editor-link-import-trace", "tabIndex", "role", "onClick", "title", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2", "EDITOR_LINK_STYLES"], "mappings": ";AAAA,SAASA,eAAe,QAAQ,iCAAgC;AAUhE,OAAO,SAASC,WAAW,KAAmC;IAAnC,IAAA,EAAEC,IAAI,EAAEC,QAAQ,EAAmB,GAAnC;QAGXA,gBACJA;IAHV,MAAMC,OAAOJ,gBAAgB;QAC3BE;QACAG,YAAYF,CAAAA,iBAAAA,4BAAAA,SAAUG,IAAI,YAAdH,iBAAkB;QAC9BI,QAAQJ,CAAAA,mBAAAA,4BAAAA,SAAUI,MAAM,YAAhBJ,mBAAoB;IAC9B;IAEA,qBACE,MAACK;QACCC,+BAA6B;QAC7BC,4CAA0C;QAC1CC,UAAU;QACVC,MAAM;QACNC,SAAST;QACTU,OAAO;;YAENZ;YACAC,WAAW,AAAC,MAAGA,SAASG,IAAI,GAAC,MAAGH,SAASI,MAAM,GAAK;0BACrD,MAACQ;gBACCC,OAAM;gBACNC,SAAQ;gBACRC,MAAK;gBACLC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;kCAEf,KAACC;wBAAKC,GAAE;;kCACR,KAACC;wBAASC,QAAO;;kCACjB,KAACpB;wBAAKqB,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;;;;;;AAIzC;AAEA,OAAO,MAAMC,qBAAsB,gWAelC"}