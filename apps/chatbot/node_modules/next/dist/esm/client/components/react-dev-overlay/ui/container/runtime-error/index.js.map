{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/container/runtime-error/index.tsx"], "sourcesContent": ["import { useMemo } from 'react'\nimport { CodeFrame } from '../../components/code-frame/code-frame'\nimport { CallStack } from '../../components/errors/call-stack/call-stack'\nimport { PSEUDO_HTML_DIFF_STYLES } from './component-stack-pseudo-html'\nimport {\n  useFrames,\n  type ReadyRuntimeError,\n} from '../../../utils/get-error-by-type'\n\nexport type RuntimeErrorProps = {\n  error: ReadyRuntimeError\n  dialogResizerRef: React.RefObject<HTMLDivElement | null>\n}\n\nexport function RuntimeError({ error, dialogResizerRef }: RuntimeErrorProps) {\n  const frames = useFrames(error)\n\n  const firstFrame = useMemo(() => {\n    const firstFirstPartyFrameIndex = frames.findIndex(\n      (entry) =>\n        !entry.ignored &&\n        Boolean(entry.originalCodeFrame) &&\n        Boolean(entry.originalStackFrame)\n    )\n\n    return frames[firstFirstPartyFrameIndex] ?? null\n  }, [frames])\n\n  return (\n    <>\n      {firstFrame && (\n        <CodeFrame\n          stackFrame={firstFrame.originalStackFrame!}\n          codeFrame={firstFrame.originalCodeFrame!}\n        />\n      )}\n\n      {frames.length > 0 && (\n        <CallStack dialogResizerRef={dialogResizerRef} frames={frames} />\n      )}\n    </>\n  )\n}\n\nexport const styles = `\n  ${PSEUDO_HTML_DIFF_STYLES}\n`\n"], "names": ["useMemo", "CodeFrame", "CallStack", "PSEUDO_HTML_DIFF_STYLES", "useFrames", "RuntimeError", "error", "dialogResizerRef", "frames", "firstFrame", "firstFirstPartyFrameIndex", "findIndex", "entry", "ignored", "Boolean", "originalCodeFrame", "originalStackFrame", "stackFrame", "codeFrame", "length", "styles"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,QAAO;AAC/B,SAASC,SAAS,QAAQ,yCAAwC;AAClE,SAASC,SAAS,QAAQ,gDAA+C;AACzE,SAASC,uBAAuB,QAAQ,gCAA+B;AACvE,SACEC,SAAS,QAEJ,mCAAkC;AAOzC,OAAO,SAASC,aAAa,KAA8C;IAA9C,IAAA,EAAEC,KAAK,EAAEC,gBAAgB,EAAqB,GAA9C;IAC3B,MAAMC,SAASJ,UAAUE;IAEzB,MAAMG,aAAaT,QAAQ;QACzB,MAAMU,4BAA4BF,OAAOG,SAAS,CAChD,CAACC,QACC,CAACA,MAAMC,OAAO,IACdC,QAAQF,MAAMG,iBAAiB,KAC/BD,QAAQF,MAAMI,kBAAkB;YAG7BR;QAAP,OAAOA,CAAAA,oCAAAA,MAAM,CAACE,0BAA0B,YAAjCF,oCAAqC;IAC9C,GAAG;QAACA;KAAO;IAEX,qBACE;;YACGC,4BACC,KAACR;gBACCgB,YAAYR,WAAWO,kBAAkB;gBACzCE,WAAWT,WAAWM,iBAAiB;;YAI1CP,OAAOW,MAAM,GAAG,mBACf,KAACjB;gBAAUK,kBAAkBA;gBAAkBC,QAAQA;;;;AAI/D;AAEA,OAAO,MAAMY,SAAS,AAAC,SACnBjB,0BAAwB,KAC3B"}