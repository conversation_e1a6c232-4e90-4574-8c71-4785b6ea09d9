{"version": 3, "sources": ["../../../../src/client/components/http-access-fallback/error-fallback.tsx"], "sourcesContent": ["import React from 'react'\n\nconst styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n\n  desc: {\n    display: 'inline-block',\n  },\n\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    padding: '0 23px 0 0',\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n    lineHeight: '49px',\n  },\n\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '49px',\n    margin: 0,\n  },\n}\n\nexport function HTTPAccessErrorFallback({\n  status,\n  message,\n}: {\n  status: number\n  message: string\n}) {\n  return (\n    <>\n      {/* <head> */}\n      <title>{`${status}: ${message}`}</title>\n      {/* </head> */}\n      <div style={styles.error}>\n        <div>\n          <style\n            dangerouslySetInnerHTML={{\n              /* Minified CSS from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                @media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }\n              */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}`,\n            }}\n          />\n          <h1 className=\"next-error-h1\" style={styles.h1}>\n            {status}\n          </h1>\n          <div style={styles.desc}>\n            <h2 style={styles.h2}>{message}</h2>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": ["React", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "h1", "margin", "padding", "fontSize", "fontWeight", "verticalAlign", "lineHeight", "h2", "HTTPAccessErrorFallback", "status", "message", "title", "div", "style", "dangerouslySetInnerHTML", "__html", "className"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AAEzB,MAAMC,SAA8C;IAClDC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IAEAC,MAAM;QACJJ,SAAS;IACX;IAEAK,IAAI;QACFL,SAAS;QACTM,QAAQ;QACRC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,eAAe;QACfC,YAAY;IACd;IAEAC,IAAI;QACFJ,UAAU;QACVC,YAAY;QACZE,YAAY;QACZL,QAAQ;IACV;AACF;AAEA,OAAO,SAASO,wBAAwB,KAMvC;IANuC,IAAA,EACtCC,MAAM,EACNC,OAAO,EAIR,GANuC;IAOtC,qBACE;;0BAEE,KAACC;0BAAO,AAAGF,SAAO,OAAIC;;0BAEtB,KAACE;gBAAIC,OAAOvB,OAAOC,KAAK;0BACtB,cAAA,MAACqB;;sCACC,KAACC;4BACCC,yBAAyB;gCACvB;;;;;;;;;;;;cAYA,GACAC,QAAS;4BACX;;sCAEF,KAACf;4BAAGgB,WAAU;4BAAgBH,OAAOvB,OAAOU,EAAE;sCAC3CS;;sCAEH,KAACG;4BAAIC,OAAOvB,OAAOS,IAAI;sCACrB,cAAA,KAACQ;gCAAGM,OAAOvB,OAAOiB,EAAE;0CAAGG;;;;;;;;AAMnC"}