{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/container/errors.tsx"], "sourcesContent": ["import { useState, useMemo, useEffect, useRef, Suspense } from 'react'\nimport type { DebugInfo } from '../../types'\nimport { Overlay } from '../components/overlay'\nimport { RuntimeError } from './runtime-error'\nimport { getErrorSource } from '../../../../../shared/lib/error-source'\nimport { HotlinkedText } from '../components/hot-linked-text'\nimport { PseudoHtmlDiff } from './runtime-error/component-stack-pseudo-html'\nimport {\n  type HydrationErrorState,\n  getHydrationWarningType,\n} from '../../../errors/hydration-error-info'\nimport {\n  isConsoleError,\n  getConsoleErrorType,\n} from '../../../errors/console-error'\nimport { extractNextErrorCode } from '../../../../../lib/error-telemetry-utils'\nimport {\n  ErrorOverlayLayout,\n  type ErrorOverlayLayoutProps,\n} from '../components/errors/error-overlay-layout/error-overlay-layout'\nimport { NEXTJS_HYDRATION_ERROR_LINK } from '../../../is-hydration-error'\nimport type { ReadyRuntimeError } from '../../utils/get-error-by-type'\nimport type { ErrorBaseProps } from '../components/errors/error-overlay/error-overlay'\n\nexport interface ErrorsProps extends ErrorBaseProps {\n  runtimeErrors: ReadyRuntimeError[]\n  debugInfo: DebugInfo\n  onClose: () => void\n}\n\ntype ReadyErrorEvent = ReadyRuntimeError\n\nfunction isNextjsLink(text: string): boolean {\n  return text.startsWith('https://nextjs.org')\n}\n\nfunction ErrorDescription({\n  error,\n  hydrationWarning,\n}: {\n  error: Error\n  hydrationWarning: string | null\n}) {\n  const unhandledErrorType = isConsoleError(error)\n    ? getConsoleErrorType(error)\n    : null\n  const isConsoleErrorStringMessage = unhandledErrorType === 'string'\n  // If the error is:\n  // - hydration warning\n  // - captured console error or unhandled rejection\n  // skip displaying the error name\n  const title =\n    isConsoleErrorStringMessage || hydrationWarning ? '' : error.name + ': '\n\n  const environmentName =\n    'environmentName' in error ? error.environmentName : ''\n  const envPrefix = environmentName ? `[ ${environmentName} ] ` : ''\n\n  // The environment name will be displayed as a label, so remove it\n  // from the message (e.g. \"[ Server ] hello world\" -> \"hello world\").\n  let message = error.message\n  if (message.startsWith(envPrefix)) {\n    message = message.slice(envPrefix.length)\n  }\n\n  return (\n    <>\n      {title}\n      <HotlinkedText\n        text={hydrationWarning || message}\n        matcher={isNextjsLink}\n      />\n    </>\n  )\n}\n\nfunction getErrorType(error: Error): ErrorOverlayLayoutProps['errorType'] {\n  if (isConsoleError(error)) {\n    return 'Console Error'\n  }\n  return 'Runtime Error'\n}\n\nexport function Errors({\n  runtimeErrors,\n  debugInfo,\n  onClose,\n  ...props\n}: ErrorsProps) {\n  const dialogResizerRef = useRef<HTMLDivElement | null>(null)\n\n  useEffect(() => {\n    // Close the error overlay when pressing escape\n    function handleKeyDown(event: KeyboardEvent) {\n      if (event.key === 'Escape') {\n        onClose()\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [onClose])\n\n  const isLoading = useMemo<boolean>(() => {\n    return runtimeErrors.length < 1\n  }, [runtimeErrors.length])\n\n  const [activeIdx, setActiveIndex] = useState<number>(0)\n\n  const activeError = useMemo<ReadyErrorEvent | null>(\n    () => runtimeErrors[activeIdx] ?? null,\n    [activeIdx, runtimeErrors]\n  )\n\n  if (isLoading) {\n    // TODO: better loading state\n    return <Overlay />\n  }\n\n  if (!activeError) {\n    return null\n  }\n\n  const error = activeError.error\n  const isServerError = ['server', 'edge-server'].includes(\n    getErrorSource(error) || ''\n  )\n  const errorType = getErrorType(error)\n  const errorDetails: HydrationErrorState = (error as any).details || {}\n  const notes = errorDetails.notes || ''\n  const [warningTemplate, serverContent, clientContent] =\n    errorDetails.warning || [null, '', '']\n\n  const hydrationErrorType = getHydrationWarningType(warningTemplate)\n  const hydrationWarning = warningTemplate\n    ? warningTemplate\n        .replace('%s', serverContent)\n        .replace('%s', clientContent)\n        .replace('%s', '') // remove the %s for stack\n        .replace(/%s$/, '') // If there's still a %s at the end, remove it\n        .replace(/^Warning: /, '')\n        .replace(/^Error: /, '')\n    : null\n\n  const errorCode = extractNextErrorCode(error)\n\n  const footerMessage = isServerError\n    ? 'This error happened while generating the page. Any console logs will be displayed in the terminal window.'\n    : undefined\n\n  return (\n    <ErrorOverlayLayout\n      errorCode={errorCode}\n      errorType={errorType}\n      errorMessage={\n        <ErrorDescription error={error} hydrationWarning={hydrationWarning} />\n      }\n      onClose={isServerError ? undefined : onClose}\n      debugInfo={debugInfo}\n      error={error}\n      runtimeErrors={runtimeErrors}\n      activeIdx={activeIdx}\n      setActiveIndex={setActiveIndex}\n      footerMessage={footerMessage}\n      dialogResizerRef={dialogResizerRef}\n      {...props}\n    >\n      <div className=\"error-overlay-notes-container\">\n        {notes ? (\n          <>\n            <p\n              id=\"nextjs__container_errors__notes\"\n              className=\"nextjs__container_errors__notes\"\n            >\n              {notes}\n            </p>\n          </>\n        ) : null}\n        {hydrationWarning ? (\n          <p\n            id=\"nextjs__container_errors__link\"\n            className=\"nextjs__container_errors__link\"\n          >\n            <HotlinkedText\n              text={`See more info here: ${NEXTJS_HYDRATION_ERROR_LINK}`}\n            />\n          </p>\n        ) : null}\n      </div>\n\n      {hydrationWarning &&\n      (activeError.componentStackFrames?.length ||\n        !!errorDetails.reactOutputComponentDiff) ? (\n        <PseudoHtmlDiff\n          className=\"nextjs__container_errors__component-stack\"\n          hydrationMismatchType={hydrationErrorType}\n          firstContent={serverContent}\n          secondContent={clientContent}\n          reactOutputComponentDiff={errorDetails.reactOutputComponentDiff || ''}\n        />\n      ) : null}\n      <Suspense fallback={<div data-nextjs-error-suspended />}>\n        <RuntimeError\n          key={activeError.id.toString()}\n          error={activeError}\n          dialogResizerRef={dialogResizerRef}\n        />\n      </Suspense>\n    </ErrorOverlayLayout>\n  )\n}\n\nexport const styles = `\n  .nextjs-error-with-static {\n    bottom: calc(16px * 4.5);\n  }\n  p.nextjs__container_errors__link {\n    font-size: var(--size-14);\n  }\n  p.nextjs__container_errors__notes {\n    color: var(--color-stack-notes);\n    font-size: var(--size-14);\n    line-height: 1.5;\n  }\n  .nextjs-container-errors-body > h2:not(:first-child) {\n    margin-top: calc(16px + 8px);\n  }\n  .nextjs-container-errors-body > h2 {\n    color: var(--color-title-color);\n    margin-bottom: 8px;\n    font-size: var(--size-20);\n  }\n  .nextjs-toast-errors-parent {\n    cursor: pointer;\n    transition: transform 0.2s ease;\n  }\n  .nextjs-toast-errors-parent:hover {\n    transform: scale(1.1);\n  }\n  .nextjs-toast-errors {\n    display: flex;\n    align-items: center;\n    justify-content: flex-start;\n  }\n  .nextjs-toast-errors > svg {\n    margin-right: 8px;\n  }\n  .nextjs-toast-hide-button {\n    margin-left: 24px;\n    border: none;\n    background: none;\n    color: var(--color-ansi-bright-white);\n    padding: 0;\n    transition: opacity 0.25s ease;\n    opacity: 0.7;\n  }\n  .nextjs-toast-hide-button:hover {\n    opacity: 1;\n  }\n  .nextjs__container_errors_inspect_copy_button {\n    cursor: pointer;\n    background: none;\n    border: none;\n    color: var(--color-ansi-bright-white);\n    font-size: var(--size-24);\n    padding: 0;\n    margin: 0;\n    margin-left: 8px;\n    transition: opacity 0.25s ease;\n  }\n  .nextjs__container_errors__error_title {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 14px;\n  }\n  .error-overlay-notes-container {\n    margin: 8px 2px;\n  }\n  .error-overlay-notes-container p {\n    white-space: pre-wrap;\n  }\n`\n"], "names": ["useState", "useMemo", "useEffect", "useRef", "Suspense", "Overlay", "RuntimeError", "getErrorSource", "HotlinkedText", "PseudoHtmlDiff", "getHydrationWarningType", "isConsoleError", "getConsoleErrorType", "extractNextErrorCode", "ErrorOverlayLayout", "NEXTJS_HYDRATION_ERROR_LINK", "isNextjsLink", "text", "startsWith", "ErrorDescription", "error", "hydrationWarning", "unhandledErrorType", "isConsoleErrorStringMessage", "title", "name", "environmentName", "envPrefix", "message", "slice", "length", "matcher", "getErrorType", "Errors", "runtimeErrors", "debugInfo", "onClose", "props", "activeError", "dialogResizerRef", "handleKeyDown", "event", "key", "document", "addEventListener", "removeEventListener", "isLoading", "activeIdx", "setActiveIndex", "isServerError", "includes", "errorType", "errorDetails", "details", "notes", "warningTemplate", "serverContent", "clientContent", "warning", "hydrationErrorType", "replace", "errorCode", "footerMessage", "undefined", "errorMessage", "div", "className", "p", "id", "componentStackFrames", "reactOutputComponentDiff", "hydrationMismatchType", "firstContent", "second<PERSON><PERSON>nt", "fallback", "data-nextjs-error-suspended", "toString", "styles"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,QAAO;AAEtE,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,cAAc,QAAQ,yCAAwC;AACvE,SAASC,aAAa,QAAQ,gCAA+B;AAC7D,SAASC,cAAc,QAAQ,8CAA6C;AAC5E,SAEEC,uBAAuB,QAClB,uCAAsC;AAC7C,SACEC,cAAc,EACdC,mBAAmB,QACd,gCAA+B;AACtC,SAASC,oBAAoB,QAAQ,2CAA0C;AAC/E,SACEC,kBAAkB,QAEb,iEAAgE;AACvE,SAASC,2BAA2B,QAAQ,8BAA6B;AAYzE,SAASC,aAAaC,IAAY;IAChC,OAAOA,KAAKC,UAAU,CAAC;AACzB;AAEA,SAASC,iBAAiB,KAMzB;IANyB,IAAA,EACxBC,KAAK,EACLC,gBAAgB,EAIjB,GANyB;IAOxB,MAAMC,qBAAqBX,eAAeS,SACtCR,oBAAoBQ,SACpB;IACJ,MAAMG,8BAA8BD,uBAAuB;IAC3D,mBAAmB;IACnB,sBAAsB;IACtB,kDAAkD;IAClD,iCAAiC;IACjC,MAAME,QACJD,+BAA+BF,mBAAmB,KAAKD,MAAMK,IAAI,GAAG;IAEtE,MAAMC,kBACJ,qBAAqBN,QAAQA,MAAMM,eAAe,GAAG;IACvD,MAAMC,YAAYD,kBAAkB,AAAC,OAAIA,kBAAgB,QAAO;IAEhE,kEAAkE;IAClE,qEAAqE;IACrE,IAAIE,UAAUR,MAAMQ,OAAO;IAC3B,IAAIA,QAAQV,UAAU,CAACS,YAAY;QACjCC,UAAUA,QAAQC,KAAK,CAACF,UAAUG,MAAM;IAC1C;IAEA,qBACE;;YACGN;0BACD,KAAChB;gBACCS,MAAMI,oBAAoBO;gBAC1BG,SAASf;;;;AAIjB;AAEA,SAASgB,aAAaZ,KAAY;IAChC,IAAIT,eAAeS,QAAQ;QACzB,OAAO;IACT;IACA,OAAO;AACT;AAEA,OAAO,SAASa,OAAO,KAKT;IALS,IAAA,EACrBC,aAAa,EACbC,SAAS,EACTC,OAAO,EACP,GAAGC,OACS,GALS;QA4GhBC;IAtGL,MAAMC,mBAAmBpC,OAA8B;IAEvDD,UAAU;QACR,+CAA+C;QAC/C,SAASsC,cAAcC,KAAoB;YACzC,IAAIA,MAAMC,GAAG,KAAK,UAAU;gBAC1BN;YACF;QACF;QAEAO,SAASC,gBAAgB,CAAC,WAAWJ;QACrC,OAAO,IAAMG,SAASE,mBAAmB,CAAC,WAAWL;IACvD,GAAG;QAACJ;KAAQ;IAEZ,MAAMU,YAAY7C,QAAiB;QACjC,OAAOiC,cAAcJ,MAAM,GAAG;IAChC,GAAG;QAACI,cAAcJ,MAAM;KAAC;IAEzB,MAAM,CAACiB,WAAWC,eAAe,GAAGhD,SAAiB;IAErD,MAAMsC,cAAcrC,QAClB;YAAMiC;eAAAA,CAAAA,2BAAAA,aAAa,CAACa,UAAU,YAAxBb,2BAA4B;OAClC;QAACa;QAAWb;KAAc;IAG5B,IAAIY,WAAW;QACb,6BAA6B;QAC7B,qBAAO,KAACzC;IACV;IAEA,IAAI,CAACiC,aAAa;QAChB,OAAO;IACT;IAEA,MAAMlB,QAAQkB,YAAYlB,KAAK;IAC/B,MAAM6B,gBAAgB;QAAC;QAAU;KAAc,CAACC,QAAQ,CACtD3C,eAAea,UAAU;IAE3B,MAAM+B,YAAYnB,aAAaZ;IAC/B,MAAMgC,eAAoC,AAAChC,MAAciC,OAAO,IAAI,CAAC;IACrE,MAAMC,QAAQF,aAAaE,KAAK,IAAI;IACpC,MAAM,CAACC,iBAAiBC,eAAeC,cAAc,GACnDL,aAAaM,OAAO,IAAI;QAAC;QAAM;QAAI;KAAG;IAExC,MAAMC,qBAAqBjD,wBAAwB6C;IACnD,MAAMlC,mBAAmBkC,kBACrBA,gBACGK,OAAO,CAAC,MAAMJ,eACdI,OAAO,CAAC,MAAMH,eACdG,OAAO,CAAC,MAAM,IAAI,0BAA0B;KAC5CA,OAAO,CAAC,OAAO,IAAI,8CAA8C;KACjEA,OAAO,CAAC,cAAc,IACtBA,OAAO,CAAC,YAAY,MACvB;IAEJ,MAAMC,YAAYhD,qBAAqBO;IAEvC,MAAM0C,gBAAgBb,gBAClB,8GACAc;IAEJ,qBACE,MAACjD;QACC+C,WAAWA;QACXV,WAAWA;QACXa,4BACE,KAAC7C;YAAiBC,OAAOA;YAAOC,kBAAkBA;;QAEpDe,SAASa,gBAAgBc,YAAY3B;QACrCD,WAAWA;QACXf,OAAOA;QACPc,eAAeA;QACfa,WAAWA;QACXC,gBAAgBA;QAChBc,eAAeA;QACfvB,kBAAkBA;QACjB,GAAGF,KAAK;;0BAET,MAAC4B;gBAAIC,WAAU;;oBACZZ,sBACC;kCACE,cAAA,KAACa;4BACCC,IAAG;4BACHF,WAAU;sCAETZ;;yBAGH;oBACHjC,iCACC,KAAC8C;wBACCC,IAAG;wBACHF,WAAU;kCAEV,cAAA,KAAC1D;4BACCS,MAAM,AAAC,yBAAsBF;;yBAG/B;;;YAGLM,oBACAiB,CAAAA,EAAAA,oCAAAA,YAAY+B,oBAAoB,qBAAhC/B,kCAAkCR,MAAM,KACvC,CAAC,CAACsB,aAAakB,wBAAwB,AAAD,kBACtC,KAAC7D;gBACCyD,WAAU;gBACVK,uBAAuBZ;gBACvBa,cAAchB;gBACdiB,eAAehB;gBACfa,0BAA0BlB,aAAakB,wBAAwB,IAAI;iBAEnE;0BACJ,KAAClE;gBAASsE,wBAAU,KAACT;oBAAIU,6BAA2B;;0BAClD,cAAA,KAACrE;oBAECc,OAAOkB;oBACPC,kBAAkBA;mBAFbD,YAAY8B,EAAE,CAACQ,QAAQ;;;;AAOtC;AAEA,OAAO,MAAMC,SAAU,2rDAsEtB"}