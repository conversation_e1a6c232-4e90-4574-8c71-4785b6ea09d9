{"version": 3, "sources": ["../../../src/client/components/parallel-route-default.tsx"], "sourcesContent": ["import { notFound } from './not-found'\n\nexport const PARALLEL_ROUTE_DEFAULT_PATH =\n  'next/dist/client/components/parallel-route-default.js'\n\nexport default function ParallelRouteDefault() {\n  notFound()\n}\n"], "names": ["notFound", "PARALLEL_ROUTE_DEFAULT_PATH", "<PERSON>llel<PERSON><PERSON><PERSON>ault"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAa;AAEtC,OAAO,MAAMC,8BACX,wDAAuD;AAEzD,eAAe,SAASC;IACtBF;AACF"}