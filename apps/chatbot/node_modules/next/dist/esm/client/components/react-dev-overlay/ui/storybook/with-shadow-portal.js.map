{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/storybook/with-shadow-portal.tsx"], "sourcesContent": ["import { Base } from '../styles/base'\nimport { Colors } from '../styles/colors'\nimport { CssReset } from '../styles/css-reset'\nimport { ComponentStyles } from '../styles/component-styles'\nimport { ShadowPortal } from '../components/shadow-portal'\nimport { DarkTheme } from '../styles/dark-theme'\n\nexport const withShadowPortal = (Story: any) => (\n  <ShadowPortal>\n    <CssReset />\n    <Base />\n    <Colors />\n    <ComponentStyles />\n    <DarkTheme />\n    <Story />\n  </ShadowPortal>\n)\n"], "names": ["Base", "Colors", "CssReset", "ComponentStyles", "ShadowPort<PERSON>", "DarkTheme", "withShadowPortal", "Story"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,iBAAgB;AACrC,SAASC,MAAM,QAAQ,mBAAkB;AACzC,SAASC,QAAQ,QAAQ,sBAAqB;AAC9C,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,SAAS,QAAQ,uBAAsB;AAEhD,OAAO,MAAMC,mBAAmB,CAACC,sBAC/B,MAACH;;0BACC,KAACF;0BACD,KAACF;0BACD,KAACC;0BACD,KAACE;0BACD,KAACE;0BACD,KAACE;;OAEJ"}