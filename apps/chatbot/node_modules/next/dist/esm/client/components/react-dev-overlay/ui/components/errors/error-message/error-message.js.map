{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.tsx"], "sourcesContent": ["import { useState, useRef, useLayoutEffect } from 'react'\n\nexport type ErrorMessageType = React.ReactNode\n\ntype ErrorMessageProps = {\n  errorMessage: ErrorMessageType\n}\n\nexport function ErrorMessage({ errorMessage }: ErrorMessageProps) {\n  const [isExpanded, setIsExpanded] = useState(false)\n  const [shouldTruncate, setShouldTruncate] = useState(false)\n  const messageRef = useRef<HTMLParagraphElement>(null)\n\n  useLayoutEffect(() => {\n    if (messageRef.current) {\n      setShouldTruncate(messageRef.current.scrollHeight > 200)\n    }\n  }, [errorMessage])\n\n  return (\n    <div className=\"nextjs__container_errors_wrapper\">\n      <p\n        ref={messageRef}\n        id=\"nextjs__container_errors_desc\"\n        className={`nextjs__container_errors_desc ${shouldTruncate && !isExpanded ? 'truncated' : ''}`}\n      >\n        {errorMessage}\n      </p>\n      {shouldTruncate && !isExpanded && (\n        <>\n          <div className=\"nextjs__container_errors_gradient_overlay\" />\n          <button\n            onClick={() => setIsExpanded(true)}\n            className=\"nextjs__container_errors_expand_button\"\n            aria-expanded={isExpanded}\n            aria-controls=\"nextjs__container_errors_desc\"\n          >\n            Show More\n          </button>\n        </>\n      )}\n    </div>\n  )\n}\n\nexport const styles = `\n  .nextjs__container_errors_wrapper {\n    position: relative;\n  }\n\n  .nextjs__container_errors_desc {\n    margin: 0;\n    margin-left: 4px;\n    color: var(--color-red-900);\n    font-weight: 500;\n    font-size: var(--size-16);\n    letter-spacing: -0.32px;\n    line-height: var(--size-24);\n    overflow-wrap: break-word;\n    white-space: pre-wrap;\n  }\n\n  .nextjs__container_errors_desc.truncated {\n    max-height: 200px;\n    overflow: hidden;\n  }\n\n  .nextjs__container_errors_gradient_overlay {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 85px;\n    background: linear-gradient(\n      180deg,\n      rgba(250, 250, 250, 0) 0%,\n      var(--color-background-100) 100%\n    );\n  }\n\n  .nextjs__container_errors_expand_button {\n    position: absolute;\n    bottom: 10px;\n    left: 50%;\n    transform: translateX(-50%);\n    display: flex;\n    align-items: center;\n    padding: 6px 8px;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-alpha-400);\n    border-radius: 999px;\n    box-shadow:\n      0px 2px 2px var(--color-gray-alpha-100),\n      0px 8px 8px -8px var(--color-gray-alpha-100);\n    font-size: var(--size-13);\n    cursor: pointer;\n    color: var(--color-gray-900);\n    font-weight: 500;\n    transition: background-color 0.2s ease;\n  }\n\n  .nextjs__container_errors_expand_button:hover {\n    background: var(--color-gray-100);\n  }\n`\n"], "names": ["useState", "useRef", "useLayoutEffect", "ErrorMessage", "errorMessage", "isExpanded", "setIsExpanded", "shouldTruncate", "setShouldTruncate", "messageRef", "current", "scrollHeight", "div", "className", "p", "ref", "id", "button", "onClick", "aria-expanded", "aria-controls", "styles"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,eAAe,QAAQ,QAAO;AAQzD,OAAO,SAASC,aAAa,KAAmC;IAAnC,IAAA,EAAEC,YAAY,EAAqB,GAAnC;IAC3B,MAAM,CAACC,YAAYC,cAAc,GAAGN,SAAS;IAC7C,MAAM,CAACO,gBAAgBC,kBAAkB,GAAGR,SAAS;IACrD,MAAMS,aAAaR,OAA6B;IAEhDC,gBAAgB;QACd,IAAIO,WAAWC,OAAO,EAAE;YACtBF,kBAAkBC,WAAWC,OAAO,CAACC,YAAY,GAAG;QACtD;IACF,GAAG;QAACP;KAAa;IAEjB,qBACE,MAACQ;QAAIC,WAAU;;0BACb,KAACC;gBACCC,KAAKN;gBACLO,IAAG;gBACHH,WAAW,AAAC,mCAAgCN,CAAAA,kBAAkB,CAACF,aAAa,cAAc,EAAC;0BAE1FD;;YAEFG,kBAAkB,CAACF,4BAClB;;kCACE,KAACO;wBAAIC,WAAU;;kCACf,KAACI;wBACCC,SAAS,IAAMZ,cAAc;wBAC7BO,WAAU;wBACVM,iBAAed;wBACfe,iBAAc;kCACf;;;;;;AAOX;AAEA,OAAO,MAAMC,SAAU,i7CA2DtB"}