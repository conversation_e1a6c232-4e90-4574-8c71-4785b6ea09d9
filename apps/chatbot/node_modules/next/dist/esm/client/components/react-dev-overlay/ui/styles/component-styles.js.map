{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/styles/component-styles.tsx"], "sourcesContent": ["import { CODE_FRAME_STYLES } from '../components/code-frame/code-frame'\nimport { styles as dialog } from '../components/dialog'\nimport { styles as errorLayout } from '../components/errors/error-overlay-layout/error-overlay-layout'\nimport { styles as bottomStack } from '../components/errors/error-overlay-bottom-stack'\nimport { styles as pagination } from '../components/errors/error-overlay-pagination/error-overlay-pagination'\nimport { styles as overlay } from '../components/overlay/styles'\nimport { styles as footer } from '../components/errors/error-overlay-footer/error-overlay-footer'\nimport { TERMINAL_STYLES } from '../components/terminal/terminal'\nimport { styles as toast } from '../components/toast'\nimport { styles as versionStaleness } from '../components/version-staleness-info/version-staleness-info'\nimport { styles as buildErrorStyles } from '../container/build-error'\nimport { styles as containerErrorStyles } from '../container/errors'\nimport { styles as containerRuntimeErrorStyles } from '../container/runtime-error'\nimport { COPY_BUTTON_STYLES } from '../components/copy-button'\nimport { CALL_STACK_FRAME_STYLES } from '../components/call-stack-frame/call-stack-frame'\nimport { DEV_TOOLS_INDICATOR_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-indicator'\nimport { css } from '../../utils/css'\nimport { EDITOR_LINK_STYLES } from '../components/terminal/editor-link'\nimport { ENVIRONMENT_NAME_LABEL_STYLES } from '../components/errors/environment-name-label/environment-name-label'\nimport { DEV_TOOLS_INFO_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info'\nimport { DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-info/turbopack-info'\nimport { DEV_TOOLS_INFO_ROUTE_INFO_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-info/route-info'\nimport { DEV_TOOLS_INFO_USER_PREFERENCES_STYLES } from '../components/errors/dev-tools-indicator/dev-tools-info/user-preferences'\nimport { FADER_STYLES } from '../components/fader'\n\nexport function ComponentStyles() {\n  return (\n    <style>\n      {css`\n        ${COPY_BUTTON_STYLES}\n        ${CALL_STACK_FRAME_STYLES}\n        ${ENVIRONMENT_NAME_LABEL_STYLES}\n        ${overlay}\n        ${toast}\n        ${dialog}\n        ${errorLayout}\n        ${footer}\n        ${bottomStack}\n        ${pagination}\n        ${CODE_FRAME_STYLES}\n        ${TERMINAL_STYLES}\n        ${EDITOR_LINK_STYLES}\n        ${buildErrorStyles}\n        ${containerErrorStyles}\n        ${containerRuntimeErrorStyles}\n        ${versionStaleness}\n        ${DEV_TOOLS_INDICATOR_STYLES}\n        ${DEV_TOOLS_INFO_STYLES}\n        ${DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES}\n        ${DEV_TOOLS_INFO_ROUTE_INFO_STYLES}\n        ${DEV_TOOLS_INFO_USER_PREFERENCES_STYLES}\n        ${FADER_STYLES}\n      `}\n    </style>\n  )\n}\n"], "names": ["CODE_FRAME_STYLES", "styles", "dialog", "errorLayout", "bottomStack", "pagination", "overlay", "footer", "TERMINAL_STYLES", "toast", "versionStaleness", "buildErrorStyles", "containerErrorStyles", "containerRuntimeErrorStyles", "COPY_BUTTON_STYLES", "CALL_STACK_FRAME_STYLES", "DEV_TOOLS_INDICATOR_STYLES", "css", "EDITOR_LINK_STYLES", "ENVIRONMENT_NAME_LABEL_STYLES", "DEV_TOOLS_INFO_STYLES", "DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES", "DEV_TOOLS_INFO_ROUTE_INFO_STYLES", "DEV_TOOLS_INFO_USER_PREFERENCES_STYLES", "FADER_STYLES", "ComponentStyles", "style"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,iBAAiB,QAAQ,sCAAqC;AACvE,SAASC,UAAUC,MAAM,QAAQ,uBAAsB;AACvD,SAASD,UAAUE,WAAW,QAAQ,iEAAgE;AACtG,SAASF,UAAUG,WAAW,QAAQ,kDAAiD;AACvF,SAASH,UAAUI,UAAU,QAAQ,yEAAwE;AAC7G,SAASJ,UAAUK,OAAO,QAAQ,+BAA8B;AAChE,SAASL,UAAUM,MAAM,QAAQ,iEAAgE;AACjG,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SAASP,UAAUQ,KAAK,QAAQ,sBAAqB;AACrD,SAASR,UAAUS,gBAAgB,QAAQ,8DAA6D;AACxG,SAAST,UAAUU,gBAAgB,QAAQ,2BAA0B;AACrE,SAASV,UAAUW,oBAAoB,QAAQ,sBAAqB;AACpE,SAASX,UAAUY,2BAA2B,QAAQ,6BAA4B;AAClF,SAASC,kBAAkB,QAAQ,4BAA2B;AAC9D,SAASC,uBAAuB,QAAQ,kDAAiD;AACzF,SAASC,0BAA0B,QAAQ,+DAA8D;AACzG,SAASC,GAAG,QAAQ,kBAAiB;AACrC,SAASC,kBAAkB,QAAQ,qCAAoC;AACvE,SAASC,6BAA6B,QAAQ,qEAAoE;AAClH,SAASC,qBAAqB,QAAQ,yEAAwE;AAC9G,SAASC,oCAAoC,QAAQ,yEAAwE;AAC7H,SAASC,gCAAgC,QAAQ,qEAAoE;AACrH,SAASC,sCAAsC,QAAQ,2EAA0E;AACjI,SAASC,YAAY,QAAQ,sBAAqB;AAElD,OAAO,SAASC;IACd,qBACE,KAACC;kBACET,uBACGH,oBACAC,yBACAI,+BACAb,SACAG,OACAP,QACAC,aACAI,QACAH,aACAC,YACAL,mBACAQ,iBACAU,oBACAP,kBACAC,sBACAC,6BACAH,kBACAM,4BACAI,uBACAC,sCACAC,kCACAC,wCACAC;;AAIV"}