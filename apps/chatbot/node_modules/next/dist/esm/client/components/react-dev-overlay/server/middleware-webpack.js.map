{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware-webpack.ts"], "sourcesContent": ["import { constants as FS, promises as fs } from 'fs'\nimport { findSourceMap, type SourceMap } from 'module'\nimport path from 'path'\nimport { fileURLToPath, pathToFileURL } from 'url'\nimport {\n  SourceMapConsumer,\n  type BasicSourceMapConsumer,\n} from 'next/dist/compiled/source-map08'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport { getSourceMapFromFile } from '../utils/get-source-map-from-file'\nimport { launchEditor } from '../utils/launch-editor'\nimport {\n  getOriginalCodeFrame,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n} from './shared'\nimport { middlewareResponse } from './middleware-response'\nexport { getServerError } from '../utils/node-stack-frames'\nexport { parseStack } from '../utils/parse-stack'\nexport { getSourceMapFromFile }\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type webpack from 'webpack'\nimport type {\n  NullableMappedPosition,\n  RawSourceMap,\n} from 'next/dist/compiled/source-map08'\nimport { formatFrameSourceFile } from '../utils/webpack-module-path'\nimport type { MappedPosition } from 'source-map'\nimport { inspect } from 'util'\n\nfunction shouldIgnoreSource(sourceURL: string): boolean {\n  return (\n    sourceURL.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    sourceURL.includes('next/dist') ||\n    sourceURL.startsWith('node:')\n  )\n}\n\ntype IgnoredSources = Array<{ url: string; ignored: boolean }>\n\nexport interface IgnorableStackFrame extends StackFrame {\n  ignored: boolean\n}\n\ntype SourceAttributes = {\n  sourcePosition: NullableMappedPosition\n  sourceContent: string | null\n}\n\ntype Source =\n  | {\n      type: 'file'\n      sourceMap: RawSourceMap\n      ignoredSources: IgnoredSources\n      moduleURL: string\n    }\n  | {\n      type: 'bundle'\n      sourceMap: RawSourceMap\n      ignoredSources: IgnoredSources\n      compilation: webpack.Compilation\n      moduleId: string\n      moduleURL: string\n    }\n\nfunction getModuleById(\n  id: string | undefined,\n  compilation: webpack.Compilation\n) {\n  const { chunkGraph, modules } = compilation\n\n  return [...modules].find((module) => chunkGraph.getModuleId(module) === id)\n}\n\nfunction findModuleNotFoundFromError(errorMessage: string | undefined) {\n  return errorMessage?.match(/'([^']+)' module/)?.[1]\n}\n\nfunction getSourcePath(source: string) {\n  if (source.startsWith('file://')) {\n    return fileURLToPath(source)\n  }\n  return source.replace(/^(webpack:\\/\\/\\/|webpack:\\/\\/|webpack:\\/\\/_N_E\\/)/, '')\n}\n\n/**\n * @returns 1-based lines and 0-based columns\n */\nasync function findOriginalSourcePositionAndContent(\n  sourceMap: RawSourceMap,\n  position: { lineNumber: number | null; column: number | null }\n): Promise<SourceAttributes | null> {\n  let consumer: BasicSourceMapConsumer\n  try {\n    consumer = await new SourceMapConsumer(sourceMap)\n  } catch (cause) {\n    throw new Error(\n      `${sourceMap.file}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  try {\n    const sourcePosition = consumer.originalPositionFor({\n      line: position.lineNumber ?? 1,\n      // 0-based columns out requires 0-based columns in.\n      column: (position.column ?? 1) - 1,\n    })\n\n    if (!sourcePosition.source) {\n      return null\n    }\n\n    const sourceContent: string | null =\n      consumer.sourceContentFor(\n        sourcePosition.source,\n        /* returnNullOnMissing */ true\n      ) ?? null\n\n    return {\n      sourcePosition,\n      sourceContent,\n    }\n  } finally {\n    consumer.destroy()\n  }\n}\n\nexport function getIgnoredSources(\n  sourceMap: RawSourceMap & { ignoreList?: number[] }\n): IgnoredSources {\n  const ignoreList = new Set<number>(sourceMap.ignoreList ?? [])\n  const moduleFilenames = sourceMap?.sources ?? []\n\n  for (let index = 0; index < moduleFilenames.length; index++) {\n    // bundlerFilePath case: webpack://./app/page.tsx\n    const webpackSourceURL = moduleFilenames[index]\n    // Format the path to the normal file path\n    const formattedFilePath = formatFrameSourceFile(webpackSourceURL)\n    if (shouldIgnoreSource(formattedFilePath)) {\n      ignoreList.add(index)\n    }\n  }\n\n  const ignoredSources = sourceMap.sources.map((source, index) => {\n    return {\n      url: source,\n      ignored: ignoreList.has(sourceMap.sources.indexOf(source)),\n      content: sourceMap.sourcesContent?.[index] ?? null,\n    }\n  })\n  return ignoredSources\n}\n\nfunction isIgnoredSource(\n  source: Source,\n  sourcePosition: MappedPosition | NullableMappedPosition\n) {\n  if (sourcePosition.source == null) {\n    return true\n  }\n  for (const ignoredSource of source.ignoredSources) {\n    if (ignoredSource.ignored && ignoredSource.url === sourcePosition.source) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction findOriginalSourcePositionAndContentFromCompilation(\n  moduleId: string | undefined,\n  importedModule: string,\n  compilation: webpack.Compilation\n): SourceAttributes | null {\n  const module = getModuleById(moduleId, compilation)\n  return module?.buildInfo?.importLocByPath?.get(importedModule) ?? null\n}\n\nexport async function createOriginalStackFrame({\n  source,\n  rootDirectory,\n  frame,\n  errorMessage,\n}: {\n  source: Source\n  rootDirectory: string\n  frame: StackFrame\n  errorMessage?: string\n}): Promise<OriginalStackFrameResponse | null> {\n  const moduleNotFound = findModuleNotFoundFromError(errorMessage)\n  const result = await (() => {\n    if (moduleNotFound) {\n      if (source.type === 'file') {\n        return undefined\n      }\n\n      return findOriginalSourcePositionAndContentFromCompilation(\n        source.moduleId,\n        moduleNotFound,\n        source.compilation\n      )\n    }\n    return findOriginalSourcePositionAndContent(source.sourceMap, frame)\n  })()\n\n  if (!result) {\n    return null\n  }\n  const { sourcePosition, sourceContent } = result\n\n  if (!sourcePosition.source) {\n    return null\n  }\n\n  const ignored =\n    isIgnoredSource(source, sourcePosition) ||\n    // If the source file is externals, should be excluded even it's not ignored source.\n    // e.g. webpack://next/dist/.. needs to be ignored\n    shouldIgnoreSource(source.moduleURL)\n\n  const sourcePath = getSourcePath(\n    // When sourcePosition.source is the loader path the modulePath is generally better.\n    (sourcePosition.source!.includes('|')\n      ? source.moduleURL\n      : sourcePosition.source) || source.moduleURL\n  )\n  const filePath = path.resolve(rootDirectory, sourcePath)\n  const resolvedFilePath = path.relative(rootDirectory, filePath)\n\n  const traced: IgnorableStackFrame = {\n    file: resolvedFilePath,\n    lineNumber: sourcePosition.line,\n    column: (sourcePosition.column ?? 0) + 1,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      // default is not a valid identifier in JS so webpack uses a custom variable when it's an unnamed default export\n      // Resolve it back to `default` for the method name if the source position didn't have the method.\n      frame.methodName\n        ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n        ?.replace('__webpack_exports__.', ''),\n    arguments: [],\n    ignored,\n  }\n\n  return {\n    originalStackFrame: traced,\n    originalCodeFrame: getOriginalCodeFrame(traced, sourceContent),\n  }\n}\n\nasync function getSourceMapFromCompilation(\n  id: string,\n  compilation: webpack.Compilation\n): Promise<RawSourceMap | undefined> {\n  try {\n    const module = getModuleById(id, compilation)\n\n    if (!module) {\n      return undefined\n    }\n\n    // @ts-expect-error The types for `CodeGenerationResults.get` require a\n    // runtime to be passed as second argument, but apparently it also works\n    // without it.\n    const codeGenerationResult = compilation.codeGenerationResults.get(module)\n    const source = codeGenerationResult?.sources.get('javascript')\n\n    return source?.map() ?? undefined\n  } catch (err) {\n    console.error(`Failed to lookup module by ID (\"${id}\"):`, err)\n    return undefined\n  }\n}\n\nasync function getSource(\n  sourceURL: string,\n  options: {\n    getCompilations: () => webpack.Compilation[]\n  }\n): Promise<Source | undefined> {\n  const { getCompilations } = options\n\n  // Rspack is now using file:// URLs for source maps. Remove the rsc prefix to produce the file:/// url.\n  sourceURL = sourceURL.replace(/(.*)\\/(?=file:\\/\\/)/, '')\n\n  let nativeSourceMap: SourceMap | undefined\n  try {\n    nativeSourceMap = findSourceMap(sourceURL)\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (nativeSourceMap !== undefined) {\n    const sourceMapPayload = nativeSourceMap.payload\n    return {\n      type: 'file',\n      sourceMap: sourceMapPayload,\n      ignoredSources: getIgnoredSources(sourceMapPayload),\n      moduleURL: sourceURL,\n    }\n  }\n\n  if (path.isAbsolute(sourceURL)) {\n    sourceURL = pathToFileURL(sourceURL).href\n  }\n\n  if (sourceURL.startsWith('file:')) {\n    const sourceMap = await getSourceMapFromFile(sourceURL)\n    return sourceMap\n      ? {\n          type: 'file',\n          sourceMap,\n          ignoredSources: getIgnoredSources(sourceMap),\n          moduleURL: sourceURL,\n        }\n      : undefined\n  }\n\n  // webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n  // rsc://React/Server/webpack-internal:///(rsc)/./src/hello.tsx?42 => (rsc)/./src/hello.tsx\n  // webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n  const moduleId = sourceURL\n    .replace(\n      /^(rsc:\\/\\/React\\/[^/]+\\/)?(webpack-internal:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)/,\n      ''\n    )\n    .replace(/\\?\\d+$/, '')\n\n  // (rsc)/./src/hello.tsx => ./src/hello.tsx\n  const moduleURL = moduleId.replace(/^(\\(.*\\)\\/?)/, '')\n\n  for (const compilation of getCompilations()) {\n    const sourceMap = await getSourceMapFromCompilation(moduleId, compilation)\n\n    if (sourceMap) {\n      const ignoredSources = getIgnoredSources(sourceMap)\n      return {\n        type: 'bundle',\n        sourceMap,\n        compilation,\n        moduleId,\n        moduleURL,\n        ignoredSources,\n      }\n    }\n  }\n\n  return undefined\n}\n\nfunction getOriginalStackFrames({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frames,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frames: StackFrame[]\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFramesResponse> {\n  return Promise.all(\n    frames.map(\n      (frame): Promise<OriginalStackFramesResponse[number]> =>\n        getOriginalStackFrame({\n          isServer,\n          isEdgeServer,\n          isAppDirectory,\n          frame,\n          clientStats,\n          serverStats,\n          edgeServerStats,\n          rootDirectory,\n        }).then(\n          (value) => {\n            return {\n              status: 'fulfilled',\n              value,\n            }\n          },\n          (reason) => {\n            return {\n              status: 'rejected',\n              reason: inspect(reason, { colors: false }),\n            }\n          }\n        )\n    )\n  )\n}\n\nasync function getOriginalStackFrame({\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n  frame,\n  clientStats,\n  serverStats,\n  edgeServerStats,\n  rootDirectory,\n}: {\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n  frame: StackFrame\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n  rootDirectory: string\n}): Promise<OriginalStackFrameResponse> {\n  const filename = frame.file ?? ''\n  const source = await getSource(filename, {\n    getCompilations: () => {\n      const compilations: webpack.Compilation[] = []\n\n      // Try Client Compilation first. In `pages` we leverage\n      // `isClientError` to check. In `app` it depends on if it's a server\n      // / client component and when the code throws. E.g. during HTML\n      // rendering it's the server/edge compilation.\n      if ((!isEdgeServer && !isServer) || isAppDirectory) {\n        const compilation = clientStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Server Compilation. In `pages` this could be something\n      // imported in getServerSideProps/getStaticProps as the code for\n      // those is tree-shaken. In `app` this finds server components and\n      // code that was imported from a server component. It also covers\n      // when client component code throws during HTML rendering.\n      if (isServer || isAppDirectory) {\n        const compilation = serverStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      // Try Edge Server Compilation. Both cases are the same as Server\n      // Compilation, main difference is that it covers `runtime: 'edge'`\n      // pages/app routes.\n      if (isEdgeServer || isAppDirectory) {\n        const compilation = edgeServerStats()?.compilation\n\n        if (compilation) {\n          compilations.push(compilation)\n        }\n      }\n\n      return compilations\n    },\n  })\n\n  let defaultNormalizedStackFrameLocation = frame.file\n  if (\n    defaultNormalizedStackFrameLocation !== null &&\n    defaultNormalizedStackFrameLocation.startsWith('file://')\n  ) {\n    defaultNormalizedStackFrameLocation = path.relative(\n      rootDirectory,\n      fileURLToPath(defaultNormalizedStackFrameLocation)\n    )\n  }\n  // This stack frame is used for the one that couldn't locate the source or source mapped frame\n  const defaultStackFrame: IgnorableStackFrame = {\n    file: defaultNormalizedStackFrameLocation,\n    lineNumber: frame.lineNumber,\n    column: frame.column ?? 1,\n    methodName: frame.methodName,\n    ignored: shouldIgnoreSource(filename),\n    arguments: [],\n  }\n  if (!source) {\n    // return original stack frame with no source map\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n\n  const originalStackFrameResponse = await createOriginalStackFrame({\n    frame,\n    source,\n    rootDirectory,\n  })\n\n  if (!originalStackFrameResponse) {\n    return {\n      originalStackFrame: defaultStackFrame,\n      originalCodeFrame: null,\n    }\n  }\n\n  return originalStackFrameResponse\n}\n\nexport function getOverlayMiddleware(options: {\n  rootDirectory: string\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { rootDirectory, clientStats, serverStats, edgeServerStats } = options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      try {\n        const { frames, isServer, isEdgeServer, isAppDirectory } = JSON.parse(\n          body\n        ) as OriginalStackFramesRequest\n\n        return middlewareResponse.json(\n          res,\n          await getOriginalStackFrames({\n            isServer,\n            isEdgeServer,\n            isAppDirectory,\n            frames: frames.map((frame) => ({\n              ...frame,\n              lineNumber: frame.lineNumber ?? 0,\n              column: frame.column ?? 0,\n            })),\n            clientStats,\n            serverStats,\n            edgeServerStats,\n            rootDirectory,\n          })\n        )\n      } catch (err) {\n        return middlewareResponse.badRequest(res)\n      }\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const frame = {\n        file: searchParams.get('file') as string,\n        methodName: searchParams.get('methodName') as string,\n        lineNumber: parseInt(searchParams.get('lineNumber') ?? '0', 10) || 0,\n        column: parseInt(searchParams.get('column') ?? '0', 10) || 0,\n        arguments: searchParams.getAll('arguments').filter(Boolean),\n      } satisfies StackFrame\n\n      if (!frame.file) return middlewareResponse.badRequest(res)\n\n      // frame files may start with their webpack layer, like (middleware)/middleware.js\n      const filePath = path.resolve(\n        rootDirectory,\n        frame.file.replace(/^\\([^)]+\\)\\//, '')\n      )\n      const fileExists = await fs.access(filePath, FS.F_OK).then(\n        () => true,\n        () => false\n      )\n      if (!fileExists) return middlewareResponse.notFound(res)\n\n      try {\n        launchEditor(filePath, frame.lineNumber, frame.column ?? 1)\n      } catch (err) {\n        console.log('Failed to launch editor:', err)\n        return middlewareResponse.internalServerError(res)\n      }\n\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(options: {\n  clientStats: () => webpack.Stats | null\n  serverStats: () => webpack.Stats | null\n  edgeServerStats: () => webpack.Stats | null\n}) {\n  const { clientStats, serverStats, edgeServerStats } = options\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    const filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    let source: Source | undefined\n\n    try {\n      source = await getSource(filename, {\n        getCompilations: () => {\n          const compilations: webpack.Compilation[] = []\n\n          for (const stats of [\n            clientStats(),\n            serverStats(),\n            edgeServerStats(),\n          ]) {\n            if (stats?.compilation) {\n              compilations.push(stats.compilation)\n            }\n          }\n\n          return compilations\n        },\n      })\n    } catch (error) {\n      return middlewareResponse.internalServerError(res, error)\n    }\n\n    if (!source) {\n      return middlewareResponse.noContent(res)\n    }\n\n    return middlewareResponse.json(res, source.sourceMap)\n  }\n}\n"], "names": ["constants", "FS", "promises", "fs", "findSourceMap", "path", "fileURLToPath", "pathToFileURL", "SourceMapConsumer", "getSourceMapFromFile", "launchEditor", "getOriginalCodeFrame", "middlewareResponse", "getServerError", "parseStack", "formatFrameSourceFile", "inspect", "shouldIgnoreSource", "sourceURL", "includes", "startsWith", "getModuleById", "id", "compilation", "chunkGraph", "modules", "find", "module", "getModuleId", "findModuleNotFoundFromError", "errorMessage", "match", "getSourcePath", "source", "replace", "findOriginalSourcePositionAndContent", "sourceMap", "position", "consumer", "cause", "Error", "file", "sourcePosition", "originalPositionFor", "line", "lineNumber", "column", "sourceContent", "sourceContentFor", "destroy", "getIgnoredSources", "ignoreList", "Set", "moduleFilenames", "sources", "index", "length", "webpackSourceURL", "formattedFilePath", "add", "ignoredSources", "map", "url", "ignored", "has", "indexOf", "content", "sourcesContent", "isIgnoredSource", "ignoredSource", "findOriginalSourcePositionAndContentFromCompilation", "moduleId", "importedModule", "buildInfo", "importLocByPath", "get", "createOriginalStackFrame", "rootDirectory", "frame", "moduleNotFound", "result", "type", "undefined", "moduleURL", "sourcePath", "filePath", "resolve", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relative", "traced", "methodName", "arguments", "originalStackFrame", "originalCodeFrame", "getSourceMapFromCompilation", "codeGenerationResult", "codeGenerationResults", "err", "console", "error", "getSource", "options", "getCompilations", "nativeSourceMap", "sourceMapPayload", "payload", "isAbsolute", "href", "getOriginalStackFrames", "isServer", "isEdgeServer", "isAppDirectory", "frames", "clientStats", "serverStats", "edgeServerStats", "Promise", "all", "getOriginalStackFrame", "then", "value", "status", "reason", "colors", "filename", "compilations", "push", "defaultNormalizedStackFrameLocation", "defaultStackFrame", "originalStackFrameResponse", "getOverlayMiddleware", "req", "res", "next", "pathname", "searchParams", "URL", "method", "badRequest", "body", "reject", "data", "on", "chunk", "JSON", "parse", "json", "parseInt", "getAll", "filter", "Boolean", "fileExists", "access", "F_OK", "notFound", "log", "internalServerError", "noContent", "getSourceMapMiddleware", "stats"], "mappings": "AAAA,SAASA,aAAaC,EAAE,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACpD,SAASC,aAAa,QAAwB,SAAQ;AACtD,OAAOC,UAAU,OAAM;AACvB,SAASC,aAAa,EAAEC,aAAa,QAAQ,MAAK;AAClD,SACEC,iBAAiB,QAEZ,kCAAiC;AAExC,SAASC,oBAAoB,QAAQ,oCAAmC;AACxE,SAASC,YAAY,QAAQ,yBAAwB;AACrD,SACEC,oBAAoB,QAIf,WAAU;AACjB,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASL,oBAAoB,GAAE;AAQ/B,SAASM,qBAAqB,QAAQ,+BAA8B;AAEpE,SAASC,OAAO,QAAQ,OAAM;AAE9B,SAASC,mBAAmBC,SAAiB;IAC3C,OACEA,UAAUC,QAAQ,CAAC,mBACnB,2EAA2E;IAC3ED,UAAUC,QAAQ,CAAC,gBACnBD,UAAUE,UAAU,CAAC;AAEzB;AA6BA,SAASC,cACPC,EAAsB,EACtBC,WAAgC;IAEhC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAE,GAAGF;IAEhC,OAAO;WAAIE;KAAQ,CAACC,IAAI,CAAC,CAACC,SAAWH,WAAWI,WAAW,CAACD,YAAYL;AAC1E;AAEA,SAASO,4BAA4BC,YAAgC;QAC5DA;IAAP,OAAOA,iCAAAA,sBAAAA,aAAcC,KAAK,CAAC,wCAApBD,mBAAyC,CAAC,EAAE;AACrD;AAEA,SAASE,cAAcC,MAAc;IACnC,IAAIA,OAAOb,UAAU,CAAC,YAAY;QAChC,OAAOd,cAAc2B;IACvB;IACA,OAAOA,OAAOC,OAAO,CAAC,qDAAqD;AAC7E;AAEA;;CAEC,GACD,eAAeC,qCACbC,SAAuB,EACvBC,QAA8D;IAE9D,IAAIC;IACJ,IAAI;QACFA,WAAW,MAAM,IAAI9B,kBAAkB4B;IACzC,EAAE,OAAOG,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,KAAEJ,UAAUK,IAAI,GAAC,4FAClB;YAAEF;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAI;YAEMF,sBAEGA;QAHX,MAAMK,iBAAiBJ,SAASK,mBAAmB,CAAC;YAClDC,MAAMP,CAAAA,uBAAAA,SAASQ,UAAU,YAAnBR,uBAAuB;YAC7B,mDAAmD;YACnDS,QAAQ,AAACT,CAAAA,CAAAA,mBAAAA,SAASS,MAAM,YAAfT,mBAAmB,CAAA,IAAK;QACnC;QAEA,IAAI,CAACK,eAAeT,MAAM,EAAE;YAC1B,OAAO;QACT;YAGEK;QADF,MAAMS,gBACJT,CAAAA,6BAAAA,SAASU,gBAAgB,CACvBN,eAAeT,MAAM,EACrB,uBAAuB,GAAG,iBAF5BK,6BAGK;QAEP,OAAO;YACLI;YACAK;QACF;IACF,SAAU;QACRT,SAASW,OAAO;IAClB;AACF;AAEA,OAAO,SAASC,kBACdd,SAAmD;QAEhBA;IAAnC,MAAMe,aAAa,IAAIC,IAAYhB,CAAAA,wBAAAA,UAAUe,UAAU,YAApBf,wBAAwB,EAAE;QACrCA;IAAxB,MAAMiB,kBAAkBjB,CAAAA,qBAAAA,6BAAAA,UAAWkB,OAAO,YAAlBlB,qBAAsB,EAAE;IAEhD,IAAK,IAAImB,QAAQ,GAAGA,QAAQF,gBAAgBG,MAAM,EAAED,QAAS;QAC3D,iDAAiD;QACjD,MAAME,mBAAmBJ,eAAe,CAACE,MAAM;QAC/C,0CAA0C;QAC1C,MAAMG,oBAAoB3C,sBAAsB0C;QAChD,IAAIxC,mBAAmByC,oBAAoB;YACzCP,WAAWQ,GAAG,CAACJ;QACjB;IACF;IAEA,MAAMK,iBAAiBxB,UAAUkB,OAAO,CAACO,GAAG,CAAC,CAAC5B,QAAQsB;YAIzCnB;YAAAA;QAHX,OAAO;YACL0B,KAAK7B;YACL8B,SAASZ,WAAWa,GAAG,CAAC5B,UAAUkB,OAAO,CAACW,OAAO,CAAChC;YAClDiC,SAAS9B,CAAAA,mCAAAA,4BAAAA,UAAU+B,cAAc,qBAAxB/B,yBAA0B,CAACmB,MAAM,YAAjCnB,kCAAqC;QAChD;IACF;IACA,OAAOwB;AACT;AAEA,SAASQ,gBACPnC,MAAc,EACdS,cAAuD;IAEvD,IAAIA,eAAeT,MAAM,IAAI,MAAM;QACjC,OAAO;IACT;IACA,KAAK,MAAMoC,iBAAiBpC,OAAO2B,cAAc,CAAE;QACjD,IAAIS,cAAcN,OAAO,IAAIM,cAAcP,GAAG,KAAKpB,eAAeT,MAAM,EAAE;YACxE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASqC,oDACPC,QAA4B,EAC5BC,cAAsB,EACtBjD,WAAgC;QAGzBI,mCAAAA;IADP,MAAMA,SAASN,cAAckD,UAAUhD;QAChCI;IAAP,OAAOA,CAAAA,wCAAAA,2BAAAA,oBAAAA,OAAQ8C,SAAS,sBAAjB9C,oCAAAA,kBAAmB+C,eAAe,qBAAlC/C,kCAAoCgD,GAAG,CAACH,2BAAxC7C,wCAA2D;AACpE;AAEA,OAAO,eAAeiD,yBAAyB,KAU9C;IAV8C,IAAA,EAC7C3C,MAAM,EACN4C,aAAa,EACbC,KAAK,EACLhD,YAAY,EAMb,GAV8C;QAwDzC,sEAAsE;IACtE,4EAA4E;IAC5E,kCAAkC;IAClC,oGAAoG;IACpG,gHAAgH;IAChH,kGAAkG;IAClGgD,2BAAAA;IAnDJ,MAAMC,iBAAiBlD,4BAA4BC;IACnD,MAAMkD,SAAS,MAAM,AAAC,CAAA;QACpB,IAAID,gBAAgB;YAClB,IAAI9C,OAAOgD,IAAI,KAAK,QAAQ;gBAC1B,OAAOC;YACT;YAEA,OAAOZ,oDACLrC,OAAOsC,QAAQ,EACfQ,gBACA9C,OAAOV,WAAW;QAEtB;QACA,OAAOY,qCAAqCF,OAAOG,SAAS,EAAE0C;IAChE,CAAA;IAEA,IAAI,CAACE,QAAQ;QACX,OAAO;IACT;IACA,MAAM,EAAEtC,cAAc,EAAEK,aAAa,EAAE,GAAGiC;IAE1C,IAAI,CAACtC,eAAeT,MAAM,EAAE;QAC1B,OAAO;IACT;IAEA,MAAM8B,UACJK,gBAAgBnC,QAAQS,mBACxB,oFAAoF;IACpF,kDAAkD;IAClDzB,mBAAmBgB,OAAOkD,SAAS;IAErC,MAAMC,aAAapD,cAEjB,AADA,oFAAoF;IACnFU,CAAAA,eAAeT,MAAM,CAAEd,QAAQ,CAAC,OAC7Bc,OAAOkD,SAAS,GAChBzC,eAAeT,MAAM,AAAD,KAAMA,OAAOkD,SAAS;IAEhD,MAAME,WAAWhF,KAAKiF,OAAO,CAACT,eAAeO;IAC7C,MAAMG,mBAAmBlF,KAAKmF,QAAQ,CAACX,eAAeQ;QAK3C3C;IAHX,MAAM+C,SAA8B;QAClChD,MAAM8C;QACN1C,YAAYH,eAAeE,IAAI;QAC/BE,QAAQ,AAACJ,CAAAA,CAAAA,yBAAAA,eAAeI,MAAM,YAArBJ,yBAAyB,CAAA,IAAK;QACvCgD,UAAU,GAORZ,oBAAAA,MAAMY,UAAU,sBAAhBZ,4BAAAA,kBACI5C,OAAO,CAAC,8BAA8B,+BAD1C4C,0BAEI5C,OAAO,CAAC,wBAAwB;QACtCyD,WAAW,EAAE;QACb5B;IACF;IAEA,OAAO;QACL6B,oBAAoBH;QACpBI,mBAAmBlF,qBAAqB8E,QAAQ1C;IAClD;AACF;AAEA,eAAe+C,4BACbxE,EAAU,EACVC,WAAgC;IAEhC,IAAI;QACF,MAAMI,SAASN,cAAcC,IAAIC;QAEjC,IAAI,CAACI,QAAQ;YACX,OAAOuD;QACT;QAEA,uEAAuE;QACvE,wEAAwE;QACxE,cAAc;QACd,MAAMa,uBAAuBxE,YAAYyE,qBAAqB,CAACrB,GAAG,CAAChD;QACnE,MAAMM,SAAS8D,wCAAAA,qBAAsBzC,OAAO,CAACqB,GAAG,CAAC;YAE1C1C;QAAP,OAAOA,CAAAA,cAAAA,0BAAAA,OAAQ4B,GAAG,cAAX5B,cAAiBiD;IAC1B,EAAE,OAAOe,KAAK;QACZC,QAAQC,KAAK,CAAC,AAAC,qCAAkC7E,KAAG,OAAM2E;QAC1D,OAAOf;IACT;AACF;AAEA,eAAekB,UACblF,SAAiB,EACjBmF,OAEC;IAED,MAAM,EAAEC,eAAe,EAAE,GAAGD;IAE5B,uGAAuG;IACvGnF,YAAYA,UAAUgB,OAAO,CAAC,uBAAuB;IAErD,IAAIqE;IACJ,IAAI;QACFA,kBAAkBnG,cAAcc;IAClC,EAAE,OAAOqB,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,KAAEtB,YAAU,4FACb;YAAEqB;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIgE,oBAAoBrB,WAAW;QACjC,MAAMsB,mBAAmBD,gBAAgBE,OAAO;QAChD,OAAO;YACLxB,MAAM;YACN7C,WAAWoE;YACX5C,gBAAgBV,kBAAkBsD;YAClCrB,WAAWjE;QACb;IACF;IAEA,IAAIb,KAAKqG,UAAU,CAACxF,YAAY;QAC9BA,YAAYX,cAAcW,WAAWyF,IAAI;IAC3C;IAEA,IAAIzF,UAAUE,UAAU,CAAC,UAAU;QACjC,MAAMgB,YAAY,MAAM3B,qBAAqBS;QAC7C,OAAOkB,YACH;YACE6C,MAAM;YACN7C;YACAwB,gBAAgBV,kBAAkBd;YAClC+C,WAAWjE;QACb,IACAgE;IACN;IAEA,yDAAyD;IACzD,2FAA2F;IAC3F,oDAAoD;IACpD,MAAMX,WAAWrD,UACdgB,OAAO,CACN,6EACA,IAEDA,OAAO,CAAC,UAAU;IAErB,2CAA2C;IAC3C,MAAMiD,YAAYZ,SAASrC,OAAO,CAAC,gBAAgB;IAEnD,KAAK,MAAMX,eAAe+E,kBAAmB;QAC3C,MAAMlE,YAAY,MAAM0D,4BAA4BvB,UAAUhD;QAE9D,IAAIa,WAAW;YACb,MAAMwB,iBAAiBV,kBAAkBd;YACzC,OAAO;gBACL6C,MAAM;gBACN7C;gBACAb;gBACAgD;gBACAY;gBACAvB;YACF;QACF;IACF;IAEA,OAAOsB;AACT;AAEA,SAAS0B,uBAAuB,KAkB/B;IAlB+B,IAAA,EAC9BC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,WAAW,EACXC,eAAe,EACftC,aAAa,EAUd,GAlB+B;IAmB9B,OAAOuC,QAAQC,GAAG,CAChBL,OAAOnD,GAAG,CACR,CAACiB,QACCwC,sBAAsB;YACpBT;YACAC;YACAC;YACAjC;YACAmC;YACAC;YACAC;YACAtC;QACF,GAAG0C,IAAI,CACL,CAACC;YACC,OAAO;gBACLC,QAAQ;gBACRD;YACF;QACF,GACA,CAACE;YACC,OAAO;gBACLD,QAAQ;gBACRC,QAAQ1G,QAAQ0G,QAAQ;oBAAEC,QAAQ;gBAAM;YAC1C;QACF;AAIV;AAEA,eAAeL,sBAAsB,KAkBpC;IAlBoC,IAAA,EACnCT,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdjC,KAAK,EACLmC,WAAW,EACXC,WAAW,EACXC,eAAe,EACftC,aAAa,EAUd,GAlBoC;QAmBlBC;IAAjB,MAAM8C,WAAW9C,CAAAA,cAAAA,MAAMrC,IAAI,YAAVqC,cAAc;IAC/B,MAAM7C,SAAS,MAAMmE,UAAUwB,UAAU;QACvCtB,iBAAiB;YACf,MAAMuB,eAAsC,EAAE;YAE9C,uDAAuD;YACvD,oEAAoE;YACpE,gEAAgE;YAChE,8CAA8C;YAC9C,IAAI,AAAC,CAACf,gBAAgB,CAACD,YAAaE,gBAAgB;oBAC9BE;gBAApB,MAAM1F,eAAc0F,eAAAA,kCAAAA,aAAe1F,WAAW;gBAE9C,IAAIA,aAAa;oBACfsG,aAAaC,IAAI,CAACvG;gBACpB;YACF;YAEA,6DAA6D;YAC7D,gEAAgE;YAChE,kEAAkE;YAClE,iEAAiE;YACjE,2DAA2D;YAC3D,IAAIsF,YAAYE,gBAAgB;oBACVG;gBAApB,MAAM3F,eAAc2F,eAAAA,kCAAAA,aAAe3F,WAAW;gBAE9C,IAAIA,aAAa;oBACfsG,aAAaC,IAAI,CAACvG;gBACpB;YACF;YAEA,iEAAiE;YACjE,mEAAmE;YACnE,oBAAoB;YACpB,IAAIuF,gBAAgBC,gBAAgB;oBACdI;gBAApB,MAAM5F,eAAc4F,mBAAAA,sCAAAA,iBAAmB5F,WAAW;gBAElD,IAAIA,aAAa;oBACfsG,aAAaC,IAAI,CAACvG;gBACpB;YACF;YAEA,OAAOsG;QACT;IACF;IAEA,IAAIE,sCAAsCjD,MAAMrC,IAAI;IACpD,IACEsF,wCAAwC,QACxCA,oCAAoC3G,UAAU,CAAC,YAC/C;QACA2G,sCAAsC1H,KAAKmF,QAAQ,CACjDX,eACAvE,cAAcyH;IAElB;QAKUjD;IAJV,8FAA8F;IAC9F,MAAMkD,oBAAyC;QAC7CvF,MAAMsF;QACNlF,YAAYiC,MAAMjC,UAAU;QAC5BC,QAAQgC,CAAAA,gBAAAA,MAAMhC,MAAM,YAAZgC,gBAAgB;QACxBY,YAAYZ,MAAMY,UAAU;QAC5B3B,SAAS9C,mBAAmB2G;QAC5BjC,WAAW,EAAE;IACf;IACA,IAAI,CAAC1D,QAAQ;QACX,iDAAiD;QACjD,OAAO;YACL2D,oBAAoBoC;YACpBnC,mBAAmB;QACrB;IACF;IAEA,MAAMoC,6BAA6B,MAAMrD,yBAAyB;QAChEE;QACA7C;QACA4C;IACF;IAEA,IAAI,CAACoD,4BAA4B;QAC/B,OAAO;YACLrC,oBAAoBoC;YACpBnC,mBAAmB;QACrB;IACF;IAEA,OAAOoC;AACT;AAEA,OAAO,SAASC,qBAAqB7B,OAKpC;IACC,MAAM,EAAExB,aAAa,EAAEoC,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAGd;IAErE,OAAO,eACL8B,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUL,IAAIrE,GAAG;QAE7D,IAAIwE,aAAa,mCAAmC;YAClD,IAAIH,IAAIM,MAAM,KAAK,QAAQ;gBACzB,OAAO7H,mBAAmB8H,UAAU,CAACN;YACvC;YAEA,MAAMO,OAAO,MAAM,IAAIvB,QAAgB,CAAC9B,SAASsD;gBAC/C,IAAIC,OAAO;gBACXV,IAAIW,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAZ,IAAIW,EAAE,CAAC,OAAO,IAAMxD,QAAQuD;gBAC5BV,IAAIW,EAAE,CAAC,SAASF;YAClB;YAEA,IAAI;gBACF,MAAM,EAAE5B,MAAM,EAAEH,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAE,GAAGiC,KAAKC,KAAK,CACnEN;gBAGF,OAAO/H,mBAAmBsI,IAAI,CAC5Bd,KACA,MAAMxB,uBAAuB;oBAC3BC;oBACAC;oBACAC;oBACAC,QAAQA,OAAOnD,GAAG,CAAC,CAACiB;4BAENA,mBACJA;+BAHqB;4BAC7B,GAAGA,KAAK;4BACRjC,YAAYiC,CAAAA,oBAAAA,MAAMjC,UAAU,YAAhBiC,oBAAoB;4BAChChC,QAAQgC,CAAAA,gBAAAA,MAAMhC,MAAM,YAAZgC,gBAAgB;wBAC1B;;oBACAmC;oBACAC;oBACAC;oBACAtC;gBACF;YAEJ,EAAE,OAAOoB,KAAK;gBACZ,OAAOrF,mBAAmB8H,UAAU,CAACN;YACvC;QACF,OAAO,IAAIE,aAAa,2BAA2B;gBAI1BC,mBACJA;YAJnB,MAAMzD,QAAQ;gBACZrC,MAAM8F,aAAa5D,GAAG,CAAC;gBACvBe,YAAY6C,aAAa5D,GAAG,CAAC;gBAC7B9B,YAAYsG,SAASZ,CAAAA,oBAAAA,aAAa5D,GAAG,CAAC,yBAAjB4D,oBAAkC,KAAK,OAAO;gBACnEzF,QAAQqG,SAASZ,CAAAA,qBAAAA,aAAa5D,GAAG,CAAC,qBAAjB4D,qBAA8B,KAAK,OAAO;gBAC3D5C,WAAW4C,aAAaa,MAAM,CAAC,aAAaC,MAAM,CAACC;YACrD;YAEA,IAAI,CAACxE,MAAMrC,IAAI,EAAE,OAAO7B,mBAAmB8H,UAAU,CAACN;YAEtD,kFAAkF;YAClF,MAAM/C,WAAWhF,KAAKiF,OAAO,CAC3BT,eACAC,MAAMrC,IAAI,CAACP,OAAO,CAAC,gBAAgB;YAErC,MAAMqH,aAAa,MAAMpJ,GAAGqJ,MAAM,CAACnE,UAAUpF,GAAGwJ,IAAI,EAAElC,IAAI,CACxD,IAAM,MACN,IAAM;YAER,IAAI,CAACgC,YAAY,OAAO3I,mBAAmB8I,QAAQ,CAACtB;YAEpD,IAAI;oBACuCtD;gBAAzCpE,aAAa2E,UAAUP,MAAMjC,UAAU,EAAEiC,CAAAA,gBAAAA,MAAMhC,MAAM,YAAZgC,gBAAgB;YAC3D,EAAE,OAAOmB,KAAK;gBACZC,QAAQyD,GAAG,CAAC,4BAA4B1D;gBACxC,OAAOrF,mBAAmBgJ,mBAAmB,CAACxB;YAChD;YAEA,OAAOxH,mBAAmBiJ,SAAS,CAACzB;QACtC;QAEA,OAAOC;IACT;AACF;AAEA,OAAO,SAASyB,uBAAuBzD,OAItC;IACC,MAAM,EAAEY,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAGd;IAEtD,OAAO,eACL8B,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUL,IAAIrE,GAAG;QAE7D,IAAIwE,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,MAAMT,WAAWW,aAAa5D,GAAG,CAAC;QAElC,IAAI,CAACiD,UAAU;YACb,OAAOhH,mBAAmB8H,UAAU,CAACN;QACvC;QAEA,IAAInG;QAEJ,IAAI;YACFA,SAAS,MAAMmE,UAAUwB,UAAU;gBACjCtB,iBAAiB;oBACf,MAAMuB,eAAsC,EAAE;oBAE9C,KAAK,MAAMkC,SAAS;wBAClB9C;wBACAC;wBACAC;qBACD,CAAE;wBACD,IAAI4C,yBAAAA,MAAOxI,WAAW,EAAE;4BACtBsG,aAAaC,IAAI,CAACiC,MAAMxI,WAAW;wBACrC;oBACF;oBAEA,OAAOsG;gBACT;YACF;QACF,EAAE,OAAO1B,OAAO;YACd,OAAOvF,mBAAmBgJ,mBAAmB,CAACxB,KAAKjC;QACrD;QAEA,IAAI,CAAClE,QAAQ;YACX,OAAOrB,mBAAmBiJ,SAAS,CAACzB;QACtC;QAEA,OAAOxH,mBAAmBsI,IAAI,CAACd,KAAKnG,OAAOG,SAAS;IACtD;AACF"}