{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/hooks.ts"], "sourcesContent": ["import React from 'react'\nimport * as Bus from './bus'\nimport { useErrorOverlayReducer } from '../shared'\nimport { Router } from '../../../router'\n\nexport const usePagesDevOverlay = () => {\n  const [state, dispatch] = useErrorOverlayReducer('pages')\n\n  React.useEffect(() => {\n    Bus.on(dispatch)\n\n    const { handleStaticIndicator } =\n      require('./hot-reloader-client') as typeof import('./hot-reloader-client')\n\n    Router.events.on('routeChangeComplete', handleStaticIndicator)\n\n    return function () {\n      Router.events.off('routeChangeComplete', handleStaticIndicator)\n      Bus.off(dispatch)\n    }\n  }, [dispatch])\n\n  const onComponentError = React.useCallback(\n    (_error: Error, _componentStack: string | null) => {\n      // TODO: special handling\n    },\n    []\n  )\n\n  return {\n    state,\n    onComponentError,\n  }\n}\n"], "names": ["React", "Bus", "useErrorOverlayReducer", "Router", "usePagesDevOverlay", "state", "dispatch", "useEffect", "on", "handleStaticIndicator", "require", "events", "off", "onComponentError", "useCallback", "_error", "_componentStack"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,YAAYC,SAAS,QAAO;AAC5B,SAASC,sBAAsB,QAAQ,YAAW;AAClD,SAASC,MAAM,QAAQ,kBAAiB;AAExC,OAAO,MAAMC,qBAAqB;IAChC,MAAM,CAACC,OAAOC,SAAS,GAAGJ,uBAAuB;IAEjDF,MAAMO,SAAS,CAAC;QACdN,IAAIO,EAAE,CAACF;QAEP,MAAM,EAAEG,qBAAqB,EAAE,GAC7BC,QAAQ;QAEVP,OAAOQ,MAAM,CAACH,EAAE,CAAC,uBAAuBC;QAExC,OAAO;YACLN,OAAOQ,MAAM,CAACC,GAAG,CAAC,uBAAuBH;YACzCR,IAAIW,GAAG,CAACN;QACV;IACF,GAAG;QAACA;KAAS;IAEb,MAAMO,mBAAmBb,MAAMc,WAAW,CACxC,CAACC,QAAeC;IACd,yBAAyB;IAC3B,GACA,EAAE;IAGJ,OAAO;QACLX;QACAQ;IACF;AACF,EAAC"}