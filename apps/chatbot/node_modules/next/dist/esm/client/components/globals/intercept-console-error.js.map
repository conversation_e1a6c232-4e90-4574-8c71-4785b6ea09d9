{"version": 3, "sources": ["../../../../src/client/components/globals/intercept-console-error.ts"], "sourcesContent": ["import isError from '../../../lib/is-error'\nimport { isNextRouterError } from '../is-next-router-error'\nimport { handleConsoleError } from '../errors/use-error-handler'\nimport { parseConsoleArgs } from '../../lib/console'\n\nexport const originConsoleError = globalThis.console.error\n\n// Patch console.error to collect information about hydration errors\nexport function patchConsoleError() {\n  // Ensure it's only patched once\n  if (typeof window === 'undefined') {\n    return\n  }\n  window.console.error = function error(...args: any[]) {\n    let maybeError: unknown\n    if (process.env.NODE_ENV !== 'production') {\n      const { error: replayedError } = parseConsoleArgs(args)\n      if (replayedError) {\n        maybeError = replayedError\n      } else if (isError(args[0])) {\n        maybeError = args[0]\n      } else {\n        // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n        maybeError = args[1]\n      }\n    } else {\n      maybeError = args[0]\n    }\n\n    if (!isNextRouterError(maybeError)) {\n      if (process.env.NODE_ENV !== 'production') {\n        handleConsoleError(\n          // replayed errors have their own complex format string that should be used,\n          // but if we pass the error directly, `handleClientError` will ignore it\n          maybeError,\n          args\n        )\n      }\n\n      originConsoleError.apply(window.console, args)\n    }\n  }\n}\n"], "names": ["isError", "isNextRouterError", "handleConsoleError", "parseConsoleArgs", "originConsoleError", "globalThis", "console", "error", "patchConsoleError", "window", "args", "maybeError", "process", "env", "NODE_ENV", "replayedError", "apply"], "mappings": "AAAA,OAAOA,aAAa,wBAAuB;AAC3C,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,kBAAkB,QAAQ,8BAA6B;AAChE,SAASC,gBAAgB,QAAQ,oBAAmB;AAEpD,OAAO,MAAMC,qBAAqBC,WAAWC,OAAO,CAACC,KAAK,CAAA;AAE1D,oEAAoE;AACpE,OAAO,SAASC;IACd,gCAAgC;IAChC,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IACAA,OAAOH,OAAO,CAACC,KAAK,GAAG,SAASA;QAAM,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGG,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;YAAGA,KAAH,QAAA,SAAA,CAAA,KAAc;;QAClD,IAAIC;QACJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAM,EAAEP,OAAOQ,aAAa,EAAE,GAAGZ,iBAAiBO;YAClD,IAAIK,eAAe;gBACjBJ,aAAaI;YACf,OAAO,IAAIf,QAAQU,IAAI,CAAC,EAAE,GAAG;gBAC3BC,aAAaD,IAAI,CAAC,EAAE;YACtB,OAAO;gBACL,iJAAiJ;gBACjJC,aAAaD,IAAI,CAAC,EAAE;YACtB;QACF,OAAO;YACLC,aAAaD,IAAI,CAAC,EAAE;QACtB;QAEA,IAAI,CAACT,kBAAkBU,aAAa;YAClC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzCZ,mBACE,4EAA4E;gBAC5E,wEAAwE;gBACxES,YACAD;YAEJ;YAEAN,mBAAmBY,KAAK,CAACP,OAAOH,OAAO,EAAEI;QAC3C;IACF;AACF"}