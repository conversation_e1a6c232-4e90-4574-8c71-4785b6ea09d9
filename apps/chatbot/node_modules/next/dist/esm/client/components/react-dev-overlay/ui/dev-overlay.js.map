{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/ui/dev-overlay.tsx"], "sourcesContent": ["import type { OverlayState } from '../shared'\n\nimport { ShadowPortal } from './components/shadow-portal'\nimport { Base } from './styles/base'\nimport { ComponentStyles } from './styles/component-styles'\nimport { CssReset } from './styles/css-reset'\nimport { Colors } from './styles/colors'\nimport { ErrorOverlay } from './components/errors/error-overlay/error-overlay'\nimport { DevToolsIndicator } from './components/errors/dev-tools-indicator/dev-tools-indicator'\nimport { RenderError } from './container/runtime-error/render-error'\nimport { DarkTheme } from './styles/dark-theme'\nimport { useDevToolsScale } from './components/errors/dev-tools-indicator/dev-tools-info/preferences'\n\nexport function DevOverlay({\n  state,\n  isErrorOverlayOpen,\n  setIsErrorOverlayOpen,\n}: {\n  state: OverlayState\n  isErrorOverlayOpen: boolean\n  setIsErrorOverlayOpen: (\n    isErrorOverlayOpen: boolean | ((prev: boolean) => boolean)\n  ) => void\n}) {\n  const [scale, setScale] = useDevToolsScale()\n  return (\n    <ShadowPortal>\n      <CssReset />\n      <Base scale={scale} />\n      <Colors />\n      <ComponentStyles />\n      <DarkTheme />\n\n      <RenderError state={state} isAppDir={true}>\n        {({ runtimeErrors, totalErrorCount }) => {\n          const isBuildError = runtimeErrors.length === 0\n          return (\n            <>\n              <DevToolsIndicator\n                scale={scale}\n                setScale={setScale}\n                state={state}\n                errorCount={totalErrorCount}\n                isBuildError={isBuildError}\n                setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n              />\n\n              <ErrorOverlay\n                state={state}\n                runtimeErrors={runtimeErrors}\n                isErrorOverlayOpen={isErrorOverlayOpen}\n                setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n              />\n            </>\n          )\n        }}\n      </RenderError>\n    </ShadowPortal>\n  )\n}\n"], "names": ["ShadowPort<PERSON>", "Base", "ComponentStyles", "CssReset", "Colors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DevToolsIndicator", "RenderError", "DarkTheme", "useDevToolsScale", "DevOverlay", "state", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "scale", "setScale", "isAppDir", "runtimeErrors", "totalErrorCount", "isBuildError", "length", "errorCount"], "mappings": ";AAEA,SAASA,YAAY,QAAQ,6BAA4B;AACzD,SAASC,IAAI,QAAQ,gBAAe;AACpC,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,QAAQ,QAAQ,qBAAoB;AAC7C,SAASC,MAAM,QAAQ,kBAAiB;AACxC,SAASC,YAAY,QAAQ,kDAAiD;AAC9E,SAASC,iBAAiB,QAAQ,8DAA6D;AAC/F,SAASC,WAAW,QAAQ,yCAAwC;AACpE,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,gBAAgB,QAAQ,qEAAoE;AAErG,OAAO,SAASC,WAAW,KAU1B;IAV0B,IAAA,EACzBC,KAAK,EACLC,kBAAkB,EAClBC,qBAAqB,EAOtB,GAV0B;IAWzB,MAAM,CAACC,OAAOC,SAAS,GAAGN;IAC1B,qBACE,MAACT;;0BACC,KAACG;0BACD,KAACF;gBAAKa,OAAOA;;0BACb,KAACV;0BACD,KAACF;0BACD,KAACM;0BAED,KAACD;gBAAYI,OAAOA;gBAAOK,UAAU;0BAClC;wBAAC,EAAEC,aAAa,EAAEC,eAAe,EAAE;oBAClC,MAAMC,eAAeF,cAAcG,MAAM,KAAK;oBAC9C,qBACE;;0CACE,KAACd;gCACCQ,OAAOA;gCACPC,UAAUA;gCACVJ,OAAOA;gCACPU,YAAYH;gCACZC,cAAcA;gCACdN,uBAAuBA;;0CAGzB,KAACR;gCACCM,OAAOA;gCACPM,eAAeA;gCACfL,oBAAoBA;gCACpBC,uBAAuBA;;;;gBAI/B;;;;AAIR"}