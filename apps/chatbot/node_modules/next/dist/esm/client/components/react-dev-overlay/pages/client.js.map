{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/client.ts"], "sourcesContent": ["import * as Bus from './bus'\nimport { parseStack } from '../utils/parse-stack'\nimport { parseComponentStack } from '../utils/parse-component-stack'\nimport { storeHydrationErrorStateFromConsoleArgs } from '../../errors/hydration-error-info'\nimport {\n  ACTION_BEFORE_REFRESH,\n  ACTION_BUILD_ERROR,\n  ACTION_BUILD_OK,\n  ACTION_DEV_INDICATOR,\n  ACTION_REFRESH,\n  ACTION_STATIC_INDICATOR,\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  ACTION_VERSION_INFO,\n} from '../shared'\nimport type { VersionInfo } from '../../../../server/dev/parse-version-info'\nimport { attachHydrationErrorState } from '../../errors/attach-hydration-error-state'\nimport type { DevIndicatorServerState } from '../../../../server/dev/dev-indicator-server-state'\n\nlet isRegistered = false\n\nfunction handleError(error: unknown) {\n  if (!error || !(error instanceof Error) || typeof error.stack !== 'string') {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  attachHydrationErrorState(error)\n\n  const componentStackTrace = (error as any)._componentStack\n  const componentStackFrames =\n    typeof componentStackTrace === 'string'\n      ? parseComponentStack(componentStackTrace)\n      : undefined\n\n  // Skip ModuleBuildError and ModuleNotFoundError, as it will be sent through onBuildError callback.\n  // This is to avoid same error as different type showing up on client to cause flashing.\n  if (\n    error.name !== 'ModuleBuildError' &&\n    error.name !== 'ModuleNotFoundError'\n  ) {\n    Bus.emit({\n      type: ACTION_UNHANDLED_ERROR,\n      reason: error,\n      frames: parseStack(error.stack),\n      componentStackFrames,\n    })\n  }\n}\n\nlet origConsoleError = console.error\nfunction nextJsHandleConsoleError(...args: any[]) {\n  // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n  const error = process.env.NODE_ENV !== 'production' ? args[1] : args[0]\n  storeHydrationErrorStateFromConsoleArgs(...args)\n  handleError(error)\n  origConsoleError.apply(window.console, args)\n}\n\nfunction onUnhandledError(event: ErrorEvent) {\n  const error = event?.error\n  handleError(error)\n}\n\nfunction onUnhandledRejection(ev: PromiseRejectionEvent) {\n  const reason = ev?.reason\n  if (\n    !reason ||\n    !(reason instanceof Error) ||\n    typeof reason.stack !== 'string'\n  ) {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  const e = reason\n  Bus.emit({\n    type: ACTION_UNHANDLED_REJECTION,\n    reason: reason,\n    frames: parseStack(e.stack!),\n  })\n}\n\nexport function register() {\n  if (isRegistered) {\n    return\n  }\n  isRegistered = true\n\n  try {\n    Error.stackTraceLimit = 50\n  } catch {}\n\n  window.addEventListener('error', onUnhandledError)\n  window.addEventListener('unhandledrejection', onUnhandledRejection)\n  window.console.error = nextJsHandleConsoleError\n}\n\nexport function onBuildOk() {\n  Bus.emit({ type: ACTION_BUILD_OK })\n}\n\nexport function onBuildError(message: string) {\n  Bus.emit({ type: ACTION_BUILD_ERROR, message })\n}\n\nexport function onRefresh() {\n  Bus.emit({ type: ACTION_REFRESH })\n}\n\nexport function onBeforeRefresh() {\n  Bus.emit({ type: ACTION_BEFORE_REFRESH })\n}\n\nexport function onVersionInfo(versionInfo: VersionInfo) {\n  Bus.emit({ type: ACTION_VERSION_INFO, versionInfo })\n}\n\nexport function onStaticIndicator(isStatic: boolean) {\n  Bus.emit({ type: ACTION_STATIC_INDICATOR, staticIndicator: isStatic })\n}\n\nexport function onDevIndicator(devIndicatorsState: DevIndicatorServerState) {\n  Bus.emit({ type: ACTION_DEV_INDICATOR, devIndicator: devIndicatorsState })\n}\n\nexport { getErrorByType } from '../utils/get-error-by-type'\nexport { getServerError } from '../utils/node-stack-frames'\n"], "names": ["Bus", "parseStack", "parseComponentStack", "storeHydrationErrorStateFromConsoleArgs", "ACTION_BEFORE_REFRESH", "ACTION_BUILD_ERROR", "ACTION_BUILD_OK", "ACTION_DEV_INDICATOR", "ACTION_REFRESH", "ACTION_STATIC_INDICATOR", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_VERSION_INFO", "attachHydrationErrorState", "isRegistered", "handleError", "error", "Error", "stack", "componentStackTrace", "_componentStack", "componentStackFrames", "undefined", "name", "emit", "type", "reason", "frames", "origConsoleError", "console", "nextJsHandleConsoleError", "args", "process", "env", "NODE_ENV", "apply", "window", "onUnhandledError", "event", "onUnhandledRejection", "ev", "e", "register", "stackTraceLimit", "addEventListener", "onBuildOk", "onBuildError", "message", "onRefresh", "onBeforeRefresh", "onVersionInfo", "versionInfo", "onStaticIndicator", "isStatic", "staticIndicator", "onDevIndicator", "devIndicatorsState", "devIndicator", "getErrorByType", "getServerError"], "mappings": "AAAA,YAAYA,SAAS,QAAO;AAC5B,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,mBAAmB,QAAQ,iCAAgC;AACpE,SAASC,uCAAuC,QAAQ,oCAAmC;AAC3F,SACEC,qBAAqB,EACrBC,kBAAkB,EAClBC,eAAe,EACfC,oBAAoB,EACpBC,cAAc,EACdC,uBAAuB,EACvBC,sBAAsB,EACtBC,0BAA0B,EAC1BC,mBAAmB,QACd,YAAW;AAElB,SAASC,yBAAyB,QAAQ,4CAA2C;AAGrF,IAAIC,eAAe;AAEnB,SAASC,YAAYC,KAAc;IACjC,IAAI,CAACA,SAAS,CAAEA,CAAAA,iBAAiBC,KAAI,KAAM,OAAOD,MAAME,KAAK,KAAK,UAAU;QAC1E,8DAA8D;QAC9D;IACF;IAEAL,0BAA0BG;IAE1B,MAAMG,sBAAsB,AAACH,MAAcI,eAAe;IAC1D,MAAMC,uBACJ,OAAOF,wBAAwB,WAC3BjB,oBAAoBiB,uBACpBG;IAEN,mGAAmG;IACnG,wFAAwF;IACxF,IACEN,MAAMO,IAAI,KAAK,sBACfP,MAAMO,IAAI,KAAK,uBACf;QACAvB,IAAIwB,IAAI,CAAC;YACPC,MAAMf;YACNgB,QAAQV;YACRW,QAAQ1B,WAAWe,MAAME,KAAK;YAC9BG;QACF;IACF;AACF;AAEA,IAAIO,mBAAmBC,QAAQb,KAAK;AACpC,SAASc;IAAyB,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAc;;IAC9C,iJAAiJ;IACjJ,MAAMf,QAAQgB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAeH,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;IACvE5B,2CAA2C4B;IAC3ChB,YAAYC;IACZY,iBAAiBO,KAAK,CAACC,OAAOP,OAAO,EAAEE;AACzC;AAEA,SAASM,iBAAiBC,KAAiB;IACzC,MAAMtB,QAAQsB,yBAAAA,MAAOtB,KAAK;IAC1BD,YAAYC;AACd;AAEA,SAASuB,qBAAqBC,EAAyB;IACrD,MAAMd,SAASc,sBAAAA,GAAId,MAAM;IACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBT,KAAI,KACxB,OAAOS,OAAOR,KAAK,KAAK,UACxB;QACA,8DAA8D;QAC9D;IACF;IAEA,MAAMuB,IAAIf;IACV1B,IAAIwB,IAAI,CAAC;QACPC,MAAMd;QACNe,QAAQA;QACRC,QAAQ1B,WAAWwC,EAAEvB,KAAK;IAC5B;AACF;AAEA,OAAO,SAASwB;IACd,IAAI5B,cAAc;QAChB;IACF;IACAA,eAAe;IAEf,IAAI;QACFG,MAAM0B,eAAe,GAAG;IAC1B,EAAE,UAAM,CAAC;IAETP,OAAOQ,gBAAgB,CAAC,SAASP;IACjCD,OAAOQ,gBAAgB,CAAC,sBAAsBL;IAC9CH,OAAOP,OAAO,CAACb,KAAK,GAAGc;AACzB;AAEA,OAAO,SAASe;IACd7C,IAAIwB,IAAI,CAAC;QAAEC,MAAMnB;IAAgB;AACnC;AAEA,OAAO,SAASwC,aAAaC,OAAe;IAC1C/C,IAAIwB,IAAI,CAAC;QAAEC,MAAMpB;QAAoB0C;IAAQ;AAC/C;AAEA,OAAO,SAASC;IACdhD,IAAIwB,IAAI,CAAC;QAAEC,MAAMjB;IAAe;AAClC;AAEA,OAAO,SAASyC;IACdjD,IAAIwB,IAAI,CAAC;QAAEC,MAAMrB;IAAsB;AACzC;AAEA,OAAO,SAAS8C,cAAcC,WAAwB;IACpDnD,IAAIwB,IAAI,CAAC;QAAEC,MAAMb;QAAqBuC;IAAY;AACpD;AAEA,OAAO,SAASC,kBAAkBC,QAAiB;IACjDrD,IAAIwB,IAAI,CAAC;QAAEC,MAAMhB;QAAyB6C,iBAAiBD;IAAS;AACtE;AAEA,OAAO,SAASE,eAAeC,kBAA2C;IACxExD,IAAIwB,IAAI,CAAC;QAAEC,MAAMlB;QAAsBkD,cAAcD;IAAmB;AAC1E;AAEA,SAASE,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,cAAc,QAAQ,6BAA4B"}