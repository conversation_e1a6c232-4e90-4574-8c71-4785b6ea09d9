{"version": 3, "sources": ["../../../src/client/components/app-router-announcer.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport type { FlightRouterState } from '../../server/app-render/types'\n\nconst ANNOUNCER_TYPE = 'next-route-announcer'\nconst ANNOUNCER_ID = '__next-route-announcer__'\n\nfunction getAnnouncerNode() {\n  const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0]\n  if (existingAnnouncer?.shadowRoot?.childNodes[0]) {\n    return existingAnnouncer.shadowRoot.childNodes[0] as HTMLElement\n  } else {\n    const container = document.createElement(ANNOUNCER_TYPE)\n    container.style.cssText = 'position:absolute'\n    const announcer = document.createElement('div')\n    announcer.ariaLive = 'assertive'\n    announcer.id = ANNOUNCER_ID\n    announcer.role = 'alert'\n    announcer.style.cssText =\n      'position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal'\n\n    // Use shadow DOM here to avoid any potential CSS bleed\n    const shadow = container.attachShadow({ mode: 'open' })\n    shadow.appendChild(announcer)\n    document.body.appendChild(container)\n    return announcer\n  }\n}\n\nexport function AppRouterAnnouncer({ tree }: { tree: FlightRouterState }) {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const announcer = getAnnouncerNode()\n    setPortalNode(announcer)\n    return () => {\n      const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0]\n      if (container?.isConnected) {\n        document.body.removeChild(container)\n      }\n    }\n  }, [])\n\n  const [routeAnnouncement, setRouteAnnouncement] = useState('')\n  const previousTitle = useRef<string | undefined>(undefined)\n\n  useEffect(() => {\n    let currentTitle = ''\n    if (document.title) {\n      currentTitle = document.title\n    } else {\n      const pageHeader = document.querySelector('h1')\n      if (pageHeader) {\n        currentTitle = pageHeader.innerText || pageHeader.textContent || ''\n      }\n    }\n\n    // Only announce the title change, but not for the first load because screen\n    // readers do that automatically.\n    if (\n      previousTitle.current !== undefined &&\n      previousTitle.current !== currentTitle\n    ) {\n      setRouteAnnouncement(currentTitle)\n    }\n    previousTitle.current = currentTitle\n  }, [tree])\n\n  return portalNode ? createPortal(routeAnnouncement, portalNode) : null\n}\n"], "names": ["useEffect", "useRef", "useState", "createPortal", "ANNOUNCER_TYPE", "ANNOUNCER_ID", "getAnnouncerNode", "existingAnnouncer", "document", "getElementsByName", "shadowRoot", "childNodes", "container", "createElement", "style", "cssText", "announcer", "ariaLive", "id", "role", "shadow", "attachShadow", "mode", "append<PERSON><PERSON><PERSON>", "body", "AppRouterAnnouncer", "tree", "portalNode", "setPortalNode", "getElementsByTagName", "isConnected", "<PERSON><PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "previousTitle", "undefined", "currentTitle", "title", "pageHeader", "querySelector", "innerText", "textContent", "current"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,QAAO;AACnD,SAASC,YAAY,QAAQ,YAAW;AAGxC,MAAMC,iBAAiB;AACvB,MAAMC,eAAe;AAErB,SAASC;QAEHC;IADJ,MAAMA,oBAAoBC,SAASC,iBAAiB,CAACL,eAAe,CAAC,EAAE;IACvE,IAAIG,sCAAAA,gCAAAA,kBAAmBG,UAAU,qBAA7BH,8BAA+BI,UAAU,CAAC,EAAE,EAAE;QAChD,OAAOJ,kBAAkBG,UAAU,CAACC,UAAU,CAAC,EAAE;IACnD,OAAO;QACL,MAAMC,YAAYJ,SAASK,aAAa,CAACT;QACzCQ,UAAUE,KAAK,CAACC,OAAO,GAAG;QAC1B,MAAMC,YAAYR,SAASK,aAAa,CAAC;QACzCG,UAAUC,QAAQ,GAAG;QACrBD,UAAUE,EAAE,GAAGb;QACfW,UAAUG,IAAI,GAAG;QACjBH,UAAUF,KAAK,CAACC,OAAO,GACrB;QAEF,uDAAuD;QACvD,MAAMK,SAASR,UAAUS,YAAY,CAAC;YAAEC,MAAM;QAAO;QACrDF,OAAOG,WAAW,CAACP;QACnBR,SAASgB,IAAI,CAACD,WAAW,CAACX;QAC1B,OAAOI;IACT;AACF;AAEA,OAAO,SAASS,mBAAmB,KAAqC;IAArC,IAAA,EAAEC,IAAI,EAA+B,GAArC;IACjC,MAAM,CAACC,YAAYC,cAAc,GAAG1B,SAA6B;IAEjEF,UAAU;QACR,MAAMgB,YAAYV;QAClBsB,cAAcZ;QACd,OAAO;YACL,MAAMJ,YAAYJ,SAASqB,oBAAoB,CAACzB,eAAe,CAAC,EAAE;YAClE,IAAIQ,6BAAAA,UAAWkB,WAAW,EAAE;gBAC1BtB,SAASgB,IAAI,CAACO,WAAW,CAACnB;YAC5B;QACF;IACF,GAAG,EAAE;IAEL,MAAM,CAACoB,mBAAmBC,qBAAqB,GAAG/B,SAAS;IAC3D,MAAMgC,gBAAgBjC,OAA2BkC;IAEjDnC,UAAU;QACR,IAAIoC,eAAe;QACnB,IAAI5B,SAAS6B,KAAK,EAAE;YAClBD,eAAe5B,SAAS6B,KAAK;QAC/B,OAAO;YACL,MAAMC,aAAa9B,SAAS+B,aAAa,CAAC;YAC1C,IAAID,YAAY;gBACdF,eAAeE,WAAWE,SAAS,IAAIF,WAAWG,WAAW,IAAI;YACnE;QACF;QAEA,4EAA4E;QAC5E,iCAAiC;QACjC,IACEP,cAAcQ,OAAO,KAAKP,aAC1BD,cAAcQ,OAAO,KAAKN,cAC1B;YACAH,qBAAqBG;QACvB;QACAF,cAAcQ,OAAO,GAAGN;IAC1B,GAAG;QAACV;KAAK;IAET,OAAOC,2BAAaxB,aAAa6B,mBAAmBL,cAAc;AACpE"}