{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/parse-component-stack.ts"], "sourcesContent": ["export type ComponentStackFrame = {\n  canOpenInEditor: boolean\n  component: string\n  file?: string\n  lineNumber?: number\n  column?: number\n}\n\nenum LocationType {\n  FILE = 'file',\n  WEBPACK_INTERNAL = 'webpack-internal',\n  HTTP = 'http',\n  PROTOCOL_RELATIVE = 'protocol-relative',\n  UNKNOWN = 'unknown',\n}\n\n/**\n * Get the type of frame line based on the location\n */\nfunction getLocationType(location: string): LocationType {\n  if (location.startsWith('file://')) {\n    return LocationType.FILE\n  }\n  if (location.includes('webpack-internal://')) {\n    return LocationType.WEBPACK_INTERNAL\n  }\n  if (location.startsWith('http://') || location.startsWith('https://')) {\n    return LocationType.HTTP\n  }\n  if (location.startsWith('//')) {\n    return LocationType.PROTOCOL_RELATIVE\n  }\n  return LocationType.UNKNOWN\n}\n\nfunction parseStackFrameLocation(\n  location: string\n): Omit<ComponentStackFrame, 'component'> {\n  const locationType = getLocationType(location)\n\n  const modulePath = location?.replace(\n    /^(webpack-internal:\\/\\/\\/|file:\\/\\/)(\\(.*\\)\\/)?/,\n    ''\n  )\n  const [, file, lineNumber, column] =\n    modulePath?.match(/^(.+):(\\d+):(\\d+)/) ?? []\n\n  switch (locationType) {\n    case LocationType.FILE:\n    case LocationType.WEBPACK_INTERNAL:\n      return {\n        canOpenInEditor: true,\n        file,\n        lineNumber: lineNumber ? Number(lineNumber) : undefined,\n        column: column ? Number(column) : undefined,\n      }\n    // When the location is a URL we only show the file\n    // TODO: Resolve http(s) URLs through sourcemaps\n    case LocationType.HTTP:\n    case LocationType.PROTOCOL_RELATIVE:\n    case LocationType.UNKNOWN:\n    default: {\n      return {\n        canOpenInEditor: false,\n      }\n    }\n  }\n}\n\nexport function parseComponentStack(\n  componentStack: string\n): ComponentStackFrame[] {\n  const componentStackFrames: ComponentStackFrame[] = []\n  for (const line of componentStack.trim().split('\\n')) {\n    // TODO: support safari stack trace\n    // Get component and file from the component stack line\n    const match = /at ([^ ]+)( \\((.*)\\))?/.exec(line)\n    if (match?.[1]) {\n      const component = match[1]\n      const location = match[3]\n\n      if (!location) {\n        componentStackFrames.push({\n          canOpenInEditor: false,\n          component,\n        })\n        continue\n      }\n\n      // Stop parsing the component stack if we reach a Next.js component\n      if (location?.includes('next/dist')) {\n        break\n      }\n\n      const frameLocation = parseStackFrameLocation(location)\n      componentStackFrames.push({\n        component,\n        ...frameLocation,\n      })\n    }\n  }\n\n  return componentStackFrames\n}\n"], "names": ["LocationType", "getLocationType", "location", "startsWith", "includes", "parseStackFrameLocation", "locationType", "modulePath", "replace", "file", "lineNumber", "column", "match", "canOpenInEditor", "Number", "undefined", "parseComponentStack", "componentStack", "componentStackFrames", "line", "trim", "split", "exec", "component", "push", "frameLocation"], "mappings": "AAQA,IAAA,AAAKA,sCAAAA;;;;;;WAAAA;EAAAA;AAQL;;CAEC,GACD,SAASC,gBAAgBC,QAAgB;IACvC,IAAIA,SAASC,UAAU,CAAC,YAAY;QAClC;IACF;IACA,IAAID,SAASE,QAAQ,CAAC,wBAAwB;QAC5C;IACF;IACA,IAAIF,SAASC,UAAU,CAAC,cAAcD,SAASC,UAAU,CAAC,aAAa;QACrE;IACF;IACA,IAAID,SAASC,UAAU,CAAC,OAAO;QAC7B;IACF;IACA;AACF;AAEA,SAASE,wBACPH,QAAgB;IAEhB,MAAMI,eAAeL,gBAAgBC;IAErC,MAAMK,aAAaL,4BAAAA,SAAUM,OAAO,CAClC,mDACA;QAGAD;IADF,MAAM,GAAGE,MAAMC,YAAYC,OAAO,GAChCJ,CAAAA,oBAAAA,8BAAAA,WAAYK,KAAK,CAAC,gCAAlBL,oBAA0C,EAAE;IAE9C,OAAQD;QACN;QACA;YACE,OAAO;gBACLO,iBAAiB;gBACjBJ;gBACAC,YAAYA,aAAaI,OAAOJ,cAAcK;gBAC9CJ,QAAQA,SAASG,OAAOH,UAAUI;YACpC;QACF,mDAAmD;QACnD,gDAAgD;QAChD;QACA;QACA;QACA;YAAS;gBACP,OAAO;oBACLF,iBAAiB;gBACnB;YACF;IACF;AACF;AAEA,OAAO,SAASG,oBACdC,cAAsB;IAEtB,MAAMC,uBAA8C,EAAE;IACtD,KAAK,MAAMC,QAAQF,eAAeG,IAAI,GAAGC,KAAK,CAAC,MAAO;QACpD,mCAAmC;QACnC,uDAAuD;QACvD,MAAMT,QAAQ,yBAAyBU,IAAI,CAACH;QAC5C,IAAIP,yBAAAA,KAAO,CAAC,EAAE,EAAE;YACd,MAAMW,YAAYX,KAAK,CAAC,EAAE;YAC1B,MAAMV,WAAWU,KAAK,CAAC,EAAE;YAEzB,IAAI,CAACV,UAAU;gBACbgB,qBAAqBM,IAAI,CAAC;oBACxBX,iBAAiB;oBACjBU;gBACF;gBACA;YACF;YAEA,mEAAmE;YACnE,IAAIrB,4BAAAA,SAAUE,QAAQ,CAAC,cAAc;gBACnC;YACF;YAEA,MAAMqB,gBAAgBpB,wBAAwBH;YAC9CgB,qBAAqBM,IAAI,CAAC;gBACxBD;gBACA,GAAGE,aAAa;YAClB;QACF;IACF;IAEA,OAAOP;AACT"}