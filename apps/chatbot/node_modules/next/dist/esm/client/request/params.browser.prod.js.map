{"version": 3, "sources": ["../../../src/client/request/params.browser.prod.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\nimport { wellKnownProperties } from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nexport function makeUntrackedExoticParams(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n"], "names": ["wellKnownProperties", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "makeUntrackedExoticParams", "underlyingParams", "cachedParams", "get", "promise", "Promise", "resolve", "set", "Object", "keys", "for<PERSON>ach", "prop", "has"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,uCAAsC;AAG1E,MAAMC,eAAe,IAAIC;AAEzB,OAAO,SAASC,0BACdC,gBAAwB;IAExB,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAChCH,aAAaS,GAAG,CAACN,kBAAkBG;IAEnCI,OAAOC,IAAI,CAACR,kBAAkBS,OAAO,CAAC,CAACC;QACrC,IAAId,oBAAoBe,GAAG,CAACD,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHP,OAAe,CAACO,KAAK,GAAGV,gBAAgB,CAACU,KAAK;QAClD;IACF;IAEA,OAAOP;AACT"}