{"version": 3, "sources": ["../../src/client/use-merged-ref.ts"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useCallback", "useRef", "useMergedRef", "refA", "refB", "cleanupA", "cleanupB", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup"], "mappings": "AAAA,SAASA,WAAW,EAAEC,MAAM,QAAkB,QAAO;AAErD,gEAAgE;AAChE,uDAAuD;AACvD,yDAAyD;AACzD,oGAAoG;AACpG,iEAAiE;AACjE,uEAAuE;AACvE,8EAA8E;AAC9E,OAAO,SAASC,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWJ,OAA4B;IAC7C,MAAMK,WAAWL,OAA4B;IAE7C,mFAAmF;IACnF,yEAAyE;IACzE,iGAAiG;IACjG,8FAA8F;IAC9F,gDAAgD;IAChD,mGAAmG;IACnG,wFAAwF;IACxF,OAAOD,YACL,CAACO;QACC,IAAIA,YAAY,MAAM;YACpB,MAAMC,aAAaH,SAASE,OAAO;YACnC,IAAIC,YAAY;gBACdH,SAASE,OAAO,GAAG;gBACnBC;YACF;YACA,MAAMC,aAAaH,SAASC,OAAO;YACnC,IAAIE,YAAY;gBACdH,SAASC,OAAO,GAAG;gBACnBE;YACF;QACF,OAAO;YACL,IAAIN,MAAM;gBACRE,SAASE,OAAO,GAAGG,SAASP,MAAMI;YACpC;YACA,IAAIH,MAAM;gBACRE,SAASC,OAAO,GAAGG,SAASN,MAAMG;YACpC;QACF;IACF,GACA;QAACJ;QAAMC;KAAK;AAEhB;AAEA,SAASM,SACPP,IAAgC,EAChCI,OAAiB;IAEjB,IAAI,OAAOJ,SAAS,YAAY;QAC9B,MAAMQ,UAAUR,KAAKI;QACrB,IAAI,OAAOI,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMR,KAAK;QACpB;IACF,OAAO;QACLA,KAAKI,OAAO,GAAGA;QACf,OAAO;YACLJ,KAAKI,OAAO,GAAG;QACjB;IACF;AACF"}