{"version": 3, "sources": ["../../../src/export/helpers/get-params.ts"], "sourcesContent": ["import {\n  type RouteMatchFn,\n  getRouteMatcher,\n} from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\n\n// The last page and matcher that this function handled.\nlet last: {\n  page: string\n  matcher: RouteMatchFn\n} | null = null\n\n/**\n * Gets the params for the provided page.\n * @param page the page that contains dynamic path parameters\n * @param pathname the pathname to match\n * @returns the matches that were found, throws otherwise\n */\nexport function getParams(page: string, pathname: string) {\n  // Because this is often called on the output of `getStaticPaths` or similar\n  // where the `page` here doesn't change, this will \"remember\" the last page\n  // it created the RegExp for. If it matches, it'll just re-use it.\n  let matcher: RouteMatchFn\n  if (last?.page === page) {\n    matcher = last.matcher\n  } else {\n    matcher = getRouteMatcher(getRouteRegex(page))\n  }\n\n  const params = matcher(pathname)\n  if (!params) {\n    throw new Error(\n      `The provided export path '${pathname}' doesn't match the '${page}' page.\\nRead more: https://nextjs.org/docs/messages/export-path-mismatch`\n    )\n  }\n\n  return params\n}\n"], "names": ["getRouteMatcher", "getRouteRegex", "last", "getParams", "page", "pathname", "matcher", "params", "Error"], "mappings": "AAAA,SAEEA,eAAe,QACV,8CAA6C;AACpD,SAASC,aAAa,QAAQ,4CAA2C;AAEzE,wDAAwD;AACxD,IAAIC,OAGO;AAEX;;;;;CAKC,GACD,OAAO,SAASC,UAAUC,IAAY,EAAEC,QAAgB;IACtD,4EAA4E;IAC5E,2EAA2E;IAC3E,kEAAkE;IAClE,IAAIC;IACJ,IAAIJ,CAAAA,wBAAAA,KAAME,IAAI,MAAKA,MAAM;QACvBE,UAAUJ,KAAKI,OAAO;IACxB,OAAO;QACLA,UAAUN,gBAAgBC,cAAcG;IAC1C;IAEA,MAAMG,SAASD,QAAQD;IACvB,IAAI,CAACE,QAAQ;QACX,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,0BAA0B,EAAEH,SAAS,qBAAqB,EAAED,KAAK,yEAAyE,CAAC,GADxI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOG;AACT"}