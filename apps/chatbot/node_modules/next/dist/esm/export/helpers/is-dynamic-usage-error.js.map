{"version": 3, "sources": ["../../../src/export/helpers/is-dynamic-usage-error.ts"], "sourcesContent": ["import { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\n\nexport const isDynamicUsageError = (err: unknown) =>\n  isDynamicServerError(err) ||\n  isBailoutToCSRError(err) ||\n  isNextRouterError(err) ||\n  isDynamicPostpone(err)\n"], "names": ["isDynamicServerError", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone", "isDynamicUsageError", "err"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,+CAA8C;AACnF,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,iBAAiB,QAAQ,4CAA2C;AAE7E,OAAO,MAAMC,sBAAsB,CAACC,MAClCL,qBAAqBK,QACrBJ,oBAAoBI,QACpBH,kBAAkBG,QAClBF,kBAAkBE,KAAI"}