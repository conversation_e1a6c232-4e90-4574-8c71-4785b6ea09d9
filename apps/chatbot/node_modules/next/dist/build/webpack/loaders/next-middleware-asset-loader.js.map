{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-middleware-asset-loader.ts"], "sourcesContent": ["import loaderUtils from 'next/dist/compiled/loader-utils3'\nimport { getModuleBuildInfo } from './get-module-build-info'\n\nexport default function MiddlewareAssetLoader(this: any, source: Buffer) {\n  const name = loaderUtils.interpolateName(this, '[name].[hash].[ext]', {\n    context: this.rootContext,\n    content: source,\n  })\n  const filePath = `edge-chunks/asset_${name}`\n  const buildInfo = getModuleBuildInfo(this._module)\n  buildInfo.nextAssetMiddlewareBinding = {\n    filePath: `server/${filePath}`,\n    name,\n  }\n  this.emitFile(filePath, source)\n  return `module.exports = ${JSON.stringify(`blob:${name}`)}`\n}\n\nexport const raw = true\n"], "names": ["MiddlewareAssetLoader", "raw", "source", "name", "loaderUtils", "interpolateName", "context", "rootContext", "content", "filePath", "buildInfo", "getModuleBuildInfo", "_module", "nextAssetMiddlewareBinding", "emitFile", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;IAGA,OAaC;eAbuBA;;IAeXC,GAAG;eAAHA;;;qEAlBW;oCACW;;;;;;AAEpB,SAASD,sBAAiCE,MAAc;IACrE,MAAMC,OAAOC,qBAAW,CAACC,eAAe,CAAC,IAAI,EAAE,uBAAuB;QACpEC,SAAS,IAAI,CAACC,WAAW;QACzBC,SAASN;IACX;IACA,MAAMO,WAAW,CAAC,kBAAkB,EAAEN,MAAM;IAC5C,MAAMO,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjDF,UAAUG,0BAA0B,GAAG;QACrCJ,UAAU,CAAC,OAAO,EAAEA,UAAU;QAC9BN;IACF;IACA,IAAI,CAACW,QAAQ,CAACL,UAAUP;IACxB,OAAO,CAAC,iBAAiB,EAAEa,KAAKC,SAAS,CAAC,CAAC,KAAK,EAAEb,MAAM,GAAG;AAC7D;AAEO,MAAMF,MAAM"}