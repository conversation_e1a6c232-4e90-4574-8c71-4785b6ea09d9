{"version": 3, "sources": ["../../../src/build/babel/preset.ts"], "sourcesContent": ["import type { PluginItem } from 'next/dist/compiled/babel/core'\nimport { dirname } from 'path'\n\nconst isLoadIntentTest = process.env.NODE_ENV === 'test'\nconst isLoadIntentDevelopment = process.env.NODE_ENV === 'development'\n\ntype StyledJsxPlugin = [string, any] | string\ntype StyledJsxBabelOptions =\n  | {\n      plugins?: StyledJsxPlugin[]\n      styleModule?: string\n      'babel-test'?: boolean\n    }\n  | undefined\n\n// Resolve styled-jsx plugins\nfunction styledJsxOptions(options: StyledJsxBabelOptions) {\n  options = options || {}\n  options.styleModule = 'styled-jsx/style'\n\n  if (!Array.isArray(options.plugins)) {\n    return options\n  }\n\n  options.plugins = options.plugins.map(\n    (plugin: StyledJsxPlugin): StyledJsxPlugin => {\n      if (Array.isArray(plugin)) {\n        const [name, pluginOptions] = plugin\n        return [require.resolve(name), pluginOptions]\n      }\n\n      return require.resolve(plugin)\n    }\n  )\n\n  return options\n}\n\ntype NextBabelPresetOptions = {\n  'preset-env'?: any\n  'preset-react'?: any\n  'class-properties'?: any\n  'transform-runtime'?: any\n  'styled-jsx'?: StyledJsxBabelOptions\n  'preset-typescript'?: any\n}\n\ntype BabelPreset = {\n  presets?: PluginItem[] | null\n  plugins?: PluginItem[] | null\n  sourceType?: 'script' | 'module' | 'unambiguous'\n  overrides?: Array<{ test: RegExp } & Omit<BabelPreset, 'overrides'>>\n}\n\n// Taken from https://github.com/babel/babel/commit/d60c5e1736543a6eac4b549553e107a9ba967051#diff-b4beead8ad9195361b4537601cc22532R158\nfunction supportsStaticESM(caller: any): boolean {\n  return !!caller?.supportsStaticESM\n}\n\nexport default (\n  api: any,\n  options: NextBabelPresetOptions = {}\n): BabelPreset => {\n  const supportsESM = api.caller(supportsStaticESM)\n  const isServer = api.caller((caller: any) => !!caller && caller.isServer)\n  const isCallerDevelopment = api.caller((caller: any) => caller?.isDev)\n\n  // Look at external intent if used without a caller (e.g. via Jest):\n  const isTest = isCallerDevelopment == null && isLoadIntentTest\n\n  // Look at external intent if used without a caller (e.g. Storybook):\n  const isDevelopment =\n    isCallerDevelopment === true ||\n    (isCallerDevelopment == null && isLoadIntentDevelopment)\n\n  // Default to production mode if not `test` nor `development`:\n  const isProduction = !(isTest || isDevelopment)\n\n  const isBabelLoader = api.caller(\n    (caller: any) =>\n      !!caller &&\n      (caller.name === 'babel-loader' ||\n        caller.name === 'next-babel-turbo-loader')\n  )\n\n  const useJsxRuntime =\n    options['preset-react']?.runtime === 'automatic' ||\n    (Boolean(api.caller((caller: any) => !!caller && caller.hasJsxRuntime)) &&\n      options['preset-react']?.runtime !== 'classic')\n\n  const presetEnvConfig = {\n    // In the test environment `modules` is often needed to be set to true, babel figures that out by itself using the `'auto'` option\n    // In production/development this option is set to `false` so that webpack can handle import/export with tree-shaking\n    modules: 'auto',\n    exclude: ['transform-typeof-symbol'],\n    ...options['preset-env'],\n  }\n\n  // When transpiling for the server or tests, target the current Node version\n  // if not explicitly specified:\n  if (\n    (isServer || isTest) &&\n    (!presetEnvConfig.targets ||\n      !(\n        typeof presetEnvConfig.targets === 'object' &&\n        'node' in presetEnvConfig.targets\n      ))\n  ) {\n    presetEnvConfig.targets = {\n      // Targets the current process' version of Node. This requires apps be\n      // built and deployed on the same version of Node.\n      // This is the same as using \"current\" but explicit\n      node: process.versions.node,\n    }\n  }\n\n  return {\n    sourceType: 'unambiguous',\n    presets: [\n      [require('next/dist/compiled/babel/preset-env'), presetEnvConfig],\n      [\n        require('next/dist/compiled/babel/preset-react'),\n        {\n          // This adds @babel/plugin-transform-react-jsx-source and\n          // @babel/plugin-transform-react-jsx-self automatically in development\n          development: isDevelopment || isTest,\n          ...(useJsxRuntime ? { runtime: 'automatic' } : { pragma: '__jsx' }),\n          ...options['preset-react'],\n        },\n      ],\n      [\n        require('next/dist/compiled/babel/preset-typescript'),\n        { allowNamespaces: true, ...options['preset-typescript'] },\n      ],\n    ],\n    plugins: [\n      !useJsxRuntime && [\n        require('./plugins/jsx-pragma'),\n        {\n          // This produces the following injected import for modules containing JSX:\n          //   import React from 'react';\n          //   var __jsx = React.createElement;\n          module: 'react',\n          importAs: 'React',\n          pragma: '__jsx',\n          property: 'createElement',\n        },\n      ],\n      [\n        require('./plugins/optimize-hook-destructuring'),\n        {\n          // only optimize hook functions imported from React/Preact\n          lib: true,\n        },\n      ],\n      require('next/dist/compiled/babel/plugin-syntax-dynamic-import'),\n      [\n        require('next/dist/compiled/babel/plugin-syntax-import-attributes'),\n        {\n          deprecatedAssertSyntax: true,\n        },\n      ],\n      require('./plugins/react-loadable-plugin'),\n      // only enable this plugin if custom config for it was provided\n      // otherwise we will only enable it if their browserslist triggers\n      // preset-env to pull it in\n      options['class-properties'] && [\n        require('next/dist/compiled/babel/plugin-proposal-class-properties'),\n        options['class-properties'] || {},\n      ],\n      [\n        require('next/dist/compiled/babel/plugin-proposal-object-rest-spread'),\n        {\n          useBuiltIns: true,\n        },\n      ],\n      !isServer && [\n        require('next/dist/compiled/babel/plugin-transform-runtime'),\n        {\n          corejs: false,\n          helpers: true,\n          regenerator: true,\n          useESModules: supportsESM && presetEnvConfig.modules !== 'commonjs',\n          absoluteRuntime: isBabelLoader\n            ? dirname(\n                require.resolve(\n                  'next/dist/compiled/@babel/runtime/package.json'\n                )\n              )\n            : undefined,\n          ...options['transform-runtime'],\n        },\n      ],\n      [\n        isTest && options['styled-jsx'] && options['styled-jsx']['babel-test']\n          ? require('styled-jsx/babel-test')\n          : require('styled-jsx/babel'),\n        styledJsxOptions(options['styled-jsx']),\n      ],\n      require('./plugins/amp-attributes'),\n      isProduction && [\n        require('next/dist/compiled/babel/plugin-transform-react-remove-prop-types'),\n        {\n          removeImport: true,\n        },\n      ],\n      isServer && require('next/dist/compiled/babel/plugin-syntax-bigint'),\n      // Always compile numeric separator because the resulting number is\n      // smaller.\n      require('next/dist/compiled/babel/plugin-proposal-numeric-separator'),\n      require('next/dist/compiled/babel/plugin-proposal-export-namespace-from'),\n    ].filter(Boolean),\n  }\n}\n"], "names": ["isLoadIntentTest", "process", "env", "NODE_ENV", "isLoadIntentDevelopment", "styledJsxOptions", "options", "styleModule", "Array", "isArray", "plugins", "map", "plugin", "name", "pluginOptions", "require", "resolve", "supportsStaticESM", "caller", "api", "supportsESM", "isServer", "isCallerDevelopment", "isDev", "isTest", "isDevelopment", "isProduction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useJsxRuntime", "runtime", "Boolean", "hasJsxRuntime", "presetEnvConfig", "modules", "exclude", "targets", "node", "versions", "sourceType", "presets", "development", "pragma", "allowNamespaces", "module", "importAs", "property", "lib", "deprecatedAssertSyntax", "useBuiltIns", "corejs", "helpers", "regenerator", "useESModules", "absoluteRuntime", "dirname", "undefined", "removeImport", "filter"], "mappings": ";;;;+BA2DA;;;eAAA;;;sBA1DwB;AAExB,MAAMA,mBAAmBC,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAClD,MAAMC,0BAA0BH,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAWzD,6BAA6B;AAC7B,SAASE,iBAAiBC,OAA8B;IACtDA,UAAUA,WAAW,CAAC;IACtBA,QAAQC,WAAW,GAAG;IAEtB,IAAI,CAACC,MAAMC,OAAO,CAACH,QAAQI,OAAO,GAAG;QACnC,OAAOJ;IACT;IAEAA,QAAQI,OAAO,GAAGJ,QAAQI,OAAO,CAACC,GAAG,CACnC,CAACC;QACC,IAAIJ,MAAMC,OAAO,CAACG,SAAS;YACzB,MAAM,CAACC,MAAMC,cAAc,GAAGF;YAC9B,OAAO;gBAACG,QAAQC,OAAO,CAACH;gBAAOC;aAAc;QAC/C;QAEA,OAAOC,QAAQC,OAAO,CAACJ;IACzB;IAGF,OAAON;AACT;AAkBA,sIAAsI;AACtI,SAASW,kBAAkBC,MAAW;IACpC,OAAO,CAAC,EAACA,0BAAAA,OAAQD,iBAAiB;AACpC;MAEA,WAAe,CACbE,KACAb,UAAkC,CAAC,CAAC;QAyBlCA,sBAEEA;IAzBJ,MAAMc,cAAcD,IAAID,MAAM,CAACD;IAC/B,MAAMI,WAAWF,IAAID,MAAM,CAAC,CAACA,SAAgB,CAAC,CAACA,UAAUA,OAAOG,QAAQ;IACxE,MAAMC,sBAAsBH,IAAID,MAAM,CAAC,CAACA,SAAgBA,0BAAAA,OAAQK,KAAK;IAErE,oEAAoE;IACpE,MAAMC,SAASF,uBAAuB,QAAQtB;IAE9C,qEAAqE;IACrE,MAAMyB,gBACJH,wBAAwB,QACvBA,uBAAuB,QAAQlB;IAElC,8DAA8D;IAC9D,MAAMsB,eAAe,CAAEF,CAAAA,UAAUC,aAAY;IAE7C,MAAME,gBAAgBR,IAAID,MAAM,CAC9B,CAACA,SACC,CAAC,CAACA,UACDA,CAAAA,OAAOL,IAAI,KAAK,kBACfK,OAAOL,IAAI,KAAK,yBAAwB;IAG9C,MAAMe,gBACJtB,EAAAA,uBAAAA,OAAO,CAAC,eAAe,qBAAvBA,qBAAyBuB,OAAO,MAAK,eACpCC,QAAQX,IAAID,MAAM,CAAC,CAACA,SAAgB,CAAC,CAACA,UAAUA,OAAOa,aAAa,MACnEzB,EAAAA,wBAAAA,OAAO,CAAC,eAAe,qBAAvBA,sBAAyBuB,OAAO,MAAK;IAEzC,MAAMG,kBAAkB;QACtB,kIAAkI;QAClI,qHAAqH;QACrHC,SAAS;QACTC,SAAS;YAAC;SAA0B;QACpC,GAAG5B,OAAO,CAAC,aAAa;IAC1B;IAEA,4EAA4E;IAC5E,+BAA+B;IAC/B,IACE,AAACe,CAAAA,YAAYG,MAAK,KACjB,CAAA,CAACQ,gBAAgBG,OAAO,IACvB,CACE,CAAA,OAAOH,gBAAgBG,OAAO,KAAK,YACnC,UAAUH,gBAAgBG,OAAO,AAAD,CAClC,GACF;QACAH,gBAAgBG,OAAO,GAAG;YACxB,sEAAsE;YACtE,kDAAkD;YAClD,mDAAmD;YACnDC,MAAMnC,QAAQoC,QAAQ,CAACD,IAAI;QAC7B;IACF;IAEA,OAAO;QACLE,YAAY;QACZC,SAAS;YACP;gBAACxB,QAAQ;gBAAwCiB;aAAgB;YACjE;gBACEjB,QAAQ;gBACR;oBACE,yDAAyD;oBACzD,sEAAsE;oBACtEyB,aAAaf,iBAAiBD;oBAC9B,GAAII,gBAAgB;wBAAEC,SAAS;oBAAY,IAAI;wBAAEY,QAAQ;oBAAQ,CAAC;oBAClE,GAAGnC,OAAO,CAAC,eAAe;gBAC5B;aACD;YACD;gBACES,QAAQ;gBACR;oBAAE2B,iBAAiB;oBAAM,GAAGpC,OAAO,CAAC,oBAAoB;gBAAC;aAC1D;SACF;QACDI,SAAS;YACP,CAACkB,iBAAiB;gBAChBb,QAAQ;gBACR;oBACE,0EAA0E;oBAC1E,+BAA+B;oBAC/B,qCAAqC;oBACrC4B,QAAQ;oBACRC,UAAU;oBACVH,QAAQ;oBACRI,UAAU;gBACZ;aACD;YACD;gBACE9B,QAAQ;gBACR;oBACE,0DAA0D;oBAC1D+B,KAAK;gBACP;aACD;YACD/B,QAAQ;YACR;gBACEA,QAAQ;gBACR;oBACEgC,wBAAwB;gBAC1B;aACD;YACDhC,QAAQ;YACR,+DAA+D;YAC/D,kEAAkE;YAClE,2BAA2B;YAC3BT,OAAO,CAAC,mBAAmB,IAAI;gBAC7BS,QAAQ;gBACRT,OAAO,CAAC,mBAAmB,IAAI,CAAC;aACjC;YACD;gBACES,QAAQ;gBACR;oBACEiC,aAAa;gBACf;aACD;YACD,CAAC3B,YAAY;gBACXN,QAAQ;gBACR;oBACEkC,QAAQ;oBACRC,SAAS;oBACTC,aAAa;oBACbC,cAAchC,eAAeY,gBAAgBC,OAAO,KAAK;oBACzDoB,iBAAiB1B,gBACb2B,IAAAA,aAAO,EACLvC,QAAQC,OAAO,CACb,qDAGJuC;oBACJ,GAAGjD,OAAO,CAAC,oBAAoB;gBACjC;aACD;YACD;gBACEkB,UAAUlB,OAAO,CAAC,aAAa,IAAIA,OAAO,CAAC,aAAa,CAAC,aAAa,GAClES,QAAQ,2BACRA,QAAQ;gBACZV,iBAAiBC,OAAO,CAAC,aAAa;aACvC;YACDS,QAAQ;YACRW,gBAAgB;gBACdX,QAAQ;gBACR;oBACEyC,cAAc;gBAChB;aACD;YACDnC,YAAYN,QAAQ;YACpB,mEAAmE;YACnE,WAAW;YACXA,QAAQ;YACRA,QAAQ;SACT,CAAC0C,MAAM,CAAC3B;IACX;AACF"}