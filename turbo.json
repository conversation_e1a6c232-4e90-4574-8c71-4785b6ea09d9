{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "db:push": {"cache": false, "persistent": true}, "db:studio": {"cache": false, "persistent": true}, "db:migrate": {"cache": false, "persistent": true}, "db:generate": {"cache": false, "persistent": true}}}