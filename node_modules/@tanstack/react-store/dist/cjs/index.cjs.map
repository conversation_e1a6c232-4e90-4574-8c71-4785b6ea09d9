{"version": 3, "file": "index.cjs", "sources": ["../../src/index.ts"], "sourcesContent": ["import { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\nimport type { Derived, Store } from '@tanstack/store'\n\nexport * from '@tanstack/store'\n\n/**\n * @private\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): TSelected\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Derived<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): TSelected\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any> | Derived<TState, any>,\n  selector: (state: NoInfer<TState>) => TSelected = (d) => d as any,\n): TSelected {\n  const slice = useSyncExternalStoreWithSelector(\n    store.subscribe,\n    () => store.state,\n    () => store.state,\n    selector,\n    shallow,\n  )\n\n  return slice\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false\n    for (const [k, v] of objA) {\n      if (!objB.has(k) || !Object.is(v, objB.get(k))) return false\n    }\n    return true\n  }\n\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false\n    for (const v of objA) {\n      if (!objB.has(v)) return false\n    }\n    return true\n  }\n\n  if (objA instanceof Date && objB instanceof Date) {\n    if (objA.getTime() !== objB.getTime()) return false\n    return true\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (let i = 0; i < keysA.length; i++) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i] as string) ||\n      !Object.is(objA[keysA[i] as keyof T], objB[keysA[i] as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n"], "names": ["store", "useSyncExternalStoreWithSelector"], "mappings": ";;;;AAkBO,SAAS,SACdA,QACA,WAAkD,CAAC,MAAM,GAC9C;AACX,QAAM,QAAQC,gBAAAA;AAAAA,IACZD,OAAM;AAAA,IACN,MAAMA,OAAM;AAAA,IACZ,MAAMA,OAAM;AAAA,IACZ;AAAA,IACA;AAAA,EAAA;AAGF,SAAO;AACT;AAEO,SAAS,QAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EAAA;AAGT,MACE,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACA,WAAO;AAAA,EAAA;AAGT,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,CAAC,GAAG,CAAC,KAAK,MAAM;AACzB,UAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,EAAG,QAAO;AAAA,IAAA;AAEzD,WAAO;AAAA,EAAA;AAGT,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,KAAK,MAAM;AACpB,UAAI,CAAC,KAAK,IAAI,CAAC,EAAG,QAAO;AAAA,IAAA;AAE3B,WAAO;AAAA,EAAA;AAGT,MAAI,gBAAgB,QAAQ,gBAAgB,MAAM;AAChD,QAAI,KAAK,QAAA,MAAc,KAAK,QAAA,EAAW,QAAO;AAC9C,WAAO;AAAA,EAAA;AAGT,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EAAA;AAGT,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QACE,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAW,KAC9D,CAAC,OAAO,GAAG,KAAK,MAAM,CAAC,CAAY,GAAG,KAAK,MAAM,CAAC,CAAY,CAAC,GAC/D;AACA,aAAO;AAAA,IAAA;AAAA,EACT;AAEF,SAAO;AACT;;;;;;;;;"}