{"name": "@tanstack/form-core", "version": "1.14.0", "description": "Powerful, type-safe, framework agnostic forms.", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/form.git", "directory": "packages/form-core"}, "homepage": "https://tanstack.com/form", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "dependencies": {"@tanstack/store": "^0.7.2"}, "devDependencies": {"arktype": "^2.1.20", "valibot": "^1.1.0", "zod": "^3.25.64"}, "scripts": {}}