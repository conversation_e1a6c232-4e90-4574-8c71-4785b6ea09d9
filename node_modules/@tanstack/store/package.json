{"name": "@tanstack/store", "version": "0.7.2", "description": "Framework agnostic type-safe store w/ reactive framework adapters", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/TanStack/store.git", "directory": "packages/store"}, "homepage": "https://tanstack.com/store", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["store", "typescript"], "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "devDependencies": {"@angular/core": "^19.2.14", "@preact/signals": "^1.3.2", "solid-js": "^1.9.7", "vue": "^3.5.17"}, "scripts": {"clean": "premove ./dist ./coverage", "test:eslint": "eslint ./src ./tests", "test:types": "pnpm run \"/^test:types:ts[0-9]{2}$/\"", "test:types:ts50": "node ../../node_modules/typescript50/lib/tsc.js", "test:types:ts51": "node ../../node_modules/typescript51/lib/tsc.js", "test:types:ts52": "node ../../node_modules/typescript52/lib/tsc.js", "test:types:ts53": "node ../../node_modules/typescript53/lib/tsc.js", "test:types:ts54": "tsc", "test:lib": "vitest", "test:bench": "vitest bench", "test:lib:dev": "pnpm run test:lib --watch", "test:build": "publint --strict", "build": "vite build"}}