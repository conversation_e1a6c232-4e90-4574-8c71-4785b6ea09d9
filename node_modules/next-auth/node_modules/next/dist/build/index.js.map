{"version": 3, "sources": ["../../src/build/index.ts"], "sourcesContent": ["import type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from './webpack/plugins/pages-manifest-plugin'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { MiddlewareManifest } from './webpack/plugins/middleware-plugin'\nimport type { ActionManifest } from './webpack/plugins/flight-client-entry-plugin'\nimport type { CacheControl, Revalidate } from '../server/lib/cache-control'\n\nimport '../lib/setup-exception-listeners'\n\nimport { loadEnvConfig, type LoadedEnvFiles } from '@next/env'\nimport { bold, yellow } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { makeRe } from 'next/dist/compiled/picomatch'\nimport { existsSync, promises as fs } from 'fs'\nimport os from 'os'\nimport { Worker } from '../lib/worker'\nimport { defaultConfig } from '../server/config-shared'\nimport devalue from 'next/dist/compiled/devalue'\nimport findUp from 'next/dist/compiled/find-up'\nimport { nanoid } from 'next/dist/compiled/nanoid/index.cjs'\nimport path from 'path'\nimport {\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  PAGES_DIR_ALIAS,\n  INSTRUMENTATION_HOOK_FILENAME,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  NEXT_RESUME_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  MATCHED_PATH_HEADER,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../lib/constants'\nimport { FileType, fileExists } from '../lib/file-exists'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport loadCustomRoutes, {\n  normalizeRouteRegex,\n} from '../lib/load-custom-routes'\nimport type {\n  CustomRoutes,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteHas,\n} from '../lib/load-custom-routes'\nimport { nonNullable } from '../lib/non-nullable'\nimport { recursiveDelete } from '../lib/recursive-delete'\nimport { verifyPartytownSetup } from '../lib/verify-partytown-setup'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  IMAGES_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_FILES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  MIDDLEWARE_MANIFEST,\n  APP_PATHS_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  APP_BUILD_MANIFEST,\n  RSC_MODULE_TYPES,\n  NEXT_FONT_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  DYNAMIC_CSS_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\nimport {\n  getSortedRoutes,\n  isDynamicRoute,\n  getSortedRouteObjects,\n} from '../shared/lib/router/utils'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport loadConfig from '../server/config'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getPagePath } from '../server/require'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n  writeTurborepoAccessTraceResult,\n} from './turborepo-access-trace'\n\nimport {\n  eventBuildOptimize,\n  eventCliSession,\n  eventBuildFeatureUsage,\n  eventNextPlugins,\n  EVENT_BUILD_FEATURE_USAGE,\n  eventPackageUsedInGetServerSideProps,\n  eventBuildCompleted,\n  eventBuildFailed,\n} from '../telemetry/events'\nimport type { EventBuildFeatureUsage } from '../telemetry/events'\nimport { Telemetry } from '../telemetry/storage'\nimport { hadUnsupportedValue } from './analysis/get-page-static-info'\nimport {\n  createPagesMapping,\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from './entries'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { generateBuildId } from './generate-build-id'\nimport { isWriteable } from './is-writeable'\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport { trace, flushAllTraces, setGlobal, type Span } from '../trace'\nimport {\n  detectConflictingPaths,\n  computeFromManifest,\n  getJsPageSizeInKb,\n  printCustomRoutes,\n  printTreeView,\n  copyTracedFiles,\n  isReservedPage,\n  isAppBuiltinNotFoundPage,\n  collectRoutesUsingEdgeRuntime,\n  collectMeta,\n} from './utils'\nimport type { PageInfo, PageInfos } from './utils'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport { writeBuildId } from './write-build-id'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport isError from '../lib/is-error'\nimport type { NextError } from '../lib/is-error'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport { lockfilePatchPromise, teardownTraceSubscriber } from './swc'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getFilesInDir } from '../lib/get-files-in-dir'\nimport { eventSwcPlugins } from '../telemetry/events/swc-plugins'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport {\n  ACTION_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n} from '../client/components/app-router-headers'\nimport { webpackBuild } from './webpack-build'\nimport { NextBuildContext, type MappedPages } from './build-context'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { createClientRouterFilter } from '../lib/create-client-router-filter'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { startTypeChecking } from './type-check'\nimport { generateInterceptionRoutesRewrites } from '../lib/generate-interception-routes-rewrites'\n\nimport { buildDataRoute } from '../server/lib/router-utils/build-data-route'\nimport { collectBuildTraces } from './collect-build-traces'\nimport type { BuildTraceContext } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport { formatManifest } from './manifests/formatter/format-manifest'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n  recordFetchMetrics,\n} from '../diagnostics/build-diagnostics'\nimport { getStartServerInfo, logStartInfo } from '../server/lib/app-info-log'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { traceMemoryUsage } from '../lib/memory/trace'\nimport { generateEncryptionKeyBase64 } from '../server/app-render/encryption-utils-server'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport uploadTrace from '../trace/upload-trace'\nimport {\n  checkIsAppPPREnabled,\n  checkIsRoutePPREnabled,\n} from '../server/lib/experimental/ppr'\nimport { FallbackMode, fallbackModeToFallbackField } from '../lib/fallback'\nimport { RenderingMode } from './rendering-mode'\nimport { getParamKeys } from '../server/request/fallback-params'\nimport {\n  formatNodeOptions,\n  getParsedNodeOptionsWithoutInspect,\n} from '../server/lib/utils'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport type { UseCacheTrackerKey } from './webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport {\n  buildPrefetchSegmentDataRoute,\n  type PrefetchSegmentDataRoute,\n} from '../server/lib/router-utils/build-prefetch-segment-data-route'\n\nimport { turbopackBuild } from './turbopack-build'\nimport { isPersistentCachingEnabled } from '../shared/lib/turbopack/utils'\nimport { inlineStaticEnv } from '../lib/inline-static-env'\nimport { populateStaticEnv } from '../lib/static-env'\nimport { durationToString } from './duration-to-string'\nimport { traceGlobals } from '../trace/shared'\nimport { extractNextErrorCode } from '../lib/error-telemetry-utils'\n\ntype Fallback = null | boolean | string\n\nexport interface PrerenderManifestRoute {\n  dataRoute: string | null\n  experimentalBypassFor?: RouteHas[]\n\n  /**\n   * The headers that should be served along side this prerendered route.\n   */\n  initialHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be served along side this prerendered route.\n   */\n  initialStatus?: number\n\n  /**\n   * The revalidate value for this route. This might be inferred from:\n   * - route segment configs\n   * - fetch calls\n   * - unstable_cache\n   * - \"use cache\"\n   */\n  initialRevalidateSeconds: Revalidate\n\n  /**\n   * The expire value for this route, which is inferred from the \"use cache\"\n   * functions that are used by the route, or the expireTime config.\n   */\n  initialExpireSeconds: number | undefined\n\n  /**\n   * The prefetch data route associated with this page. If not defined, this\n   * page does not support prefetching.\n   */\n  prefetchDataRoute: string | null | undefined\n\n  /**\n   * The dynamic route that this statically prerendered route is based on. If\n   * this is null, then the route was not based on a dynamic route.\n   */\n  srcRoute: string | null\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\nexport interface DynamicPrerenderManifestRoute {\n  dataRoute: string | null\n  dataRouteRegex: string | null\n  experimentalBypassFor?: RouteHas[]\n  fallback: Fallback\n\n  /**\n   * When defined, it describes the revalidation configuration for the fallback\n   * route.\n   */\n  fallbackRevalidate: Revalidate | undefined\n\n  /**\n   * When defined, it describes the expire configuration for the fallback route.\n   */\n  fallbackExpire: number | undefined\n\n  /**\n   * The headers that should used when serving the fallback.\n   */\n  fallbackHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be used when serving the fallback.\n   */\n  fallbackStatus?: number\n\n  /**\n   * The root params that are unknown for this fallback route.\n   */\n  fallbackRootParams: readonly string[] | undefined\n\n  /**\n   * The source route that this fallback route is based on. This is a reference\n   * so that we can associate this dynamic route with the correct source.\n   */\n  fallbackSourceRoute: string | undefined\n\n  prefetchDataRoute: string | null | undefined\n  prefetchDataRouteRegex: string | null | undefined\n  routeRegex: string\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\n/**\n * The headers that are allowed to be used when revalidating routes. Currently\n * this includes both headers used by the pages and app routers.\n */\nconst ALLOWED_HEADERS: string[] = [\n  'host',\n  MATCHED_PATH_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n]\n\nexport type PrerenderManifest = {\n  version: 4\n  routes: { [route: string]: PrerenderManifestRoute }\n  dynamicRoutes: { [route: string]: DynamicPrerenderManifestRoute }\n  notFoundRoutes: string[]\n  preview: __ApiPreviewProps\n}\n\ntype ManifestBuiltRoute = {\n  /**\n   * The route pattern used to match requests for this route.\n   */\n  regex: string\n}\n\nexport type ManifestRewriteRoute = ManifestBuiltRoute & Rewrite\nexport type ManifestRedirectRoute = ManifestBuiltRoute & Redirect\nexport type ManifestHeaderRoute = ManifestBuiltRoute & Header\n\nexport type ManifestRoute = ManifestBuiltRoute & {\n  page: string\n  namedRegex?: string\n  routeKeys?: { [key: string]: string }\n  prefetchSegmentDataRoutes?: PrefetchSegmentDataRoute[]\n}\n\ntype ManifestDataRoute = {\n  page: string\n  routeKeys?: { [key: string]: string }\n  dataRouteRegex: string\n  namedDataRouteRegex?: string\n}\n\nexport type RoutesManifest = {\n  version: number\n  pages404: boolean\n  basePath: string\n  redirects: Array<Redirect>\n  rewrites?:\n    | Array<ManifestRewriteRoute>\n    | {\n        beforeFiles: Array<ManifestRewriteRoute>\n        afterFiles: Array<ManifestRewriteRoute>\n        fallback: Array<ManifestRewriteRoute>\n      }\n  headers: Array<ManifestHeaderRoute>\n  staticRoutes: Array<ManifestRoute>\n  dynamicRoutes: Array<ManifestRoute>\n  dataRoutes: Array<ManifestDataRoute>\n  i18n?: {\n    domains?: ReadonlyArray<{\n      http?: true\n      domain: string\n      locales?: readonly string[]\n      defaultLocale: string\n    }>\n    locales: readonly string[]\n    defaultLocale: string\n    localeDetection?: false\n  }\n  rsc: {\n    header: typeof RSC_HEADER\n    didPostponeHeader: typeof NEXT_DID_POSTPONE_HEADER\n    contentTypeHeader: typeof RSC_CONTENT_TYPE_HEADER\n    varyHeader: string\n    prefetchHeader: typeof NEXT_ROUTER_PREFETCH_HEADER\n    suffix: typeof RSC_SUFFIX\n    prefetchSuffix: typeof RSC_PREFETCH_SUFFIX\n    prefetchSegmentHeader: typeof NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n    prefetchSegmentDirSuffix: typeof RSC_SEGMENTS_DIR_SUFFIX\n    prefetchSegmentSuffix: typeof RSC_SEGMENT_SUFFIX\n  }\n  rewriteHeaders: {\n    pathHeader: typeof NEXT_REWRITTEN_PATH_HEADER\n    queryHeader: typeof NEXT_REWRITTEN_QUERY_HEADER\n  }\n  skipMiddlewareUrlNormalize?: boolean\n  caseSensitive?: boolean\n  /**\n   * Configuration related to Partial Prerendering.\n   */\n  ppr?: {\n    /**\n     * The chained response for the PPR resume.\n     */\n    chain: {\n      /**\n       * The headers that will indicate to Next.js that the request is for a PPR\n       * resume.\n       */\n      headers: Record<string, string>\n    }\n  }\n}\n\nfunction pageToRoute(page: string) {\n  const routeRegex = getNamedRouteRegex(page, {\n    prefixRouteKeys: true,\n  })\n  return {\n    page,\n    regex: normalizeRouteRegex(routeRegex.re.source),\n    routeKeys: routeRegex.routeKeys,\n    namedRegex: routeRegex.namedRegex,\n  }\n}\n\nfunction getCacheDir(distDir: string): string {\n  const cacheDir = path.join(distDir, 'cache')\n  if (ciEnvironment.isCI && !ciEnvironment.hasNextSupport) {\n    const hasCache = existsSync(cacheDir)\n\n    if (!hasCache) {\n      // Intentionally not piping to stderr which is what `Log.warn` does in case people fail in CI when\n      // stderr is detected.\n      console.log(\n        `${Log.prefixes.warn} No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache`\n      )\n    }\n  }\n  return cacheDir\n}\n\nasync function writeFileUtf8(filePath: string, content: string): Promise<void> {\n  await fs.writeFile(filePath, content, 'utf-8')\n}\n\nfunction readFileUtf8(filePath: string): Promise<string> {\n  return fs.readFile(filePath, 'utf8')\n}\n\nasync function writeManifest<T extends object>(\n  filePath: string,\n  manifest: T\n): Promise<void> {\n  await writeFileUtf8(filePath, formatManifest(manifest))\n}\n\nasync function readManifest<T extends object>(filePath: string): Promise<T> {\n  return JSON.parse(await readFileUtf8(filePath))\n}\n\nasync function writePrerenderManifest(\n  distDir: string,\n  manifest: DeepReadonly<PrerenderManifest>\n): Promise<void> {\n  await writeManifest(path.join(distDir, PRERENDER_MANIFEST), manifest)\n}\n\nasync function writeClientSsgManifest(\n  prerenderManifest: DeepReadonly<PrerenderManifest>,\n  {\n    buildId,\n    distDir,\n    locales,\n  }: {\n    buildId: string\n    distDir: string\n    locales: readonly string[] | undefined\n  }\n) {\n  const ssgPages = new Set<string>(\n    [\n      ...Object.entries(prerenderManifest.routes)\n        // Filter out dynamic routes\n        .filter(([, { srcRoute }]) => srcRoute == null)\n        .map(([route]) => normalizeLocalePath(route, locales).pathname),\n      ...Object.keys(prerenderManifest.dynamicRoutes),\n    ].sort()\n  )\n\n  const clientSsgManifestContent = `self.__SSG_MANIFEST=${devalue(\n    ssgPages\n  )};self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n  await writeFileUtf8(\n    path.join(distDir, CLIENT_STATIC_FILES_PATH, buildId, '_ssgManifest.js'),\n    clientSsgManifestContent\n  )\n}\n\nexport interface FunctionsConfigManifest {\n  version: number\n  functions: Record<\n    string,\n    {\n      maxDuration?: number | undefined\n      runtime?: 'nodejs'\n      matchers?: Array<{\n        regexp: string\n        originalSource: string\n        has?: Rewrite['has']\n        missing?: Rewrite['has']\n      }>\n    }\n  >\n}\n\nasync function writeFunctionsConfigManifest(\n  distDir: string,\n  manifest: FunctionsConfigManifest\n): Promise<void> {\n  await writeManifest(\n    path.join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n    manifest\n  )\n}\n\ninterface RequiredServerFilesManifest {\n  version: number\n  config: NextConfigComplete\n  appDir: string\n  relativeAppDir: string\n  files: string[]\n  ignore: string[]\n}\n\nasync function writeRequiredServerFilesManifest(\n  distDir: string,\n  requiredServerFiles: RequiredServerFilesManifest\n) {\n  await writeManifest(\n    path.join(distDir, SERVER_FILES_MANIFEST),\n    requiredServerFiles\n  )\n}\n\nasync function writeImagesManifest(\n  distDir: string,\n  config: NextConfigComplete\n): Promise<void> {\n  const images = { ...config.images }\n  const { deviceSizes, imageSizes } = images\n  ;(images as any).sizes = [...deviceSizes, ...imageSizes]\n\n  // By default, remotePatterns will allow no remote images ([])\n  images.remotePatterns = (config?.images?.remotePatterns || []).map((p) => ({\n    // Modifying the manifest should also modify matchRemotePattern()\n    protocol: p.protocol?.replace(/:$/, '') as 'http' | 'https' | undefined,\n    hostname: makeRe(p.hostname).source,\n    port: p.port,\n    pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n    search: p.search,\n  }))\n\n  // By default, localPatterns will allow all local images (undefined)\n  if (config?.images?.localPatterns) {\n    images.localPatterns = config.images.localPatterns.map((p) => ({\n      // Modifying the manifest should also modify matchLocalPattern()\n      pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n      search: p.search,\n    }))\n  }\n\n  await writeManifest(path.join(distDir, IMAGES_MANIFEST), {\n    version: 1,\n    images,\n  })\n}\n\nconst STANDALONE_DIRECTORY = 'standalone' as const\nasync function writeStandaloneDirectory(\n  nextBuildSpan: Span,\n  distDir: string,\n  pageKeys: { pages: string[]; app: string[] | undefined },\n  denormalizedAppPages: string[] | undefined,\n  outputFileTracingRoot: string,\n  requiredServerFiles: RequiredServerFilesManifest,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>,\n  loadedEnvFiles: LoadedEnvFiles,\n  appDir: string | undefined\n) {\n  await nextBuildSpan\n    .traceChild('write-standalone-directory')\n    .traceAsyncFn(async () => {\n      await copyTracedFiles(\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        requiredServerFiles.appDir,\n        distDir,\n        pageKeys.pages,\n        denormalizedAppPages,\n        outputFileTracingRoot,\n        requiredServerFiles.config,\n        middlewareManifest,\n        hasNodeMiddleware,\n        hasInstrumentationHook,\n        staticPages\n      )\n\n      for (const file of [\n        ...requiredServerFiles.files,\n        path.join(requiredServerFiles.config.distDir, SERVER_FILES_MANIFEST),\n        ...loadedEnvFiles.reduce<string[]>((acc, envFile) => {\n          if (['.env', '.env.production'].includes(envFile.path)) {\n            acc.push(envFile.path)\n          }\n          return acc\n        }, []),\n      ]) {\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        const filePath = path.join(requiredServerFiles.appDir, file)\n        const outputPath = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, filePath)\n        )\n        await fs.mkdir(path.dirname(outputPath), {\n          recursive: true,\n        })\n        await fs.copyFile(filePath, outputPath)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareOutput = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'middleware.js'\n        )\n\n        await fs.mkdir(path.dirname(middlewareOutput), { recursive: true })\n        await fs.copyFile(\n          path.join(distDir, SERVER_DIRECTORY, 'middleware.js'),\n          middlewareOutput\n        )\n      }\n\n      await recursiveCopy(\n        path.join(distDir, SERVER_DIRECTORY, 'pages'),\n        path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'pages'\n        ),\n        { overwrite: true }\n      )\n      if (appDir) {\n        const originalServerApp = path.join(distDir, SERVER_DIRECTORY, 'app')\n        if (existsSync(originalServerApp)) {\n          await recursiveCopy(\n            originalServerApp,\n            path.join(\n              distDir,\n              STANDALONE_DIRECTORY,\n              path.relative(outputFileTracingRoot, distDir),\n              SERVER_DIRECTORY,\n              'app'\n            ),\n            { overwrite: true }\n          )\n        }\n      }\n    })\n}\n\nfunction getNumberOfWorkers(config: NextConfigComplete) {\n  if (\n    config.experimental.cpus &&\n    config.experimental.cpus !== defaultConfig.experimental!.cpus\n  ) {\n    return config.experimental.cpus\n  }\n\n  if (config.experimental.memoryBasedWorkersCount) {\n    return Math.max(\n      Math.min(config.experimental.cpus || 1, Math.floor(os.freemem() / 1e9)),\n      // enforce a minimum of 4 workers\n      4\n    )\n  }\n\n  if (config.experimental.cpus) {\n    return config.experimental.cpus\n  }\n\n  // Fall back to 4 workers if a count is not specified\n  return 4\n}\n\nconst staticWorkerPath = require.resolve('./worker')\nconst staticWorkerExposedMethods = [\n  'hasCustomGetInitialProps',\n  'isPageStatic',\n  'getDefinedNamedExports',\n  'exportPages',\n] as const\ntype StaticWorker = typeof import('./worker') & Worker\nexport function createStaticWorker(\n  config: NextConfigComplete,\n  progress?: {\n    run: () => void\n    clear: () => void\n  }\n): StaticWorker {\n  // Get the node options without inspect and also remove the\n  // --max-old-space-size flag as it can cause memory issues.\n  const nodeOptions = getParsedNodeOptionsWithoutInspect()\n  delete nodeOptions['max-old-space-size']\n  delete nodeOptions['max_old_space_size']\n\n  return new Worker(staticWorkerPath, {\n    logger: Log,\n    numWorkers: getNumberOfWorkers(config),\n    onActivity: () => {\n      progress?.run()\n    },\n    onActivityAbort: () => {\n      progress?.clear()\n    },\n    forkOptions: {\n      env: { ...process.env, NODE_OPTIONS: formatNodeOptions(nodeOptions) },\n    },\n    enableWorkerThreads: config.experimental.workerThreads,\n    exposedMethods: staticWorkerExposedMethods,\n  }) as StaticWorker\n}\n\nasync function writeFullyStaticExport(\n  config: NextConfigComplete,\n  dir: string,\n  enabledDirectories: NextEnabledDirectories,\n  configOutDir: string,\n  nextBuildSpan: Span\n): Promise<void> {\n  const exportApp = require('../export')\n    .default as typeof import('../export').default\n\n  const pagesWorker = createStaticWorker(config)\n  const appWorker = createStaticWorker(config)\n\n  await exportApp(\n    dir,\n    {\n      buildExport: false,\n      nextConfig: config,\n      enabledDirectories,\n      silent: true,\n      outdir: path.join(dir, configOutDir),\n      numWorkers: getNumberOfWorkers(config),\n    },\n    nextBuildSpan\n  )\n\n  pagesWorker.end()\n  appWorker.end()\n}\n\nasync function getBuildId(\n  isGenerateMode: boolean,\n  distDir: string,\n  nextBuildSpan: Span,\n  config: NextConfigComplete\n) {\n  if (isGenerateMode) {\n    return await fs.readFile(path.join(distDir, 'BUILD_ID'), 'utf8')\n  }\n  return await nextBuildSpan\n    .traceChild('generate-buildid')\n    .traceAsyncFn(() => generateBuildId(config.generateBuildId, nanoid))\n}\n\nexport default async function build(\n  dir: string,\n  reactProductionProfiling = false,\n  debugOutput = false,\n  runLint = true,\n  noMangling = false,\n  appDirOnly = false,\n  isTurbopack = false,\n  experimentalBuildMode: 'default' | 'compile' | 'generate' | 'generate-env',\n  traceUploadUrl: string | undefined\n): Promise<void> {\n  const isCompileMode = experimentalBuildMode === 'compile'\n  const isGenerateMode = experimentalBuildMode === 'generate'\n  NextBuildContext.isCompileMode = isCompileMode\n  const buildStartTime = Date.now()\n\n  let loadedConfig: NextConfigComplete | undefined\n  try {\n    const nextBuildSpan = trace('next-build', undefined, {\n      buildMode: experimentalBuildMode,\n      isTurboBuild: String(isTurbopack),\n      version: process.env.__NEXT_VERSION as string,\n    })\n\n    NextBuildContext.nextBuildSpan = nextBuildSpan\n    NextBuildContext.dir = dir\n    NextBuildContext.appDirOnly = appDirOnly\n    NextBuildContext.reactProductionProfiling = reactProductionProfiling\n    NextBuildContext.noMangling = noMangling\n\n    await nextBuildSpan.traceAsyncFn(async () => {\n      // attempt to load global env values so they are available in next.config.js\n      const { loadedEnvFiles } = nextBuildSpan\n        .traceChild('load-dotenv')\n        .traceFn(() => loadEnvConfig(dir, false, Log))\n      NextBuildContext.loadedEnvFiles = loadedEnvFiles\n\n      const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n      const config: NextConfigComplete = await nextBuildSpan\n        .traceChild('load-next-config')\n        .traceAsyncFn(() =>\n          turborepoTraceAccess(\n            () =>\n              loadConfig(PHASE_PRODUCTION_BUILD, dir, {\n                // Log for next.config loading process\n                silent: false,\n                reactProductionProfiling,\n              }),\n            turborepoAccessTraceResult\n          )\n        )\n      loadedConfig = config\n\n      process.env.NEXT_DEPLOYMENT_ID = config.deploymentId || ''\n      NextBuildContext.config = config\n\n      let configOutDir = 'out'\n      if (hasCustomExportOutput(config)) {\n        configOutDir = config.distDir\n        config.distDir = '.next'\n      }\n      const distDir = path.join(dir, config.distDir)\n      NextBuildContext.distDir = distDir\n      setGlobal('phase', PHASE_PRODUCTION_BUILD)\n      setGlobal('distDir', distDir)\n\n      const buildId = await getBuildId(\n        isGenerateMode,\n        distDir,\n        nextBuildSpan,\n        config\n      )\n      NextBuildContext.buildId = buildId\n\n      if (experimentalBuildMode === 'generate-env') {\n        if (isTurbopack) {\n          Log.warn('generate-env is not needed with turbopack')\n          process.exit(0)\n        }\n        Log.info('Inlining static env ...')\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n\n        Log.info('Complete')\n        await flushAllTraces()\n        teardownTraceSubscriber()\n        process.exit(0)\n      }\n\n      // when using compile mode static env isn't inlined so we\n      // need to populate in normal runtime env\n      if (isCompileMode || isGenerateMode) {\n        populateStaticEnv(config)\n      }\n\n      const customRoutes: CustomRoutes = await nextBuildSpan\n        .traceChild('load-custom-routes')\n        .traceAsyncFn(() => loadCustomRoutes(config))\n\n      const { headers, rewrites, redirects } = customRoutes\n      const combinedRewrites: Rewrite[] = [\n        ...rewrites.beforeFiles,\n        ...rewrites.afterFiles,\n        ...rewrites.fallback,\n      ]\n      const hasRewrites = combinedRewrites.length > 0\n      NextBuildContext.hasRewrites = hasRewrites\n      NextBuildContext.originalRewrites = config._originalRewrites\n      NextBuildContext.originalRedirects = config._originalRedirects\n\n      const cacheDir = getCacheDir(distDir)\n\n      const telemetry = new Telemetry({ distDir })\n\n      setGlobal('telemetry', telemetry)\n\n      const publicDir = path.join(dir, 'public')\n      const { pagesDir, appDir } = findPagesDir(dir)\n      NextBuildContext.pagesDir = pagesDir\n      NextBuildContext.appDir = appDir\n\n      const enabledDirectories: NextEnabledDirectories = {\n        app: typeof appDir === 'string',\n        pages: typeof pagesDir === 'string',\n      }\n\n      // Generate a random encryption key for this build.\n      // This key is used to encrypt cross boundary values and can be used to generate hashes.\n      const encryptionKey = await generateEncryptionKeyBase64({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.encryptionKey = encryptionKey\n\n      const isSrcDir = path\n        .relative(dir, pagesDir || appDir || '')\n        .startsWith('src')\n      const hasPublicDir = existsSync(publicDir)\n\n      telemetry.record(\n        eventCliSession(dir, config, {\n          webpackVersion: 5,\n          cliCommand: 'build',\n          isSrcDir,\n          hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n          isCustomServer: null,\n          turboFlag: false,\n          pagesDir: !!pagesDir,\n          appDir: !!appDir,\n        })\n      )\n\n      eventNextPlugins(path.resolve(dir)).then((events) =>\n        telemetry.record(events)\n      )\n\n      eventSwcPlugins(path.resolve(dir), config).then((events) =>\n        telemetry.record(events)\n      )\n\n      // Always log next version first then start rest jobs\n      const { envInfo, experimentalFeatures } = await getStartServerInfo(\n        dir,\n        false\n      )\n      logStartInfo({\n        networkUrl: null,\n        appUrl: null,\n        envInfo,\n        experimentalFeatures,\n      })\n\n      const ignoreESLint = Boolean(config.eslint.ignoreDuringBuilds)\n      const shouldLint = !ignoreESLint && runLint\n\n      const typeCheckingOptions: Parameters<typeof startTypeChecking>[0] = {\n        dir,\n        appDir,\n        pagesDir,\n        runLint,\n        shouldLint,\n        ignoreESLint,\n        telemetry,\n        nextBuildSpan,\n        config,\n        cacheDir,\n      }\n\n      const distDirCreated = await nextBuildSpan\n        .traceChild('create-dist-dir')\n        .traceAsyncFn(async () => {\n          try {\n            await fs.mkdir(distDir, { recursive: true })\n            return true\n          } catch (err) {\n            if (isError(err) && err.code === 'EPERM') {\n              return false\n            }\n            throw err\n          }\n        })\n\n      if (!distDirCreated || !(await isWriteable(distDir))) {\n        throw new Error(\n          '> Build directory is not writeable. https://nextjs.org/docs/messages/build-dir-not-writeable'\n        )\n      }\n\n      if (config.cleanDistDir && !isGenerateMode) {\n        await recursiveDelete(distDir, /^cache/)\n      }\n\n      // For app directory, we run type checking after build. That's because\n      // we dynamically generate types for each layout and page in the app\n      // directory.\n      if (!appDir && !isCompileMode)\n        await startTypeChecking(typeCheckingOptions)\n\n      if (appDir && 'exportPathMap' in config) {\n        Log.error(\n          'The \"exportPathMap\" configuration cannot be used with the \"app\" directory. Please use generateStaticParams() instead.'\n        )\n        await telemetry.flush()\n        process.exit(1)\n      }\n\n      const buildLintEvent: EventBuildFeatureUsage = {\n        featureName: 'build-lint',\n        invocationCount: shouldLint ? 1 : 0,\n      }\n      telemetry.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: buildLintEvent,\n      })\n\n      const validFileMatcher = createValidFileMatcher(\n        config.pageExtensions,\n        appDir\n      )\n\n      const providedPagePaths: string[] = JSON.parse(\n        process.env.NEXT_PRIVATE_PAGE_PATHS || '[]'\n      )\n\n      let pagesPaths = Boolean(process.env.NEXT_PRIVATE_PAGE_PATHS)\n        ? providedPagePaths\n        : !appDirOnly && pagesDir\n          ? await nextBuildSpan.traceChild('collect-pages').traceAsyncFn(() =>\n              recursiveReadDir(pagesDir, {\n                pathnameFilter: validFileMatcher.isPageFile,\n              })\n            )\n          : []\n\n      const middlewareDetectionRegExp = new RegExp(\n        `^${MIDDLEWARE_FILENAME}\\\\.(?:${config.pageExtensions.join('|')})$`\n      )\n\n      const instrumentationHookDetectionRegExp = new RegExp(\n        `^${INSTRUMENTATION_HOOK_FILENAME}\\\\.(?:${config.pageExtensions.join(\n          '|'\n        )})$`\n      )\n\n      const rootDir = path.join((pagesDir || appDir)!, '..')\n      const includes = [\n        middlewareDetectionRegExp,\n        instrumentationHookDetectionRegExp,\n      ]\n\n      const rootPaths = Array.from(await getFilesInDir(rootDir))\n        .filter((file) => includes.some((include) => include.test(file)))\n        .sort(sortByPageExts(config.pageExtensions))\n        .map((file) => path.join(rootDir, file).replace(dir, ''))\n\n      const hasInstrumentationHook = rootPaths.some((p) =>\n        p.includes(INSTRUMENTATION_HOOK_FILENAME)\n      )\n      const hasMiddlewareFile = rootPaths.some((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n\n      NextBuildContext.hasInstrumentationHook = hasInstrumentationHook\n\n      const previewProps: __ApiPreviewProps = {\n        previewModeId: crypto.randomBytes(16).toString('hex'),\n        previewModeSigningKey: crypto.randomBytes(32).toString('hex'),\n        previewModeEncryptionKey: crypto.randomBytes(32).toString('hex'),\n      }\n      NextBuildContext.previewProps = previewProps\n\n      const mappedPages = await nextBuildSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: false,\n            pageExtensions: config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagesPaths,\n            pagesDir,\n            appDir,\n          })\n        )\n      NextBuildContext.mappedPages = mappedPages\n\n      let mappedAppPages: MappedPages | undefined\n      let denormalizedAppPages: string[] | undefined\n\n      if (appDir) {\n        const providedAppPaths: string[] = JSON.parse(\n          process.env.NEXT_PRIVATE_APP_PATHS || '[]'\n        )\n\n        let appPaths = Boolean(process.env.NEXT_PRIVATE_APP_PATHS)\n          ? providedAppPaths\n          : await nextBuildSpan\n              .traceChild('collect-app-paths')\n              .traceAsyncFn(() =>\n                recursiveReadDir(appDir, {\n                  pathnameFilter: (absolutePath) =>\n                    validFileMatcher.isAppRouterPage(absolutePath) ||\n                    // For now we only collect the root /not-found page in the app\n                    // directory as the 404 fallback\n                    validFileMatcher.isRootNotFound(absolutePath),\n                  ignorePartFilter: (part) => part.startsWith('_'),\n                })\n              )\n\n        mappedAppPages = await nextBuildSpan\n          .traceChild('create-app-mapping')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: appPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        NextBuildContext.mappedAppPages = mappedAppPages\n      }\n\n      const mappedRootPaths = await createPagesMapping({\n        isDev: false,\n        pageExtensions: config.pageExtensions,\n        pagePaths: rootPaths,\n        pagesType: PAGE_TYPES.ROOT,\n        pagesDir: pagesDir,\n        appDir,\n      })\n      NextBuildContext.mappedRootPaths = mappedRootPaths\n\n      const pagesPageKeys = Object.keys(mappedPages)\n\n      const conflictingAppPagePaths: [pagePath: string, appPath: string][] = []\n      const appPageKeys = new Set<string>()\n      if (mappedAppPages) {\n        denormalizedAppPages = Object.keys(mappedAppPages)\n        for (const appKey of denormalizedAppPages) {\n          const normalizedAppPageKey = normalizeAppPath(appKey)\n          const pagePath = mappedPages[normalizedAppPageKey]\n          if (pagePath) {\n            const appPath = mappedAppPages[appKey]\n            conflictingAppPagePaths.push([\n              pagePath.replace(/^private-next-pages/, 'pages'),\n              appPath.replace(/^private-next-app-dir/, 'app'),\n            ])\n          }\n          appPageKeys.add(normalizedAppPageKey)\n        }\n      }\n\n      const appPaths = Array.from(appPageKeys)\n      // Interception routes are modelled as beforeFiles rewrites\n      rewrites.beforeFiles.push(\n        ...generateInterceptionRoutesRewrites(appPaths, config.basePath)\n      )\n\n      NextBuildContext.rewrites = rewrites\n\n      const totalAppPagesCount = appPaths.length\n\n      const pageKeys = {\n        pages: pagesPageKeys,\n        app: appPaths.length > 0 ? appPaths : undefined,\n      }\n\n      // Turbopack already handles conflicting app and page routes.\n      if (!isTurbopack) {\n        const numConflictingAppPaths = conflictingAppPagePaths.length\n        if (mappedAppPages && numConflictingAppPaths > 0) {\n          Log.error(\n            `Conflicting app and page file${\n              numConflictingAppPaths === 1 ? ' was' : 's were'\n            } found, please remove the conflicting files to continue:`\n          )\n          for (const [pagePath, appPath] of conflictingAppPagePaths) {\n            Log.error(`  \"${pagePath}\" - \"${appPath}\"`)\n          }\n          await telemetry.flush()\n          process.exit(1)\n        }\n      }\n\n      const conflictingPublicFiles: string[] = []\n      const hasPages404 = mappedPages['/404']?.startsWith(PAGES_DIR_ALIAS)\n      const hasApp404 = !!mappedAppPages?.[UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]\n      const hasCustomErrorPage =\n        mappedPages['/_error'].startsWith(PAGES_DIR_ALIAS)\n\n      if (hasPublicDir) {\n        const hasPublicUnderScoreNextDir = existsSync(\n          path.join(publicDir, '_next')\n        )\n        if (hasPublicUnderScoreNextDir) {\n          throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n        }\n      }\n\n      await nextBuildSpan\n        .traceChild('public-dir-conflict-check')\n        .traceAsyncFn(async () => {\n          // Check if pages conflict with files in `public`\n          // Only a page of public file can be served, not both.\n          for (const page in mappedPages) {\n            const hasPublicPageFile = await fileExists(\n              path.join(publicDir, page === '/' ? '/index' : page),\n              FileType.File\n            )\n            if (hasPublicPageFile) {\n              conflictingPublicFiles.push(page)\n            }\n          }\n\n          const numConflicting = conflictingPublicFiles.length\n\n          if (numConflicting) {\n            throw new Error(\n              `Conflicting public and page file${\n                numConflicting === 1 ? ' was' : 's were'\n              } found. https://nextjs.org/docs/messages/conflicting-public-file-page\\n${conflictingPublicFiles.join(\n                '\\n'\n              )}`\n            )\n          }\n        })\n\n      const nestedReservedPages = pageKeys.pages.filter((page) => {\n        return (\n          page.match(/\\/(_app|_document|_error)$/) && path.dirname(page) !== '/'\n        )\n      })\n\n      if (nestedReservedPages.length) {\n        Log.warn(\n          `The following reserved Next.js pages were detected not directly under the pages directory:\\n` +\n            nestedReservedPages.join('\\n') +\n            `\\nSee more info here: https://nextjs.org/docs/messages/nested-reserved-page\\n`\n        )\n      }\n\n      const restrictedRedirectPaths = ['/_next'].map((p) =>\n        config.basePath ? `${config.basePath}${p}` : p\n      )\n\n      const isAppDynamicIOEnabled = Boolean(config.experimental.dynamicIO)\n      const isAuthInterruptsEnabled = Boolean(\n        config.experimental.authInterrupts\n      )\n      const isAppPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n\n      const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n      const routesManifest: RoutesManifest = nextBuildSpan\n        .traceChild('generate-routes-manifest')\n        .traceFn(() => {\n          const sortedRoutes = getSortedRoutes([\n            ...pageKeys.pages,\n            ...(pageKeys.app ?? []),\n          ])\n          const dynamicRoutes: Array<ReturnType<typeof pageToRoute>> = []\n          const staticRoutes: typeof dynamicRoutes = []\n\n          for (const route of sortedRoutes) {\n            if (isDynamicRoute(route)) {\n              dynamicRoutes.push(pageToRoute(route))\n            } else if (!isReservedPage(route)) {\n              staticRoutes.push(pageToRoute(route))\n            }\n          }\n\n          return {\n            version: 3,\n            pages404: true,\n            caseSensitive: !!config.experimental.caseSensitiveRoutes,\n            basePath: config.basePath,\n            redirects: redirects.map((r) =>\n              buildCustomRoute('redirect', r, restrictedRedirectPaths)\n            ),\n            headers: headers.map((r) => buildCustomRoute('header', r)),\n            dynamicRoutes,\n            staticRoutes,\n            dataRoutes: [],\n            i18n: config.i18n || undefined,\n            rsc: {\n              header: RSC_HEADER,\n              // This vary header is used as a default. It is technically re-assigned in `base-server`,\n              // and may include an additional Vary option for `Next-URL`.\n              varyHeader: `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`,\n              prefetchHeader: NEXT_ROUTER_PREFETCH_HEADER,\n              didPostponeHeader: NEXT_DID_POSTPONE_HEADER,\n              contentTypeHeader: RSC_CONTENT_TYPE_HEADER,\n              suffix: RSC_SUFFIX,\n              prefetchSuffix: RSC_PREFETCH_SUFFIX,\n              prefetchSegmentHeader: NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n              prefetchSegmentSuffix: RSC_SEGMENT_SUFFIX,\n              prefetchSegmentDirSuffix: RSC_SEGMENTS_DIR_SUFFIX,\n            },\n            rewriteHeaders: {\n              pathHeader: NEXT_REWRITTEN_PATH_HEADER,\n              queryHeader: NEXT_REWRITTEN_QUERY_HEADER,\n            },\n            skipMiddlewareUrlNormalize: config.skipMiddlewareUrlNormalize,\n            ppr: isAppPPREnabled\n              ? {\n                  chain: {\n                    headers: {\n                      [NEXT_RESUME_HEADER]: '1',\n                    },\n                  },\n                }\n              : undefined,\n          } satisfies RoutesManifest\n        })\n\n      if (rewrites.beforeFiles.length === 0 && rewrites.fallback.length === 0) {\n        routesManifest.rewrites = rewrites.afterFiles.map((r) =>\n          buildCustomRoute('rewrite', r)\n        )\n      } else {\n        routesManifest.rewrites = {\n          beforeFiles: rewrites.beforeFiles.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n          afterFiles: rewrites.afterFiles.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n          fallback: rewrites.fallback.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n        }\n      }\n      let clientRouterFilters:\n        | undefined\n        | ReturnType<typeof createClientRouterFilter>\n\n      if (config.experimental.clientRouterFilter) {\n        const nonInternalRedirects = (config._originalRedirects || []).filter(\n          (r: any) => !r.internal\n        )\n        clientRouterFilters = createClientRouterFilter(\n          [...appPaths],\n          config.experimental.clientRouterFilterRedirects\n            ? nonInternalRedirects\n            : [],\n          config.experimental.clientRouterFilterAllowedRate\n        )\n        NextBuildContext.clientRouterFilters = clientRouterFilters\n      }\n\n      // Ensure commonjs handling is used for files in the distDir (generally .next)\n      // Files outside of the distDir can be \"type\": \"module\"\n      await writeFileUtf8(\n        path.join(distDir, 'package.json'),\n        '{\"type\": \"commonjs\"}'\n      )\n\n      // These are written to distDir, so they need to come after creating and cleaning distDr.\n      await recordFrameworkVersion(process.env.__NEXT_VERSION as string)\n      await updateBuildDiagnostics({\n        buildStage: 'start',\n      })\n\n      const outputFileTracingRoot = config.outputFileTracingRoot || dir\n\n      const pagesManifestPath = path.join(\n        distDir,\n        SERVER_DIRECTORY,\n        PAGES_MANIFEST\n      )\n\n      let buildTraceContext: undefined | BuildTraceContext\n      let buildTracesPromise: Promise<any> | undefined = undefined\n\n      // If there's has a custom webpack config and disable the build worker.\n      // Otherwise respect the option if it's set.\n      const useBuildWorker =\n        config.experimental.webpackBuildWorker ||\n        (config.experimental.webpackBuildWorker === undefined &&\n          !config.webpack)\n      const runServerAndEdgeInParallel =\n        config.experimental.parallelServerCompiles\n      const collectServerBuildTracesInParallel =\n        config.experimental.parallelServerBuildTraces ||\n        (config.experimental.parallelServerBuildTraces === undefined &&\n          isCompileMode)\n\n      nextBuildSpan.setAttribute(\n        'has-custom-webpack-config',\n        String(!!config.webpack)\n      )\n      nextBuildSpan.setAttribute('use-build-worker', String(useBuildWorker))\n\n      if (\n        !useBuildWorker &&\n        (runServerAndEdgeInParallel || collectServerBuildTracesInParallel)\n      ) {\n        throw new Error(\n          'The \"parallelServerBuildTraces\" and \"parallelServerCompiles\" options may only be used when build workers can be used. Read more: https://nextjs.org/docs/messages/parallel-build-without-worker'\n        )\n      }\n\n      Log.info('Creating an optimized production build ...')\n      traceMemoryUsage('Starting build', nextBuildSpan)\n\n      await updateBuildDiagnostics({\n        buildStage: 'compile',\n        buildOptions: {\n          useBuildWorker: String(useBuildWorker),\n        },\n      })\n\n      let shutdownPromise = Promise.resolve()\n      if (!isGenerateMode) {\n        if (isTurbopack) {\n          const {\n            duration: compilerDuration,\n            shutdownPromise: p,\n            ...rest\n          } = await turbopackBuild(\n            process.env.NEXT_TURBOPACK_USE_WORKER === undefined ||\n              process.env.NEXT_TURBOPACK_USE_WORKER !== '0'\n          )\n          shutdownPromise = p\n          traceMemoryUsage('Finished build', nextBuildSpan)\n\n          buildTraceContext = rest.buildTraceContext\n\n          const durationString = durationToString(compilerDuration)\n          Log.event(`Compiled successfully in ${durationString}`)\n\n          telemetry.record(\n            eventBuildCompleted(pagesPaths, {\n              bundler: 'turbopack',\n              durationInSeconds: Math.round(compilerDuration),\n              totalAppPagesCount,\n            })\n          )\n        } else {\n          if (\n            runServerAndEdgeInParallel ||\n            collectServerBuildTracesInParallel\n          ) {\n            let durationInSeconds = 0\n\n            await updateBuildDiagnostics({\n              buildStage: 'compile-server',\n            })\n\n            const serverBuildPromise = webpackBuild(useBuildWorker, [\n              'server',\n            ]).then((res) => {\n              traceMemoryUsage('Finished server compilation', nextBuildSpan)\n              buildTraceContext = res.buildTraceContext\n              durationInSeconds += res.duration\n\n              if (collectServerBuildTracesInParallel) {\n                const buildTraceWorker = new Worker(\n                  require.resolve('./collect-build-traces'),\n                  {\n                    numWorkers: 1,\n                    exposedMethods: ['collectBuildTraces'],\n                  }\n                ) as Worker & typeof import('./collect-build-traces')\n\n                buildTracesPromise = buildTraceWorker\n                  .collectBuildTraces({\n                    dir,\n                    config,\n                    distDir,\n                    // Serialize Map as this is sent to the worker.\n                    edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(new Map()),\n                    staticPages: [],\n                    hasSsrAmpPages: false,\n                    buildTraceContext,\n                    outputFileTracingRoot,\n                  })\n                  .catch((err) => {\n                    console.error(err)\n                    process.exit(1)\n                  })\n              }\n            })\n            if (!runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n\n            const edgeBuildPromise = webpackBuild(useBuildWorker, [\n              'edge-server',\n            ]).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage(\n                'Finished edge-server compilation',\n                nextBuildSpan\n              )\n            })\n            if (runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n            await edgeBuildPromise\n\n            await updateBuildDiagnostics({\n              buildStage: 'webpack-compile-client',\n            })\n\n            await webpackBuild(useBuildWorker, ['client']).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage('Finished client compilation', nextBuildSpan)\n            })\n\n            const durationString = durationToString(durationInSeconds)\n            Log.event(`Compiled successfully in ${durationString}`)\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds,\n                totalAppPagesCount,\n              })\n            )\n          } else {\n            const { duration: compilerDuration, ...rest } = await webpackBuild(\n              useBuildWorker,\n              null\n            )\n            traceMemoryUsage('Finished build', nextBuildSpan)\n\n            buildTraceContext = rest.buildTraceContext\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds: compilerDuration,\n                totalAppPagesCount,\n              })\n            )\n          }\n        }\n      }\n\n      // For app directory, we run type checking after build.\n      if (appDir && !isCompileMode && !isGenerateMode) {\n        await updateBuildDiagnostics({\n          buildStage: 'type-checking',\n        })\n        await startTypeChecking(typeCheckingOptions)\n        traceMemoryUsage('Finished type checking', nextBuildSpan)\n      }\n\n      const postCompileSpinner = createSpinner('Collecting page data')\n\n      const buildManifestPath = path.join(distDir, BUILD_MANIFEST)\n      const appBuildManifestPath = path.join(distDir, APP_BUILD_MANIFEST)\n\n      let staticAppPagesCount = 0\n      let serverAppPagesCount = 0\n      let edgeRuntimeAppCount = 0\n      let edgeRuntimePagesCount = 0\n      const ssgPages = new Set<string>()\n      const ssgStaticFallbackPages = new Set<string>()\n      const ssgBlockingFallbackPages = new Set<string>()\n      const staticPages = new Set<string>()\n      const invalidPages = new Set<string>()\n      const hybridAmpPages = new Set<string>()\n      const serverPropsPages = new Set<string>()\n      const additionalPaths = new Map<string, PrerenderedRoute[]>()\n      const staticPaths = new Map<string, PrerenderedRoute[]>()\n      const prospectiveRenders = new Map<\n        string,\n        { page: string; originalAppPath: string }\n      >()\n      const appNormalizedPaths = new Map<string, string>()\n      const fallbackModes = new Map<string, FallbackMode>()\n      const appDefaultConfigs = new Map<string, AppSegmentConfig>()\n      const pageInfos: PageInfos = new Map<string, PageInfo>()\n      let pagesManifest = await readManifest<PagesManifest>(pagesManifestPath)\n      const buildManifest = await readManifest<BuildManifest>(buildManifestPath)\n      const appBuildManifest = appDir\n        ? await readManifest<AppBuildManifest>(appBuildManifestPath)\n        : undefined\n\n      const appPathRoutes: Record<string, string> = {}\n\n      if (appDir) {\n        const appPathsManifest = await readManifest<Record<string, string>>(\n          path.join(distDir, SERVER_DIRECTORY, APP_PATHS_MANIFEST)\n        )\n\n        for (const key in appPathsManifest) {\n          appPathRoutes[key] = normalizeAppPath(key)\n        }\n\n        await writeManifest(\n          path.join(distDir, APP_PATH_ROUTES_MANIFEST),\n          appPathRoutes\n        )\n      }\n\n      process.env.NEXT_PHASE = PHASE_PRODUCTION_BUILD\n\n      const worker = createStaticWorker(config)\n\n      const analysisBegin = process.hrtime()\n      const staticCheckSpan = nextBuildSpan.traceChild('static-check')\n\n      const functionsConfigManifest: FunctionsConfigManifest = {\n        version: 1,\n        functions: {},\n      }\n\n      const {\n        customAppGetInitialProps,\n        namedExports,\n        isNextImageImported,\n        hasSsrAmpPages,\n        hasNonStaticErrorPage,\n      } = await staticCheckSpan.traceAsyncFn(async () => {\n        if (isCompileMode) {\n          return {\n            customAppGetInitialProps: false,\n            namedExports: [],\n            isNextImageImported: true,\n            hasSsrAmpPages: !!pagesDir,\n            hasNonStaticErrorPage: true,\n          }\n        }\n\n        const { configFileName, publicRuntimeConfig, serverRuntimeConfig } =\n          config\n        const runtimeEnvConfig = { publicRuntimeConfig, serverRuntimeConfig }\n        const sriEnabled = Boolean(config.experimental.sri?.algorithm)\n\n        const nonStaticErrorPageSpan = staticCheckSpan.traceChild(\n          'check-static-error-page'\n        )\n        const errorPageHasCustomGetInitialProps =\n          nonStaticErrorPageSpan.traceAsyncFn(\n            async () =>\n              hasCustomErrorPage &&\n              (await worker.hasCustomGetInitialProps({\n                page: '/_error',\n                distDir,\n                runtimeEnvConfig,\n                checkingApp: false,\n                sriEnabled,\n              }))\n          )\n\n        const errorPageStaticResult = nonStaticErrorPageSpan.traceAsyncFn(\n          async () =>\n            hasCustomErrorPage &&\n            worker.isPageStatic({\n              dir,\n              page: '/_error',\n              distDir,\n              configFileName,\n              runtimeEnvConfig,\n              dynamicIO: isAppDynamicIOEnabled,\n              authInterrupts: isAuthInterruptsEnabled,\n              httpAgentOptions: config.httpAgentOptions,\n              locales: config.i18n?.locales,\n              defaultLocale: config.i18n?.defaultLocale,\n              nextConfigOutput: config.output,\n              pprConfig: config.experimental.ppr,\n              cacheLifeProfiles: config.experimental.cacheLife,\n              buildId,\n              sriEnabled,\n            })\n        )\n\n        const appPageToCheck = '/_app'\n\n        const customAppGetInitialPropsPromise = worker.hasCustomGetInitialProps(\n          {\n            page: appPageToCheck,\n            distDir,\n            runtimeEnvConfig,\n            checkingApp: true,\n            sriEnabled,\n          }\n        )\n\n        const namedExportsPromise = worker.getDefinedNamedExports({\n          page: appPageToCheck,\n          distDir,\n          runtimeEnvConfig,\n          sriEnabled,\n        })\n\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let isNextImageImported: boolean | undefined\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let hasSsrAmpPages = false\n\n        const computedManifestData = await computeFromManifest(\n          { build: buildManifest, app: appBuildManifest },\n          distDir,\n          config.experimental.gzipSize\n        )\n\n        const middlewareManifest: MiddlewareManifest = require(\n          path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n        )\n\n        const actionManifest = appDir\n          ? (require(\n              path.join(\n                distDir,\n                SERVER_DIRECTORY,\n                SERVER_REFERENCE_MANIFEST + '.json'\n              )\n            ) as ActionManifest)\n          : null\n        const entriesWithAction = actionManifest ? new Set() : null\n        if (actionManifest && entriesWithAction) {\n          for (const id in actionManifest.node) {\n            for (const entry in actionManifest.node[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n          for (const id in actionManifest.edge) {\n            for (const entry in actionManifest.edge[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n        }\n\n        for (const key of Object.keys(middlewareManifest?.functions)) {\n          if (key.startsWith('/api')) {\n            edgeRuntimePagesCount++\n          }\n        }\n\n        await Promise.all(\n          Object.entries(pageKeys)\n            .reduce<Array<{ pageType: keyof typeof pageKeys; page: string }>>(\n              (acc, [key, files]) => {\n                if (!files) {\n                  return acc\n                }\n\n                const pageType = key as keyof typeof pageKeys\n\n                for (const page of files) {\n                  acc.push({ pageType, page })\n                }\n\n                return acc\n              },\n              []\n            )\n            .map(({ pageType, page }) => {\n              const checkPageSpan = staticCheckSpan.traceChild('check-page', {\n                page,\n              })\n              return checkPageSpan.traceAsyncFn(async () => {\n                const actualPage = normalizePagePath(page)\n                const [size, totalSize] = await getJsPageSizeInKb(\n                  pageType,\n                  actualPage,\n                  distDir,\n                  buildManifest,\n                  appBuildManifest,\n                  config.experimental.gzipSize,\n                  computedManifestData\n                )\n\n                let isRoutePPREnabled = false\n                let isSSG = false\n                let isStatic = false\n                let isServerComponent = false\n                let isHybridAmp = false\n                let ssgPageRoutes: string[] | null = null\n                let pagePath = ''\n\n                if (pageType === 'pages') {\n                  pagePath =\n                    pagesPaths.find((p) => {\n                      p = normalizePathSep(p)\n                      return (\n                        p.startsWith(actualPage + '.') ||\n                        p.startsWith(actualPage + '/index.')\n                      )\n                    }) || ''\n                }\n                let originalAppPath: string | undefined\n\n                if (pageType === 'app' && mappedAppPages) {\n                  for (const [originalPath, normalizedPath] of Object.entries(\n                    appPathRoutes\n                  )) {\n                    if (normalizedPath === page) {\n                      pagePath = mappedAppPages[originalPath].replace(\n                        /^private-next-app-dir/,\n                        ''\n                      )\n                      originalAppPath = originalPath\n                      break\n                    }\n                  }\n                }\n\n                const pageFilePath = isAppBuiltinNotFoundPage(pagePath)\n                  ? require.resolve(\n                      'next/dist/client/components/not-found-error'\n                    )\n                  : path.join(\n                      (pageType === 'pages' ? pagesDir : appDir) || '',\n                      pagePath\n                    )\n\n                const isInsideAppDir = pageType === 'app'\n                const staticInfo = pagePath\n                  ? await getStaticInfoIncludingLayouts({\n                      isInsideAppDir,\n                      pageFilePath,\n                      pageExtensions: config.pageExtensions,\n                      appDir,\n                      config,\n                      isDev: false,\n                      // If this route is an App Router page route, inherit the\n                      // route segment configs (e.g. `runtime`) from the layout by\n                      // passing the `originalAppPath`, which should end with `/page`.\n                      page: isInsideAppDir ? originalAppPath! : page,\n                    })\n                  : undefined\n\n                // If there's any thing that would contribute to the functions\n                // configuration, we need to add it to the manifest.\n                if (\n                  typeof staticInfo?.runtime !== 'undefined' ||\n                  typeof staticInfo?.maxDuration !== 'undefined'\n                ) {\n                  functionsConfigManifest.functions[page] = {\n                    maxDuration: staticInfo?.maxDuration,\n                  }\n                }\n\n                const pageRuntime = middlewareManifest.functions[\n                  originalAppPath || page\n                ]\n                  ? 'edge'\n                  : staticInfo?.runtime\n\n                if (!isCompileMode) {\n                  isServerComponent =\n                    pageType === 'app' &&\n                    staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n                  if (pageType === 'app' || !isReservedPage(page)) {\n                    try {\n                      let edgeInfo: any\n\n                      if (isEdgeRuntime(pageRuntime)) {\n                        if (pageType === 'app') {\n                          edgeRuntimeAppCount++\n                        } else {\n                          edgeRuntimePagesCount++\n                        }\n\n                        const manifestKey =\n                          pageType === 'pages' ? page : originalAppPath || ''\n\n                        edgeInfo = middlewareManifest.functions[manifestKey]\n                      }\n\n                      let isPageStaticSpan =\n                        checkPageSpan.traceChild('is-page-static')\n                      let workerResult = await isPageStaticSpan.traceAsyncFn(\n                        () => {\n                          return worker.isPageStatic({\n                            dir,\n                            page,\n                            originalAppPath,\n                            distDir,\n                            configFileName,\n                            runtimeEnvConfig,\n                            httpAgentOptions: config.httpAgentOptions,\n                            locales: config.i18n?.locales,\n                            defaultLocale: config.i18n?.defaultLocale,\n                            parentId: isPageStaticSpan.getId(),\n                            pageRuntime,\n                            edgeInfo,\n                            pageType,\n                            dynamicIO: isAppDynamicIOEnabled,\n                            authInterrupts: isAuthInterruptsEnabled,\n                            cacheHandler: config.cacheHandler,\n                            cacheHandlers: config.experimental.cacheHandlers,\n                            isrFlushToDisk: ciEnvironment.hasNextSupport\n                              ? false\n                              : config.experimental.isrFlushToDisk,\n                            maxMemoryCacheSize: config.cacheMaxMemorySize,\n                            nextConfigOutput: config.output,\n                            pprConfig: config.experimental.ppr,\n                            cacheLifeProfiles: config.experimental.cacheLife,\n                            buildId,\n                            sriEnabled,\n                          })\n                        }\n                      )\n\n                      if (pageType === 'app' && originalAppPath) {\n                        appNormalizedPaths.set(originalAppPath, page)\n                        // TODO-APP: handle prerendering with edge\n                        if (isEdgeRuntime(pageRuntime)) {\n                          isStatic = false\n                          isSSG = false\n\n                          Log.warnOnce(\n                            `Using edge runtime on a page currently disables static generation for that page`\n                          )\n                        } else {\n                          const isDynamic = isDynamicRoute(page)\n\n                          if (\n                            typeof workerResult.isRoutePPREnabled === 'boolean'\n                          ) {\n                            isRoutePPREnabled = workerResult.isRoutePPREnabled\n                          }\n\n                          // If this route can be partially pre-rendered, then\n                          // mark it as such and mark that it can be\n                          // generated server-side.\n                          if (workerResult.isRoutePPREnabled) {\n                            isSSG = true\n                            isStatic = true\n\n                            staticPaths.set(originalAppPath, [])\n                          }\n                          // As PPR isn't enabled for this route, if dynamic IO\n                          // is enabled, and this is a dynamic route, we should\n                          // complete a prospective render for the route so that\n                          // we can use the fallback behavior. This lets us\n                          // check that dynamic pages won't error when they\n                          // enable PPR.\n                          else if (config.experimental.dynamicIO && isDynamic) {\n                            prospectiveRenders.set(originalAppPath, {\n                              page,\n                              originalAppPath,\n                            })\n                          }\n\n                          if (workerResult.prerenderedRoutes) {\n                            staticPaths.set(\n                              originalAppPath,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                            isSSG = true\n                          }\n\n                          const appConfig = workerResult.appConfig || {}\n                          if (appConfig.revalidate !== 0) {\n                            const hasGenerateStaticParams =\n                              workerResult.prerenderedRoutes &&\n                              workerResult.prerenderedRoutes.length > 0\n\n                            if (\n                              config.output === 'export' &&\n                              isDynamic &&\n                              !hasGenerateStaticParams\n                            ) {\n                              throw new Error(\n                                `Page \"${page}\" is missing \"generateStaticParams()\" so it cannot be used with \"output: export\" config.`\n                              )\n                            }\n\n                            // Mark the app as static if:\n                            // - It has no dynamic param\n                            // - It doesn't have generateStaticParams but `dynamic` is set to\n                            //   `error` or `force-static`\n                            if (!isDynamic) {\n                              staticPaths.set(originalAppPath, [\n                                {\n                                  pathname: page,\n                                  encodedPathname: page,\n                                  fallbackRouteParams: undefined,\n                                  fallbackMode:\n                                    workerResult.prerenderFallbackMode,\n                                  fallbackRootParams: undefined,\n                                },\n                              ])\n                              isStatic = true\n                            } else if (\n                              !hasGenerateStaticParams &&\n                              (appConfig.dynamic === 'error' ||\n                                appConfig.dynamic === 'force-static')\n                            ) {\n                              staticPaths.set(originalAppPath, [])\n                              isStatic = true\n                              isRoutePPREnabled = false\n                            }\n                          }\n\n                          if (workerResult.prerenderFallbackMode) {\n                            fallbackModes.set(\n                              originalAppPath,\n                              workerResult.prerenderFallbackMode\n                            )\n                          }\n\n                          appDefaultConfigs.set(originalAppPath, appConfig)\n                        }\n                      } else {\n                        if (isEdgeRuntime(pageRuntime)) {\n                          if (workerResult.hasStaticProps) {\n                            console.warn(\n                              `\"getStaticProps\" is not yet supported fully with \"experimental-edge\", detected on ${page}`\n                            )\n                          }\n                          // TODO: add handling for statically rendering edge\n                          // pages and allow edge with Prerender outputs\n                          workerResult.isStatic = false\n                          workerResult.hasStaticProps = false\n                        }\n\n                        if (\n                          workerResult.isStatic === false &&\n                          (workerResult.isHybridAmp || workerResult.isAmpOnly)\n                        ) {\n                          hasSsrAmpPages = true\n                        }\n\n                        if (workerResult.isHybridAmp) {\n                          isHybridAmp = true\n                          hybridAmpPages.add(page)\n                        }\n\n                        if (workerResult.isNextImageImported) {\n                          isNextImageImported = true\n                        }\n\n                        if (workerResult.hasStaticProps) {\n                          ssgPages.add(page)\n                          isSSG = true\n\n                          if (\n                            workerResult.prerenderedRoutes &&\n                            workerResult.prerenderedRoutes.length > 0\n                          ) {\n                            additionalPaths.set(\n                              page,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                          }\n\n                          if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.BLOCKING_STATIC_RENDER\n                          ) {\n                            ssgBlockingFallbackPages.add(page)\n                          } else if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.PRERENDER\n                          ) {\n                            ssgStaticFallbackPages.add(page)\n                          }\n                        } else if (workerResult.hasServerProps) {\n                          serverPropsPages.add(page)\n                        } else if (\n                          workerResult.isStatic &&\n                          !isServerComponent &&\n                          (await customAppGetInitialPropsPromise) === false\n                        ) {\n                          staticPages.add(page)\n                          isStatic = true\n                        } else if (isServerComponent) {\n                          // This is a static server component page that doesn't have\n                          // gSP or gSSP. We still treat it as a SSG page.\n                          ssgPages.add(page)\n                          isSSG = true\n                        }\n\n                        if (hasPages404 && page === '/404') {\n                          if (\n                            !workerResult.isStatic &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            throw new Error(\n                              `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                            )\n                          }\n                          // we need to ensure the 404 lambda is present since we use\n                          // it when _app has getInitialProps\n                          if (\n                            (await customAppGetInitialPropsPromise) &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            staticPages.delete(page)\n                          }\n                        }\n\n                        if (\n                          STATIC_STATUS_PAGES.includes(page) &&\n                          !workerResult.isStatic &&\n                          !workerResult.hasStaticProps\n                        ) {\n                          throw new Error(\n                            `\\`pages${page}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                          )\n                        }\n                      }\n                    } catch (err) {\n                      if (\n                        !isError(err) ||\n                        err.message !== 'INVALID_DEFAULT_EXPORT'\n                      )\n                        throw err\n                      invalidPages.add(page)\n                    }\n                  }\n\n                  if (pageType === 'app') {\n                    if (isSSG || isStatic) {\n                      staticAppPagesCount++\n                    } else {\n                      serverAppPagesCount++\n                    }\n                  }\n                }\n\n                pageInfos.set(page, {\n                  size,\n                  totalSize,\n                  isStatic,\n                  isSSG,\n                  isRoutePPREnabled,\n                  isHybridAmp,\n                  ssgPageRoutes,\n                  initialCacheControl: undefined,\n                  runtime: pageRuntime,\n                  pageDuration: undefined,\n                  ssgPageDurations: undefined,\n                  hasEmptyPrelude: undefined,\n                })\n              })\n            })\n        )\n\n        if (hadUnsupportedValue) {\n          Log.error(\n            `Invalid config value exports detected, these can cause unexpected behavior from the configs not being applied. Please fix them to continue`\n          )\n          process.exit(1)\n        }\n\n        const errorPageResult = await errorPageStaticResult\n        const nonStaticErrorPage =\n          (await errorPageHasCustomGetInitialProps) ||\n          (errorPageResult && errorPageResult.hasServerProps)\n\n        const returnValue = {\n          customAppGetInitialProps: await customAppGetInitialPropsPromise,\n          namedExports: await namedExportsPromise,\n          isNextImageImported,\n          hasSsrAmpPages,\n          hasNonStaticErrorPage: nonStaticErrorPage,\n        }\n\n        return returnValue\n      })\n\n      if (postCompileSpinner) postCompileSpinner.stopAndPersist()\n      traceMemoryUsage('Finished collecting page data', nextBuildSpan)\n\n      if (customAppGetInitialProps) {\n        console.warn(\n          bold(yellow(`Warning: `)) +\n            yellow(\n              `You have opted-out of Automatic Static Optimization due to \\`getInitialProps\\` in \\`pages/_app\\`. This does not opt-out pages with \\`getStaticProps\\``\n            )\n        )\n        console.warn(\n          'Read more: https://nextjs.org/docs/messages/opt-out-auto-static-optimization\\n'\n        )\n      }\n\n      const { cacheHandler } = config\n\n      const instrumentationHookEntryFiles: string[] = []\n      if (hasInstrumentationHook) {\n        instrumentationHookEntryFiles.push(\n          path.join(SERVER_DIRECTORY, `${INSTRUMENTATION_HOOK_FILENAME}.js`)\n        )\n        // If there's edge routes, append the edge instrumentation hook\n        // Turbopack generates this chunk with a hashed name and references it in middleware-manifest.\n        if (!isTurbopack && (edgeRuntimeAppCount || edgeRuntimePagesCount)) {\n          instrumentationHookEntryFiles.push(\n            path.join(\n              SERVER_DIRECTORY,\n              `edge-${INSTRUMENTATION_HOOK_FILENAME}.js`\n            )\n          )\n        }\n      }\n\n      const requiredServerFilesManifest = nextBuildSpan\n        .traceChild('generate-required-server-files')\n        .traceFn(() => {\n          const normalizedCacheHandlers: Record<string, string> = {}\n\n          for (const [key, value] of Object.entries(\n            config.experimental.cacheHandlers || {}\n          )) {\n            if (key && value) {\n              normalizedCacheHandlers[key] = path.relative(distDir, value)\n            }\n          }\n\n          const serverFilesManifest: RequiredServerFilesManifest = {\n            version: 1,\n            config: {\n              ...config,\n              configFile: undefined,\n              ...(ciEnvironment.hasNextSupport\n                ? {\n                    compress: false,\n                  }\n                : {}),\n              cacheHandler: cacheHandler\n                ? path.relative(distDir, cacheHandler)\n                : config.cacheHandler,\n              experimental: {\n                ...config.experimental,\n                cacheHandlers: normalizedCacheHandlers,\n                trustHostHeader: ciEnvironment.hasNextSupport,\n\n                // @ts-expect-error internal field TODO: fix this, should use a separate mechanism to pass the info.\n                isExperimentalCompile: isCompileMode,\n              },\n            },\n            appDir: dir,\n            relativeAppDir: path.relative(outputFileTracingRoot, dir),\n            files: [\n              ROUTES_MANIFEST,\n              path.relative(distDir, pagesManifestPath),\n              BUILD_MANIFEST,\n              PRERENDER_MANIFEST,\n              path.join(SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_BUILD_MANIFEST + '.js'),\n              ...(!isTurbopack\n                ? [\n                    path.join(\n                      SERVER_DIRECTORY,\n                      MIDDLEWARE_REACT_LOADABLE_MANIFEST + '.js'\n                    ),\n                    REACT_LOADABLE_MANIFEST,\n                  ]\n                : []),\n              ...(appDir\n                ? [\n                    ...(config.experimental.sri\n                      ? [\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.js'\n                          ),\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.json'\n                          ),\n                        ]\n                      : []),\n                    path.join(SERVER_DIRECTORY, APP_PATHS_MANIFEST),\n                    path.join(APP_PATH_ROUTES_MANIFEST),\n                    APP_BUILD_MANIFEST,\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.js'\n                    ),\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.json'\n                    ),\n                  ]\n                : []),\n              ...(pagesDir && !isTurbopack\n                ? [\n                    DYNAMIC_CSS_MANIFEST + '.json',\n                    path.join(SERVER_DIRECTORY, DYNAMIC_CSS_MANIFEST + '.js'),\n                  ]\n                : []),\n              BUILD_ID_FILE,\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.js'),\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.json'),\n              ...instrumentationHookEntryFiles,\n            ]\n              .filter(nonNullable)\n              .map((file) => path.join(config.distDir, file)),\n            ignore: [] as string[],\n          }\n\n          return serverFilesManifest\n        })\n\n      if (!hasSsrAmpPages) {\n        requiredServerFilesManifest.ignore.push(\n          path.relative(\n            dir,\n            path.join(\n              path.dirname(\n                require.resolve(\n                  'next/dist/compiled/@ampproject/toolbox-optimizer'\n                )\n              ),\n              '**/*'\n            )\n          )\n        )\n      }\n\n      const middlewareFile = rootPaths.find((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n      let hasNodeMiddleware = false\n\n      if (middlewareFile) {\n        const staticInfo = await getStaticInfoIncludingLayouts({\n          isInsideAppDir: false,\n          pageFilePath: path.join(dir, middlewareFile),\n          config,\n          appDir,\n          pageExtensions: config.pageExtensions,\n          isDev: false,\n          page: 'middleware',\n        })\n\n        if (staticInfo.runtime === 'nodejs') {\n          hasNodeMiddleware = true\n          functionsConfigManifest.functions['/_middleware'] = {\n            runtime: staticInfo.runtime,\n            matchers: staticInfo.middleware?.matchers ?? [\n              {\n                regexp: '^.*$',\n                originalSource: '/:path*',\n              },\n            ],\n          }\n\n          if (isTurbopack) {\n            await writeManifest(\n              path.join(\n                distDir,\n                'static',\n                buildId,\n                TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST\n              ),\n              functionsConfigManifest.functions['/_middleware'].matchers || []\n            )\n          }\n        }\n      }\n\n      await writeFunctionsConfigManifest(distDir, functionsConfigManifest)\n\n      if (!isGenerateMode && !buildTracesPromise) {\n        buildTracesPromise = collectBuildTraces({\n          dir,\n          config,\n          distDir,\n          edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(pageInfos),\n          staticPages: [...staticPages],\n          nextBuildSpan,\n          hasSsrAmpPages,\n          buildTraceContext,\n          outputFileTracingRoot,\n        }).catch((err) => {\n          console.error(err)\n          process.exit(1)\n        })\n      }\n\n      if (serverPropsPages.size > 0 || ssgPages.size > 0) {\n        // We update the routes manifest after the build with the\n        // data routes since we can't determine these until after build\n        routesManifest.dataRoutes = getSortedRoutes([\n          ...serverPropsPages,\n          ...ssgPages,\n        ]).map((page) => {\n          return buildDataRoute(page, buildId)\n        })\n      }\n\n      // We need to write the manifest with rewrites before build\n      await nextBuildSpan\n        .traceChild('write-routes-manifest')\n        .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n\n      // Since custom _app.js can wrap the 404 page we have to opt-out of static optimization if it has getInitialProps\n      // Only export the static 404 when there is no /_error present\n      const useStaticPages404 =\n        !customAppGetInitialProps && (!hasNonStaticErrorPage || hasPages404)\n\n      if (invalidPages.size > 0) {\n        const err = new Error(\n          `Build optimization failed: found page${\n            invalidPages.size === 1 ? '' : 's'\n          } without a React Component as default export in \\n${[...invalidPages]\n            .map((pg) => `pages${pg}`)\n            .join(\n              '\\n'\n            )}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.\\n`\n        ) as NextError\n        err.code = 'BUILD_OPTIMIZATION_FAILED'\n        throw err\n      }\n\n      await writeBuildId(distDir, buildId)\n\n      if (config.experimental.optimizeCss) {\n        const globOrig =\n          require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n\n        const cssFilePaths = await new Promise<string[]>((resolve, reject) => {\n          globOrig(\n            '**/*.css',\n            { cwd: path.join(distDir, 'static') },\n            (err, files) => {\n              if (err) {\n                return reject(err)\n              }\n              resolve(files)\n            }\n          )\n        })\n\n        requiredServerFilesManifest.files.push(\n          ...cssFilePaths.map((filePath) =>\n            path.join(config.distDir, 'static', filePath)\n          )\n        )\n      }\n\n      const features: EventBuildFeatureUsage[] = [\n        {\n          featureName: 'experimental/dynamicIO',\n          invocationCount: config.experimental.dynamicIO ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/optimizeCss',\n          invocationCount: config.experimental.optimizeCss ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/nextScriptWorkers',\n          invocationCount: config.experimental.nextScriptWorkers ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/ppr',\n          invocationCount: config.experimental.ppr ? 1 : 0,\n        },\n        {\n          featureName: 'turbopackPersistentCaching',\n          invocationCount: isPersistentCachingEnabled(config) ? 1 : 0,\n        },\n      ]\n      telemetry.record(\n        features.map((feature) => {\n          return {\n            eventName: EVENT_BUILD_FEATURE_USAGE,\n            payload: feature,\n          }\n        })\n      )\n\n      await writeRequiredServerFilesManifest(\n        distDir,\n        requiredServerFilesManifest\n      )\n\n      // we don't need to inline for turbopack build as\n      // it will handle it's own caching separate of compile\n      if (isGenerateMode && !isTurbopack) {\n        Log.info('Inlining static env ...')\n\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n      }\n\n      const middlewareManifest: MiddlewareManifest = await readManifest(\n        path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      )\n\n      const prerenderManifest: PrerenderManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: previewProps,\n      }\n\n      const tbdPrerenderRoutes: string[] = []\n\n      const { i18n } = config\n\n      const usedStaticStatusPages = STATIC_STATUS_PAGES.filter(\n        (page) =>\n          mappedPages[page] &&\n          mappedPages[page].startsWith('private-next-pages')\n      )\n      usedStaticStatusPages.forEach((page) => {\n        if (!ssgPages.has(page) && !customAppGetInitialProps) {\n          staticPages.add(page)\n        }\n      })\n\n      const hasPages500 = usedStaticStatusPages.includes('/500')\n      const useDefaultStatic500 =\n        !hasPages500 && !hasNonStaticErrorPage && !customAppGetInitialProps\n\n      const combinedPages = [...staticPages, ...ssgPages]\n      const isApp404Static = staticPaths.has(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      const hasStaticApp404 = hasApp404 && isApp404Static\n\n      await updateBuildDiagnostics({\n        buildStage: 'static-generation',\n      })\n\n      // we need to trigger automatic exporting when we have\n      // - static 404/500\n      // - getStaticProps paths\n      // - experimental app is enabled\n      if (\n        !isCompileMode &&\n        (combinedPages.length > 0 ||\n          useStaticPages404 ||\n          useDefaultStatic500 ||\n          appDir)\n      ) {\n        const staticGenerationSpan =\n          nextBuildSpan.traceChild('static-generation')\n        await staticGenerationSpan.traceAsyncFn(async () => {\n          detectConflictingPaths(\n            [\n              ...combinedPages,\n              ...pageKeys.pages.filter((page) => !combinedPages.includes(page)),\n            ],\n            ssgPages,\n            new Map(\n              Array.from(additionalPaths.entries()).map(\n                ([page, routes]): [string, string[]] => {\n                  return [page, routes.map((route) => route.pathname)]\n                }\n              )\n            )\n          )\n          const exportApp = require('../export')\n            .default as typeof import('../export').default\n\n          const exportConfig: NextConfigComplete = {\n            ...config,\n            // Default map will be the collection of automatic statically exported\n            // pages and incremental pages.\n            // n.b. we cannot handle this above in combinedPages because the dynamic\n            // page must be in the `pages` array, but not in the mapping.\n            exportPathMap: (defaultMap: ExportPathMap) => {\n              // Dynamically routed pages should be prerendered to be used as\n              // a client-side skeleton (fallback) while data is being fetched.\n              // This ensures the end-user never sees a 500 or slow response from the\n              // server.\n              //\n              // Note: prerendering disables automatic static optimization.\n              ssgPages.forEach((page) => {\n                if (isDynamicRoute(page)) {\n                  tbdPrerenderRoutes.push(page)\n\n                  if (ssgStaticFallbackPages.has(page)) {\n                    // Override the rendering for the dynamic page to be treated as a\n                    // fallback render.\n                    if (i18n) {\n                      defaultMap[`/${i18n.defaultLocale}${page}`] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    } else {\n                      defaultMap[page] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    }\n                  } else {\n                    // Remove dynamically routed pages from the default path map when\n                    // fallback behavior is disabled.\n                    delete defaultMap[page]\n                  }\n                }\n              })\n\n              // Append the \"well-known\" routes we should prerender for, e.g. blog\n              // post slugs.\n              additionalPaths.forEach((routes, page) => {\n                routes.forEach((route) => {\n                  defaultMap[route.pathname] = {\n                    page,\n                    _ssgPath: route.encodedPathname,\n                  }\n                })\n              })\n\n              if (useStaticPages404) {\n                defaultMap['/404'] = {\n                  page: hasPages404 ? '/404' : '/_error',\n                }\n              }\n\n              if (useDefaultStatic500) {\n                defaultMap['/500'] = {\n                  page: '/_error',\n                }\n              }\n\n              // TODO: output manifest specific to app paths and their\n              // revalidate periods and dynamicParams settings\n              staticPaths.forEach((routes, originalAppPath) => {\n                const appConfig = appDefaultConfigs.get(originalAppPath)\n                const isDynamicError = appConfig?.dynamic === 'error'\n\n                const isRoutePPREnabled = appConfig\n                  ? checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                  : undefined\n\n                routes.forEach((route) => {\n                  // If the route has any dynamic root segments, we need to skip\n                  // rendering the route. This is because we don't support\n                  // revalidating the shells without the parameters present.\n                  if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    return\n                  }\n\n                  defaultMap[route.pathname] = {\n                    page: originalAppPath,\n                    _ssgPath: route.encodedPathname,\n                    _fallbackRouteParams: route.fallbackRouteParams,\n                    _isDynamicError: isDynamicError,\n                    _isAppDir: true,\n                    _isRoutePPREnabled: isRoutePPREnabled,\n                  }\n                })\n              })\n\n              // If the app does have dynamic IO enabled but does not have PPR\n              // enabled, then we need to perform a prospective render for all\n              // the dynamic pages to ensure that they won't error during\n              // rendering (due to a missing prelude).\n              for (const {\n                page,\n                originalAppPath,\n              } of prospectiveRenders.values()) {\n                defaultMap[page] = {\n                  page: originalAppPath,\n                  _ssgPath: page,\n                  _fallbackRouteParams: getParamKeys(page),\n                  // Prospective renders are only enabled for app pages.\n                  _isAppDir: true,\n                  // Prospective renders are only enabled when PPR is disabled.\n                  _isRoutePPREnabled: false,\n                  _isProspectiveRender: true,\n                  // Dynamic IO does not currently support `dynamic === 'error'`.\n                  _isDynamicError: false,\n                }\n              }\n\n              if (i18n) {\n                for (const page of [\n                  ...staticPages,\n                  ...ssgPages,\n                  ...(useStaticPages404 ? ['/404'] : []),\n                  ...(useDefaultStatic500 ? ['/500'] : []),\n                ]) {\n                  const isSsg = ssgPages.has(page)\n                  const isDynamic = isDynamicRoute(page)\n                  const isFallback = isSsg && ssgStaticFallbackPages.has(page)\n\n                  for (const locale of i18n.locales) {\n                    // skip fallback generation for SSG pages without fallback mode\n                    if (isSsg && isDynamic && !isFallback) continue\n                    const outputPath = `/${locale}${page === '/' ? '' : page}`\n\n                    defaultMap[outputPath] = {\n                      page: defaultMap[page]?.page || page,\n                      _locale: locale,\n                      _pagesFallback: isFallback,\n                    }\n                  }\n\n                  if (isSsg) {\n                    // remove non-locale prefixed variant from defaultMap\n                    delete defaultMap[page]\n                  }\n                }\n              }\n\n              return defaultMap\n            },\n          }\n\n          const outdir = path.join(distDir, 'export')\n          const exportResult = await exportApp(\n            dir,\n            {\n              nextConfig: exportConfig,\n              enabledDirectories,\n              silent: true,\n              buildExport: true,\n              debugOutput,\n              pages: combinedPages,\n              outdir,\n              statusMessage: 'Generating static pages',\n              numWorkers: getNumberOfWorkers(exportConfig),\n            },\n            nextBuildSpan\n          )\n\n          // If there was no result, there's nothing more to do.\n          if (!exportResult) return\n\n          const getCacheControl = (\n            exportPath: string,\n            defaultRevalidate: Revalidate = false\n          ): CacheControl => {\n            const cacheControl =\n              exportResult.byPath.get(exportPath)?.cacheControl\n\n            if (!cacheControl) {\n              return { revalidate: defaultRevalidate, expire: undefined }\n            }\n\n            if (\n              cacheControl.revalidate !== false &&\n              cacheControl.revalidate > 0 &&\n              cacheControl.expire === undefined\n            ) {\n              return {\n                revalidate: cacheControl.revalidate,\n                expire: config.expireTime,\n              }\n            }\n\n            return cacheControl\n          }\n\n          if (debugOutput || process.env.NEXT_SSG_FETCH_METRICS === '1') {\n            recordFetchMetrics(exportResult)\n          }\n\n          writeTurborepoAccessTraceResult({\n            distDir: config.distDir,\n            traces: [\n              turborepoAccessTraceResult,\n              ...exportResult.turborepoAccessTraceResults.values(),\n            ],\n          })\n\n          prerenderManifest.notFoundRoutes = Array.from(\n            exportResult.ssgNotFoundPaths\n          )\n\n          // remove server bundles that were exported\n          for (const page of staticPages) {\n            const serverBundle = getPagePath(page, distDir, undefined, false)\n            await fs.unlink(serverBundle)\n          }\n\n          staticPaths.forEach((prerenderedRoutes, originalAppPath) => {\n            const page = appNormalizedPaths.get(originalAppPath)\n            if (!page) throw new InvariantError('Page not found')\n\n            const appConfig = appDefaultConfigs.get(originalAppPath)\n            if (!appConfig) throw new InvariantError('App config not found')\n\n            let hasRevalidateZero =\n              appConfig.revalidate === 0 ||\n              getCacheControl(page).revalidate === 0\n\n            if (hasRevalidateZero && pageInfos.get(page)?.isStatic) {\n              // if the page was marked as being static, but it contains dynamic data\n              // (ie, in the case of a static generation bailout), then it should be marked dynamic\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                isStatic: false,\n                isSSG: false,\n              })\n            }\n\n            const isAppRouteHandler = isAppRouteRoute(originalAppPath)\n\n            // When this is an app page and PPR is enabled, the route supports\n            // partial pre-rendering.\n            const isRoutePPREnabled: true | undefined =\n              !isAppRouteHandler &&\n              checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                ? true\n                : undefined\n\n            const htmlBotsRegexString =\n              // The htmlLimitedBots has been converted to a string during loadConfig\n              config.htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING\n\n            // this flag is used to selectively bypass the static cache and invoke the lambda directly\n            // to enable server actions on static routes\n            const bypassFor: RouteHas[] = [\n              { type: 'header', key: ACTION_HEADER },\n              {\n                type: 'header',\n                key: 'content-type',\n                value: 'multipart/form-data;.*',\n              },\n              // If it's PPR rendered non-static page, bypass the PPR cache when streaming metadata is enabled.\n              // This will skip the postpone data for those bots requests and instead produce a dynamic render.\n              ...(isRoutePPREnabled\n                ? [\n                    {\n                      type: 'header',\n                      key: 'user-agent',\n                      value: htmlBotsRegexString,\n                    },\n                  ]\n                : []),\n            ]\n\n            // We should collect all the dynamic routes into a single array for\n            // this page. Including the full fallback route (the original\n            // route), any routes that were generated with unknown route params\n            // should be collected and included in the dynamic routes part\n            // of the manifest instead.\n            const routes: PrerenderedRoute[] = []\n            const dynamicRoutes: PrerenderedRoute[] = []\n\n            // Sort the outputted routes to ensure consistent output. Any route\n            // though that has unknown route params will be pulled and sorted\n            // independently. This is because the routes with unknown route\n            // params will contain the dynamic path parameters, some of which\n            // may conflict with the actual prerendered routes.\n            let unknownPrerenderRoutes: PrerenderedRoute[] = []\n            let knownPrerenderRoutes: PrerenderedRoute[] = []\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                unknownPrerenderRoutes.push(prerenderedRoute)\n              } else {\n                knownPrerenderRoutes.push(prerenderedRoute)\n              }\n            }\n\n            unknownPrerenderRoutes = getSortedRouteObjects(\n              unknownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n            knownPrerenderRoutes = getSortedRouteObjects(\n              knownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n\n            prerenderedRoutes = [\n              ...knownPrerenderRoutes,\n              ...unknownPrerenderRoutes,\n            ]\n\n            for (const prerenderedRoute of prerenderedRoutes) {\n              // TODO: check if still needed?\n              // Exclude the /_not-found route.\n              if (prerenderedRoute.pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n                continue\n              }\n\n              if (\n                isRoutePPREnabled &&\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                // If the route has unknown params, then we need to add it to\n                // the list of dynamic routes.\n                dynamicRoutes.push(prerenderedRoute)\n              } else {\n                // If the route doesn't have unknown params, then we need to\n                // add it to the list of routes.\n                routes.push(prerenderedRoute)\n              }\n            }\n\n            // Handle all the static routes.\n            for (const route of routes) {\n              if (isDynamicRoute(page) && route.pathname === page) continue\n              if (route.pathname === UNDERSCORE_NOT_FOUND_ROUTE) continue\n\n              const {\n                metadata = {},\n                hasEmptyPrelude,\n                hasPostponed,\n              } = exportResult.byPath.get(route.pathname) ?? {}\n\n              const cacheControl = getCacheControl(\n                route.pathname,\n                appConfig.revalidate\n              )\n\n              pageInfos.set(route.pathname, {\n                ...(pageInfos.get(route.pathname) as PageInfo),\n                hasPostponed,\n                hasEmptyPrelude,\n                initialCacheControl: cacheControl,\n              })\n\n              // update the page (eg /blog/[slug]) to also have the postpone metadata\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                hasPostponed,\n                hasEmptyPrelude,\n                initialCacheControl: cacheControl,\n              })\n\n              if (cacheControl.revalidate !== 0) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                let dataRoute: string | null\n                if (isAppRouteHandler) {\n                  dataRoute = null\n                } else {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | null | undefined\n                // While we may only write the `.rsc` when the route does not\n                // have PPR enabled, we still want to generate the route when\n                // deployed so it doesn't 404. If the app has PPR enabled, we\n                // should add this key.\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                const meta = collectMeta(metadata)\n\n                prerenderManifest.routes[route.pathname] = {\n                  initialStatus: meta.status,\n                  initialHeaders: meta.headers,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalPPR: isRoutePPREnabled,\n                  experimentalBypassFor: bypassFor,\n                  initialRevalidateSeconds: cacheControl.revalidate,\n                  initialExpireSeconds: cacheControl.expire,\n                  srcRoute: page,\n                  dataRoute,\n                  prefetchDataRoute,\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              } else {\n                hasRevalidateZero = true\n                // we might have determined during prerendering that this page\n                // used dynamic data\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isSSG: false,\n                  isStatic: false,\n                })\n              }\n            }\n\n            if (!hasRevalidateZero && isDynamicRoute(page)) {\n              // When PPR fallbacks aren't used, we need to include it here. If\n              // they are enabled, then it'll already be included in the\n              // prerendered routes.\n              if (!isRoutePPREnabled) {\n                dynamicRoutes.push({\n                  pathname: page,\n                  encodedPathname: page,\n                  fallbackRouteParams: undefined,\n                  fallbackMode:\n                    fallbackModes.get(originalAppPath) ??\n                    FallbackMode.NOT_FOUND,\n                  fallbackRootParams: undefined,\n                })\n              }\n\n              for (const route of dynamicRoutes) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                const metadata = exportResult.byPath.get(\n                  route.pathname\n                )?.metadata\n\n                const cacheControl = getCacheControl(route.pathname)\n\n                let dataRoute: string | null = null\n                if (!isAppRouteHandler) {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | undefined\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                if (!isAppRouteHandler && metadata?.segmentPaths) {\n                  const dynamicRoute = routesManifest.dynamicRoutes.find(\n                    (r) => r.page === page\n                  )\n                  if (!dynamicRoute) {\n                    throw new Error('Dynamic route not found')\n                  }\n\n                  dynamicRoute.prefetchSegmentDataRoutes ??= []\n                  for (const segmentPath of metadata.segmentPaths) {\n                    const result = buildPrefetchSegmentDataRoute(\n                      route.pathname,\n                      segmentPath\n                    )\n                    dynamicRoute.prefetchSegmentDataRoutes.push(result)\n                  }\n                }\n\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isDynamicAppRoute: true,\n                  // if PPR is turned on and the route contains a dynamic segment,\n                  // we assume it'll be partially prerendered\n                  hasPostponed: isRoutePPREnabled,\n                })\n\n                const fallbackMode =\n                  route.fallbackMode ?? FallbackMode.NOT_FOUND\n\n                // When the route is configured to serve a prerender, we should\n                // use the cache control from the export result. If it can't be\n                // found, mark that we should keep the shell forever\n                // (revalidate: `false` via `getCacheControl()`).\n                const fallbackCacheControl =\n                  isRoutePPREnabled && fallbackMode === FallbackMode.PRERENDER\n                    ? cacheControl\n                    : undefined\n\n                const fallback: Fallback = fallbackModeToFallbackField(\n                  fallbackMode,\n                  route.pathname\n                )\n\n                const meta =\n                  metadata &&\n                  isRoutePPREnabled &&\n                  fallbackMode === FallbackMode.PRERENDER\n                    ? collectMeta(metadata)\n                    : {}\n\n                prerenderManifest.dynamicRoutes[route.pathname] = {\n                  experimentalPPR: isRoutePPREnabled,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalBypassFor: bypassFor,\n                  routeRegex: normalizeRouteRegex(\n                    getNamedRouteRegex(route.pathname, {\n                      prefixRouteKeys: false,\n                    }).re.source\n                  ),\n                  dataRoute,\n                  fallback,\n                  fallbackRevalidate: fallbackCacheControl?.revalidate,\n                  fallbackExpire: fallbackCacheControl?.expire,\n                  fallbackStatus: meta.status,\n                  fallbackHeaders: meta.headers,\n                  fallbackRootParams: route.fallbackRootParams,\n                  fallbackSourceRoute: route.fallbackRouteParams?.length\n                    ? page\n                    : undefined,\n                  dataRouteRegex: !dataRoute\n                    ? null\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(dataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  prefetchDataRoute,\n                  prefetchDataRouteRegex: !prefetchDataRoute\n                    ? undefined\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(prefetchDataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              }\n            }\n          })\n\n          const moveExportedPage = async (\n            originPage: string,\n            page: string,\n            file: string,\n            isSsg: boolean,\n            ext: 'html' | 'json',\n            additionalSsgFile = false\n          ) => {\n            return staticGenerationSpan\n              .traceChild('move-exported-page')\n              .traceAsyncFn(async () => {\n                file = `${file}.${ext}`\n                const orig = path.join(outdir, file)\n                const pagePath = getPagePath(\n                  originPage,\n                  distDir,\n                  undefined,\n                  false\n                )\n\n                const relativeDest = path\n                  .relative(\n                    path.join(distDir, SERVER_DIRECTORY),\n                    path.join(\n                      path.join(\n                        pagePath,\n                        // strip leading / and then recurse number of nested dirs\n                        // to place from base folder\n                        originPage\n                          .slice(1)\n                          .split('/')\n                          .map(() => '..')\n                          .join('/')\n                      ),\n                      file\n                    )\n                  )\n                  .replace(/\\\\/g, '/')\n\n                if (\n                  !isSsg &&\n                  !(\n                    // don't add static status page to manifest if it's\n                    // the default generated version e.g. no pages/500\n                    (\n                      STATIC_STATUS_PAGES.includes(page) &&\n                      !usedStaticStatusPages.includes(page)\n                    )\n                  )\n                ) {\n                  pagesManifest[page] = relativeDest\n                }\n\n                const dest = path.join(distDir, SERVER_DIRECTORY, relativeDest)\n                const isNotFound =\n                  prerenderManifest.notFoundRoutes.includes(page)\n\n                // for SSG files with i18n the non-prerendered variants are\n                // output with the locale prefixed so don't attempt moving\n                // without the prefix\n                if ((!i18n || additionalSsgFile) && !isNotFound) {\n                  await fs.mkdir(path.dirname(dest), { recursive: true })\n                  await fs.rename(orig, dest)\n                } else if (i18n && !isSsg) {\n                  // this will be updated with the locale prefixed variant\n                  // since all files are output with the locale prefix\n                  delete pagesManifest[page]\n                }\n\n                if (i18n) {\n                  if (additionalSsgFile) return\n\n                  const localeExt = page === '/' ? path.extname(file) : ''\n                  const relativeDestNoPages = relativeDest.slice(\n                    'pages/'.length\n                  )\n\n                  for (const locale of i18n.locales) {\n                    const curPath = `/${locale}${page === '/' ? '' : page}`\n\n                    if (\n                      isSsg &&\n                      prerenderManifest.notFoundRoutes.includes(curPath)\n                    ) {\n                      continue\n                    }\n\n                    const updatedRelativeDest = path\n                      .join(\n                        'pages',\n                        locale + localeExt,\n                        // if it's the top-most index page we want it to be locale.EXT\n                        // instead of locale/index.html\n                        page === '/' ? '' : relativeDestNoPages\n                      )\n                      .replace(/\\\\/g, '/')\n\n                    const updatedOrig = path.join(\n                      outdir,\n                      locale + localeExt,\n                      page === '/' ? '' : file\n                    )\n                    const updatedDest = path.join(\n                      distDir,\n                      SERVER_DIRECTORY,\n                      updatedRelativeDest\n                    )\n\n                    if (!isSsg) {\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                    await fs.mkdir(path.dirname(updatedDest), {\n                      recursive: true,\n                    })\n                    await fs.rename(updatedOrig, updatedDest)\n                  }\n                }\n              })\n          }\n\n          async function moveExportedAppNotFoundTo404() {\n            return staticGenerationSpan\n              .traceChild('move-exported-app-not-found-')\n              .traceAsyncFn(async () => {\n                const orig = path.join(\n                  distDir,\n                  'server',\n                  'app',\n                  '_not-found.html'\n                )\n                const updatedRelativeDest = path\n                  .join('pages', '404.html')\n                  .replace(/\\\\/g, '/')\n\n                if (existsSync(orig)) {\n                  await fs.copyFile(\n                    orig,\n                    path.join(distDir, 'server', updatedRelativeDest)\n                  )\n                  pagesManifest['/404'] = updatedRelativeDest\n                }\n              })\n          }\n\n          // If there's /not-found inside app, we prefer it over the pages 404\n          if (hasStaticApp404) {\n            await moveExportedAppNotFoundTo404()\n          } else {\n            // Only move /404 to /404 when there is no custom 404 as in that case we don't know about the 404 page\n            if (!hasPages404 && !hasApp404 && useStaticPages404) {\n              await moveExportedPage('/_error', '/404', '/404', false, 'html')\n            }\n          }\n\n          if (useDefaultStatic500) {\n            await moveExportedPage('/_error', '/500', '/500', false, 'html')\n          }\n\n          for (const page of combinedPages) {\n            const isSsg = ssgPages.has(page)\n            const isStaticSsgFallback = ssgStaticFallbackPages.has(page)\n            const isDynamic = isDynamicRoute(page)\n            const hasAmp = hybridAmpPages.has(page)\n            const file = normalizePagePath(page)\n\n            const pageInfo = pageInfos.get(page)\n            const durationInfo = exportResult.byPage.get(page)\n            if (pageInfo && durationInfo) {\n              // Set Build Duration\n              if (pageInfo.ssgPageRoutes) {\n                pageInfo.ssgPageDurations = pageInfo.ssgPageRoutes.map(\n                  (pagePath) => {\n                    const duration = durationInfo.durationsByPath.get(pagePath)\n                    if (typeof duration === 'undefined') {\n                      throw new Error(\"Invariant: page wasn't built\")\n                    }\n\n                    return duration\n                  }\n                )\n              }\n              pageInfo.pageDuration = durationInfo.durationsByPath.get(page)\n            }\n\n            // The dynamic version of SSG pages are only prerendered if the\n            // fallback is enabled. Below, we handle the specific prerenders\n            // of these.\n            const hasHtmlOutput = !(isSsg && isDynamic && !isStaticSsgFallback)\n\n            if (hasHtmlOutput) {\n              await moveExportedPage(page, page, file, isSsg, 'html')\n            }\n\n            if (hasAmp && (!isSsg || (isSsg && !isDynamic))) {\n              const ampPage = `${file}.amp`\n              await moveExportedPage(page, ampPage, ampPage, isSsg, 'html')\n\n              if (isSsg) {\n                await moveExportedPage(page, ampPage, ampPage, isSsg, 'json')\n              }\n            }\n\n            if (isSsg) {\n              // For a non-dynamic SSG page, we must copy its data file\n              // from export, we already moved the HTML file above\n              if (!isDynamic) {\n                await moveExportedPage(page, page, file, isSsg, 'json')\n\n                if (i18n) {\n                  // TODO: do we want to show all locale variants in build output\n                  for (const locale of i18n.locales) {\n                    const localePage = `/${locale}${page === '/' ? '' : page}`\n\n                    const cacheControl = getCacheControl(localePage)\n\n                    prerenderManifest.routes[localePage] = {\n                      initialRevalidateSeconds: cacheControl.revalidate,\n                      initialExpireSeconds: cacheControl.expire,\n                      experimentalPPR: undefined,\n                      renderingMode: undefined,\n                      srcRoute: null,\n                      dataRoute: path.posix.join(\n                        '/_next/data',\n                        buildId,\n                        `${file}.json`\n                      ),\n                      prefetchDataRoute: undefined,\n                      allowHeader: ALLOWED_HEADERS,\n                    }\n                  }\n                } else {\n                  const cacheControl = getCacheControl(page)\n\n                  prerenderManifest.routes[page] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: null,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${file}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n                }\n                if (pageInfo) {\n                  pageInfo.initialCacheControl = getCacheControl(page)\n                }\n              } else {\n                // For a dynamic SSG page, we did not copy its data exports and only\n                // copy the fallback HTML file (if present).\n                // We must also copy specific versions of this page as defined by\n                // `getStaticPaths` (additionalSsgPaths).\n                for (const route of additionalPaths.get(page) ?? []) {\n                  const pageFile = normalizePagePath(route.pathname)\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'html',\n                    true\n                  )\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'json',\n                    true\n                  )\n\n                  if (hasAmp) {\n                    const ampPage = `${pageFile}.amp`\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'html',\n                      true\n                    )\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'json',\n                      true\n                    )\n                  }\n\n                  const cacheControl = getCacheControl(route.pathname)\n\n                  prerenderManifest.routes[route.pathname] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: page,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${normalizePagePath(route.pathname)}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n\n                  if (pageInfo) {\n                    pageInfo.initialCacheControl = cacheControl\n                  }\n                }\n              }\n            }\n          }\n\n          // remove temporary export folder\n          await fs.rm(outdir, { recursive: true, force: true })\n          await writeManifest(pagesManifestPath, pagesManifest)\n        })\n\n        // We need to write the manifest with rewrites after build as it might\n        // have been modified.\n        await nextBuildSpan\n          .traceChild('write-routes-manifest')\n          .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n      }\n\n      const postBuildSpinner = createSpinner('Finalizing page optimization')\n      let buildTracesSpinner = createSpinner(`Collecting build traces`)\n\n      // ensure the worker is not left hanging\n      worker.end()\n\n      const analysisEnd = process.hrtime(analysisBegin)\n      telemetry.record(\n        eventBuildOptimize(pagesPaths, {\n          durationInSeconds: analysisEnd[0],\n          staticPageCount: staticPages.size,\n          staticPropsPageCount: ssgPages.size,\n          serverPropsPageCount: serverPropsPages.size,\n          ssrPageCount:\n            pagesPaths.length -\n            (staticPages.size + ssgPages.size + serverPropsPages.size),\n          hasStatic404: useStaticPages404,\n          hasReportWebVitals:\n            namedExports?.includes('reportWebVitals') ?? false,\n          rewritesCount: combinedRewrites.length,\n          headersCount: headers.length,\n          redirectsCount: redirects.length - 1, // reduce one for trailing slash\n          headersWithHasCount: headers.filter((r: any) => !!r.has).length,\n          rewritesWithHasCount: combinedRewrites.filter((r: any) => !!r.has)\n            .length,\n          redirectsWithHasCount: redirects.filter((r: any) => !!r.has).length,\n          middlewareCount: hasMiddlewareFile ? 1 : 0,\n          totalAppPagesCount,\n          staticAppPagesCount,\n          serverAppPagesCount,\n          edgeRuntimeAppCount,\n          edgeRuntimePagesCount,\n        })\n      )\n\n      if (NextBuildContext.telemetryState) {\n        const events = eventBuildFeatureUsage(\n          NextBuildContext.telemetryState.usages\n        )\n        telemetry.record(events)\n        telemetry.record(\n          eventPackageUsedInGetServerSideProps(\n            NextBuildContext.telemetryState.packagesUsedInServerSideProps\n          )\n        )\n        const useCacheTracker = NextBuildContext.telemetryState.useCacheTracker\n\n        for (const [key, value] of Object.entries(useCacheTracker)) {\n          telemetry.record(\n            eventBuildFeatureUsage([\n              {\n                featureName: key as UseCacheTrackerKey,\n                invocationCount: value,\n              },\n            ])\n          )\n        }\n      }\n\n      if (ssgPages.size > 0 || appDir) {\n        tbdPrerenderRoutes.forEach((tbdRoute) => {\n          const normalizedRoute = normalizePagePath(tbdRoute)\n          const dataRoute = path.posix.join(\n            '/_next/data',\n            buildId,\n            `${normalizedRoute}.json`\n          )\n\n          prerenderManifest.dynamicRoutes[tbdRoute] = {\n            routeRegex: normalizeRouteRegex(\n              getNamedRouteRegex(tbdRoute, {\n                prefixRouteKeys: false,\n              }).re.source\n            ),\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            dataRoute,\n            fallback: ssgBlockingFallbackPages.has(tbdRoute)\n              ? null\n              : ssgStaticFallbackPages.has(tbdRoute)\n                ? `${normalizedRoute}.html`\n                : false,\n            fallbackRevalidate: undefined,\n            fallbackExpire: undefined,\n            fallbackSourceRoute: undefined,\n            fallbackRootParams: undefined,\n            dataRouteRegex: normalizeRouteRegex(\n              getNamedRouteRegex(dataRoute, {\n                prefixRouteKeys: true,\n                includeSuffix: true,\n                excludeOptionalTrailingSlash: true,\n              }).re.source\n            ),\n            // Pages does not have a prefetch data route.\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            allowHeader: ALLOWED_HEADERS,\n          }\n        })\n\n        NextBuildContext.previewModeId = previewProps.previewModeId\n        NextBuildContext.fetchCacheKeyPrefix =\n          config.experimental.fetchCacheKeyPrefix\n        NextBuildContext.allowedRevalidateHeaderKeys =\n          config.experimental.allowedRevalidateHeaderKeys\n\n        await writePrerenderManifest(distDir, prerenderManifest)\n        await writeClientSsgManifest(prerenderManifest, {\n          distDir,\n          buildId,\n          locales: config.i18n?.locales,\n        })\n      } else {\n        await writePrerenderManifest(distDir, {\n          version: 4,\n          routes: {},\n          dynamicRoutes: {},\n          preview: previewProps,\n          notFoundRoutes: [],\n        })\n      }\n\n      await writeImagesManifest(distDir, config)\n      await writeManifest(path.join(distDir, EXPORT_MARKER), {\n        version: 1,\n        hasExportPathMap: typeof config.exportPathMap === 'function',\n        exportTrailingSlash: config.trailingSlash === true,\n        isNextImageImported: isNextImageImported === true,\n      })\n      await fs.unlink(path.join(distDir, EXPORT_DETAIL)).catch((err) => {\n        if (err.code === 'ENOENT') {\n          return Promise.resolve()\n        }\n        return Promise.reject(err)\n      })\n\n      if (Boolean(config.experimental.nextScriptWorkers)) {\n        await nextBuildSpan\n          .traceChild('verify-partytown-setup')\n          .traceAsyncFn(async () => {\n            await verifyPartytownSetup(\n              dir,\n              path.join(distDir, CLIENT_STATIC_FILES_PATH)\n            )\n          })\n      }\n\n      await buildTracesPromise\n\n      if (buildTracesSpinner) {\n        buildTracesSpinner.stopAndPersist()\n        buildTracesSpinner = undefined\n      }\n\n      if (isCompileMode) {\n        Log.info(\n          `Build ran with \"compile\" mode, to finalize the build run either \"generate\" or \"generate-env\" mode as well`\n        )\n      }\n\n      if (config.output === 'export') {\n        await writeFullyStaticExport(\n          config,\n          dir,\n          enabledDirectories,\n          configOutDir,\n          nextBuildSpan\n        )\n      }\n\n      if (config.output === 'standalone') {\n        await writeStandaloneDirectory(\n          nextBuildSpan,\n          distDir,\n          pageKeys,\n          denormalizedAppPages,\n          outputFileTracingRoot,\n          requiredServerFilesManifest,\n          middlewareManifest,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          staticPages,\n          loadedEnvFiles,\n          appDir\n        )\n      }\n\n      if (postBuildSpinner) postBuildSpinner.stopAndPersist()\n      console.log()\n\n      if (debugOutput) {\n        nextBuildSpan\n          .traceChild('print-custom-routes')\n          .traceFn(() => printCustomRoutes({ redirects, rewrites, headers }))\n      }\n\n      await nextBuildSpan.traceChild('print-tree-view').traceAsyncFn(() =>\n        printTreeView(pageKeys, pageInfos, {\n          distPath: distDir,\n          buildId: buildId,\n          pagesDir,\n          useStaticPages404,\n          pageExtensions: config.pageExtensions,\n          appBuildManifest,\n          buildManifest,\n          middlewareManifest,\n          gzipSize: config.experimental.gzipSize,\n        })\n      )\n\n      await nextBuildSpan\n        .traceChild('telemetry-flush')\n        .traceAsyncFn(() => telemetry.flush())\n\n      await shutdownPromise\n    })\n  } catch (e) {\n    const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n    if (telemetry) {\n      telemetry.record(\n        eventBuildFailed({\n          bundler: getBundlerForTelemetry(isTurbopack),\n          errorCode: getErrorCodeForTelemetry(e),\n          durationInSeconds: Math.floor((Date.now() - buildStartTime) / 1000),\n        })\n      )\n    }\n    throw e\n  } finally {\n    // Ensure we wait for lockfile patching if present\n    await lockfilePatchPromise.cur\n\n    if (isTurbopack && !process.env.__NEXT_TEST_MODE) {\n      warnAboutTurbopackBuilds(loadedConfig)\n    }\n\n    // Ensure all traces are flushed before finishing the command\n    await flushAllTraces()\n    teardownTraceSubscriber()\n\n    if (traceUploadUrl && loadedConfig) {\n      uploadTrace({\n        traceUploadUrl,\n        mode: 'build',\n        projectDir: dir,\n        distDir: loadedConfig.distDir,\n        isTurboSession: isTurbopack,\n        sync: true,\n      })\n    }\n  }\n}\n\nfunction warnAboutTurbopackBuilds(config?: NextConfigComplete) {\n  let warningStr =\n    `Support for Turbopack builds is experimental. ` +\n    bold(\n      `We don't recommend deploying mission-critical applications to production.`\n    )\n  warningStr +=\n    '\\n\\n- ' +\n    bold(\n      'Turbopack currently always builds production sourcemaps for the browser. This will include project sourcecode if deployed to production.'\n    )\n  warningStr +=\n    '\\n- It is expected that your bundle size might be different from `next build` with webpack. This will be improved as we work towards stability.'\n\n  if (!config?.experimental.turbopackPersistentCaching) {\n    warningStr +=\n      '\\n- This build is without disk caching; subsequent builds will become faster when disk caching becomes available.'\n  }\n\n  warningStr +=\n    '\\n- When comparing output to webpack builds, make sure to first clear the Next.js cache by deleting the `.next` directory.'\n  warningStr +=\n    '\\n\\nProvide feedback for Turbopack builds at https://github.com/vercel/next.js/discussions/77721'\n\n  Log.warn(warningStr)\n}\n\nfunction getBundlerForTelemetry(isTurbopack: boolean) {\n  if (isTurbopack) {\n    return 'turbopack'\n  }\n\n  if (process.env.NEXT_RSPACK) {\n    return 'rspack'\n  }\n\n  return 'webpack'\n}\n\nfunction getErrorCodeForTelemetry(err: unknown) {\n  const code = extractNextErrorCode(err)\n  if (code != null) {\n    return code\n  }\n\n  if (err instanceof Error && 'code' in err && typeof err.code === 'string') {\n    return err.code\n  }\n\n  if (err instanceof Error) {\n    return err.name\n  }\n\n  return 'Unknown'\n}\n"], "names": ["createStaticWorker", "build", "ALLOWED_HEADERS", "MATCHED_PATH_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "pageToRoute", "page", "routeRegex", "getNamedRouteRegex", "prefixRouteKeys", "regex", "normalizeRouteRegex", "re", "source", "routeKeys", "namedRegex", "getCacheDir", "distDir", "cacheDir", "path", "join", "ciEnvironment", "isCI", "hasNextSupport", "<PERSON><PERSON><PERSON>", "existsSync", "console", "log", "Log", "prefixes", "warn", "writeFileUtf8", "filePath", "content", "fs", "writeFile", "readFileUtf8", "readFile", "writeManifest", "manifest", "formatManifest", "readManifest", "JSON", "parse", "writePrerenderManifest", "PRERENDER_MANIFEST", "writeClientSsgManifest", "prerenderManifest", "buildId", "locales", "ssgPages", "Set", "Object", "entries", "routes", "filter", "srcRoute", "map", "route", "normalizeLocalePath", "pathname", "keys", "dynamicRoutes", "sort", "clientSsgManifestContent", "devalue", "CLIENT_STATIC_FILES_PATH", "writeFunctionsConfigManifest", "SERVER_DIRECTORY", "FUNCTIONS_CONFIG_MANIFEST", "writeRequiredServerFilesManifest", "requiredServerFiles", "SERVER_FILES_MANIFEST", "writeImagesManifest", "config", "images", "deviceSizes", "imageSizes", "sizes", "remotePatterns", "p", "protocol", "replace", "hostname", "makeRe", "port", "dot", "search", "localPatterns", "IMAGES_MANIFEST", "version", "STANDALONE_DIRECTORY", "writeStandaloneDirectory", "nextBuildSpan", "pageKeys", "denormalizedAppPages", "outputFileTracingRoot", "middlewareManifest", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "loadedEnvFiles", "appDir", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "copyTracedFiles", "pages", "file", "files", "reduce", "acc", "envFile", "includes", "push", "outputPath", "relative", "mkdir", "dirname", "recursive", "copyFile", "middlewareOutput", "recursiveCopy", "overwrite", "originalServerApp", "getNumberOfWorkers", "experimental", "cpus", "defaultConfig", "memoryBasedWorkersCount", "Math", "max", "min", "floor", "os", "freemem", "staticWorkerPath", "require", "resolve", "staticWorkerExposedMethods", "progress", "nodeOptions", "getParsedNodeOptionsWithoutInspect", "Worker", "logger", "numWorkers", "onActivity", "run", "onActivityAbort", "clear", "forkOptions", "env", "process", "NODE_OPTIONS", "formatNodeOptions", "enableWorkerThreads", "workerThreads", "exposedMethods", "writeFullyStaticExport", "dir", "enabledDirectories", "configOutDir", "exportApp", "default", "pagesWorker", "appWorker", "buildExport", "nextConfig", "silent", "outdir", "end", "getBuildId", "isGenerateMode", "generateBuildId", "nanoid", "reactProductionProfiling", "debugOutput", "runLint", "noMangling", "appDirOnly", "isTurbopack", "experimentalBuildMode", "traceUploadUrl", "isCompileMode", "NextBuildContext", "buildStartTime", "Date", "now", "loadedConfig", "trace", "undefined", "buildMode", "isTurboBuild", "String", "__NEXT_VERSION", "mappedPages", "traceFn", "loadEnvConfig", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "turborepoTraceAccess", "loadConfig", "PHASE_PRODUCTION_BUILD", "NEXT_DEPLOYMENT_ID", "deploymentId", "hasCustomExportOutput", "setGlobal", "exit", "info", "inlineStaticEnv", "flushAllTraces", "teardownTraceSubscriber", "populateStaticEnv", "customRoutes", "loadCustomRoutes", "headers", "rewrites", "redirects", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "hasRewrites", "length", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "telemetry", "Telemetry", "publicDir", "pagesDir", "findPagesDir", "app", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "isSrcDir", "startsWith", "hasPublicDir", "record", "eventCliSession", "webpackVersion", "cliCommand", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "isCustomServer", "turboFlag", "eventNextPlugins", "then", "events", "eventSwcPlugins", "envInfo", "experimentalFeatures", "getStartServerInfo", "logStartInfo", "networkUrl", "appUrl", "ignoreESLint", "Boolean", "eslint", "ignoreDuringBuilds", "shouldLint", "typeCheckingOptions", "distDirCreated", "err", "isError", "code", "isWriteable", "Error", "cleanDistDir", "recursiveDelete", "startTypeChecking", "error", "flush", "buildLintEvent", "featureName", "invocationCount", "eventName", "EVENT_BUILD_FEATURE_USAGE", "payload", "validFile<PERSON><PERSON><PERSON>", "createValidFileMatcher", "pageExtensions", "providedPagePaths", "NEXT_PRIVATE_PAGE_PATHS", "pagesPaths", "recursiveReadDir", "pathnameFilter", "isPageFile", "middlewareDetectionRegExp", "RegExp", "MIDDLEWARE_FILENAME", "instrumentationHookDetectionRegExp", "INSTRUMENTATION_HOOK_FILENAME", "rootDir", "rootPaths", "Array", "from", "getFilesInDir", "some", "include", "test", "sortByPageExts", "hasMiddlewareFile", "previewProps", "previewModeId", "crypto", "randomBytes", "toString", "previewModeSigningKey", "previewModeEncryptionKey", "createPagesMapping", "isDev", "pagesType", "PAGE_TYPES", "PAGES", "pagePaths", "mappedAppPages", "providedAppPaths", "NEXT_PRIVATE_APP_PATHS", "appPaths", "absolutePath", "isAppRouterPage", "isRootNotFound", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "APP", "mappedRootPaths", "ROOT", "pagesPageKeys", "conflictingAppPagePaths", "appPageKeys", "appKey", "normalizedAppPageKey", "normalizeAppPath", "pagePath", "appPath", "add", "generateInterceptionRoutesRewrites", "basePath", "totalAppPagesCount", "numConflictingAppPaths", "conflictingPublicFiles", "hasPages404", "PAGES_DIR_ALIAS", "hasApp404", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "hasCustomErrorPage", "hasPublicUnderScoreNextDir", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "hasPublicPageFile", "fileExists", "FileType", "File", "numConflicting", "nestedReservedPages", "match", "restrictedRedirectPaths", "isAppDynamicIOEnabled", "dynamicIO", "isAuthInterruptsEnabled", "authInterrupts", "isAppPPREnabled", "checkIsAppPPREnabled", "ppr", "routesManifestPath", "ROUTES_MANIFEST", "routesManifest", "sortedRoutes", "getSortedRoutes", "staticRoutes", "isDynamicRoute", "isReservedPage", "pages404", "caseSensitive", "caseSensitiveRoutes", "r", "buildCustomRoute", "dataRoutes", "i18n", "rsc", "header", "RSC_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "prefetch<PERSON><PERSON><PERSON>", "didPostponeHeader", "NEXT_DID_POSTPONE_HEADER", "contentTypeHeader", "RSC_CONTENT_TYPE_HEADER", "suffix", "RSC_SUFFIX", "prefetchSuffix", "RSC_PREFETCH_SUFFIX", "prefetchSegmentHeader", "prefetchSegmentSuffix", "RSC_SEGMENT_SUFFIX", "prefetchSegmentDirSuffix", "RSC_SEGMENTS_DIR_SUFFIX", "rewriteHeaders", "pathHeader", "NEXT_REWRITTEN_PATH_HEADER", "query<PERSON>eader", "NEXT_REWRITTEN_QUERY_HEADER", "skipMiddlewareUrlNormalize", "chain", "NEXT_RESUME_HEADER", "clientRouterFilters", "clientRouterFilter", "nonInternalRedirects", "internal", "createClientRouterFilter", "clientRouterFilterRedirects", "clientRouterFilterAllowedRate", "recordFrameworkVersion", "updateBuildDiagnostics", "buildStage", "pagesManifestPath", "PAGES_MANIFEST", "buildTraceContext", "buildTracesPromise", "useBuildWorker", "webpackBuildWorker", "webpack", "runServerAndEdgeInParallel", "parallelServerCompiles", "collectServerBuildTracesInParallel", "parallelServerBuildTraces", "setAttribute", "traceMemoryUsage", "buildOptions", "shutdownPromise", "Promise", "duration", "compilerDuration", "rest", "turbopackBuild", "NEXT_TURBOPACK_USE_WORKER", "durationString", "durationToString", "event", "eventBuildCompleted", "bundler", "durationInSeconds", "round", "serverBuildPromise", "webpackBuild", "res", "buildTraceWorker", "collectBuildTraces", "edgeRuntimeRoutes", "collectRoutesUsingEdgeRuntime", "Map", "hasSsrAmpPages", "catch", "edgeBuildPromise", "getBundlerForTelemetry", "postCompileSpinner", "createSpinner", "buildManifestPath", "BUILD_MANIFEST", "appBuildManifestPath", "APP_BUILD_MANIFEST", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "ssgStaticFallbackPages", "ssgBlockingFallbackPages", "invalidPages", "hybridAmpPages", "serverPropsPages", "additionalPaths", "staticPaths", "prospectiveRenders", "appNormalizedPaths", "fallbackModes", "appDefaultConfigs", "pageInfos", "pagesManifest", "buildManifest", "appBuildManifest", "appPathRoutes", "appPathsManifest", "APP_PATHS_MANIFEST", "key", "APP_PATH_ROUTES_MANIFEST", "NEXT_PHASE", "worker", "analysisBegin", "hrtime", "staticCheckSpan", "functionsConfigManifest", "functions", "customAppGetInitialProps", "namedExports", "isNextImageImported", "hasNonStaticErrorPage", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "runtimeEnvConfig", "sriEnabled", "sri", "algorithm", "nonStaticErrorPageSpan", "errorPageHasCustomGetInitialProps", "hasCustomGetInitialProps", "checkingApp", "errorPageStaticResult", "isPageStatic", "httpAgentOptions", "defaultLocale", "nextConfigOutput", "output", "pprConfig", "cacheLifeProfiles", "cacheLife", "appPageToCheck", "customAppGetInitialPropsPromise", "namedExportsPromise", "getDefinedNamedExports", "computedManifestData", "computeFromManifest", "gzipSize", "MIDDLEWARE_MANIFEST", "actionManifest", "SERVER_REFERENCE_MANIFEST", "entriesWithAction", "id", "node", "entry", "workers", "edge", "all", "pageType", "checkPageSpan", "actualPage", "normalizePagePath", "size", "totalSize", "getJsPageSizeInKb", "isRoutePPREnabled", "isSSG", "isStatic", "isServerComponent", "isHybridAmp", "ssgPageRoutes", "find", "normalizePathSep", "originalAppPath", "originalPath", "normalizedPath", "pageFilePath", "isAppBuiltinNotFoundPage", "isInsideAppDir", "staticInfo", "getStaticInfoIncludingLayouts", "runtime", "maxDuration", "pageRuntime", "RSC_MODULE_TYPES", "client", "edgeInfo", "isEdgeRuntime", "manifest<PERSON>ey", "isPageStaticSpan", "workerResult", "parentId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "isrFlushToDisk", "maxMemoryCacheSize", "cacheMaxMemorySize", "set", "warnOnce", "isDynamic", "prerenderedRoutes", "appConfig", "revalidate", "hasGenerateStaticParams", "encodedPathname", "fallbackRouteParams", "fallbackMode", "prerenderFallbackMode", "fallbackRootParams", "dynamic", "hasStaticProps", "isAmpOnly", "FallbackMode", "BLOCKING_STATIC_RENDER", "PRERENDER", "hasServerProps", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "delete", "STATIC_STATUS_PAGES", "message", "initialCacheControl", "pageDuration", "ssgPageDurations", "hasEmptyPrelude", "hadUnsupportedValue", "errorPageResult", "nonStaticErrorPage", "returnValue", "stopAndPersist", "bold", "yellow", "instrumentationHookEntryFiles", "requiredServerFilesManifest", "normalizedCacheHandlers", "value", "serverFilesManifest", "configFile", "compress", "trustHostHeader", "isExperimentalCompile", "relativeAppDir", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "REACT_LOADABLE_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "DYNAMIC_CSS_MANIFEST", "BUILD_ID_FILE", "NEXT_FONT_MANIFEST", "nonNullable", "ignore", "middlewareFile", "matchers", "middleware", "regexp", "originalSource", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "buildDataRoute", "useStaticPages404", "pg", "writeBuildId", "optimizeCss", "globOrig", "cssFilePaths", "reject", "features", "nextScriptWorkers", "isPersistentCachingEnabled", "feature", "notFoundRoutes", "preview", "tbdPrerenderRoutes", "usedStaticStatusPages", "for<PERSON>ach", "has", "hasPages500", "useDefaultStatic500", "combinedPages", "isApp404Static", "hasStaticApp404", "staticGenerationSpan", "detectConflictingPaths", "exportConfig", "exportPathMap", "defaultMap", "_pagesFallback", "_ssgPath", "get", "isDynamicError", "checkIsRoutePPREnabled", "_fallbackRouteParams", "_isDynamicError", "_isAppDir", "_isRoutePPREnabled", "values", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "_isProspectiveRender", "isSsg", "<PERSON><PERSON><PERSON><PERSON>", "locale", "_locale", "exportResult", "statusMessage", "getCacheControl", "exportPath", "defaultRevalidate", "cacheControl", "by<PERSON><PERSON>", "expire", "expireTime", "NEXT_SSG_FETCH_METRICS", "recordFetchMetrics", "writeTurborepoAccessTraceResult", "traces", "turborepoAccessTraceResults", "ssgNotFoundPaths", "serverBundle", "getPagePath", "unlink", "InvariantError", "hasRevalidateZero", "isAppRouteHandler", "isAppRouteRoute", "htmlBotsRegexString", "htmlLimitedBots", "HTML_LIMITED_BOT_UA_RE_STRING", "bypassFor", "type", "ACTION_HEADER", "unknown<PERSON>rerender<PERSON><PERSON><PERSON>", "knownPrerenderRoutes", "prerenderedRoute", "getSortedRouteObjects", "UNDERSCORE_NOT_FOUND_ROUTE", "metadata", "hasPostponed", "normalizedRoute", "dataRoute", "posix", "prefetchDataRoute", "meta", "collectMeta", "initialStatus", "status", "initialHeaders", "renderingMode", "RenderingMode", "PARTIALLY_STATIC", "STATIC", "experimentalPPR", "experimentalBypassFor", "initialRevalidateSeconds", "initialExpireSeconds", "allow<PERSON>eader", "NOT_FOUND", "segmentPaths", "dynamicRoute", "prefetchSegmentDataRoutes", "segmentPath", "result", "buildPrefetchSegmentDataRoute", "isDynamicAppRoute", "fallbackCacheControl", "fallbackModeToFallbackField", "fallbackRevalidate", "fallbackExpire", "fallback<PERSON><PERSON><PERSON>", "fallbackHeaders", "fallbackSourceRoute", "dataRouteRegex", "includeSuffix", "excludeOptionalTrailingSlash", "prefetchDataRouteRegex", "moveExportedPage", "originPage", "ext", "additionalSsgFile", "orig", "relativeDest", "slice", "split", "dest", "isNotFound", "rename", "localeExt", "extname", "relativeDestNoPages", "curPath", "updatedRelativeDest", "updatedOrig", "updatedDest", "moveExportedAppNotFoundTo404", "isStaticSsgFallback", "hasAmp", "pageInfo", "durationInfo", "byPage", "durationsByPath", "hasHtmlOutput", "ampPage", "localePage", "pageFile", "rm", "force", "postBuildSpinner", "buildTracesSpinner", "analysisEnd", "eventBuildOptimize", "staticPageCount", "staticPropsPageCount", "serverPropsPageCount", "ssrPageCount", "hasStatic404", "hasReportWebVitals", "rewritesCount", "headersCount", "redirectsCount", "headersWithHasCount", "rewritesWithHasCount", "redirectsWithHasCount", "middlewareCount", "telemetryState", "eventBuildFeatureUsage", "usages", "eventPackageUsedInGetServerSideProps", "packagesUsedInServerSideProps", "useCacheTracker", "tbdRoute", "fetchCacheKeyPrefix", "allowedRevalidateHeaderKeys", "EXPORT_MARKER", "hasExportPathMap", "exportTrailingSlash", "trailingSlash", "EXPORT_DETAIL", "verifyPartytownSetup", "printCustomRoutes", "printTreeView", "distPath", "e", "traceGlobals", "eventBuildFailed", "errorCode", "getErrorCodeForTelemetry", "lockfilePatchPromise", "cur", "__NEXT_TEST_MODE", "warnAboutTurbopackBuilds", "uploadTrace", "mode", "projectDir", "isTurboSession", "sync", "warningStr", "turbopackPersistentCaching", "NEXT_RSPACK", "extractNextErrorCode", "name"], "mappings": ";;;;;;;;;;;;;;;IAuuBgBA,kBAAkB;eAAlBA;;IA0EhB,OAo3FC;eAp3F6BC;;;QA1yBvB;qBAE4C;4BACtB;+DACV;2BACI;oBACoB;2DAC5B;wBACQ;8BACO;gEACV;+DACD;0BACI;6DACN;2BAiBV;4BAC8B;8BACR;0EAGtB;6BAQqB;iCACI;sCACK;4BA+B9B;uBAKA;+DAEgB;mCAEW;yBACN;gEACG;sCAKxB;wBAWA;yBAEmB;mCACU;yBAK7B;2BACoB;iCACK;6BACJ;6DACP;gEACK;uBACkC;wBAYrD;8BAIsB;qCACO;gEAChB;+BAEU;+BACA;kCACG;qBAC6B;4BAC3B;+BACL;4BACE;0BACC;kCAW1B;8BACsB;8BACsB;kCAClB;iCACD;0CACS;8BACF;2BACL;oDACiB;gCAEpB;oCACI;gCAEJ;kCAKxB;4BAC0C;wBAEX;kCACL;wBACA;uCACW;oEAEpB;qBAIjB;0BACmD;+BAC5B;gCACD;wBAItB;gCACwB;uBACe;+CAKvC;gCAEwB;wBACY;iCACX;2BACE;kCACD;wBACJ;qCACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HrC;;;CAGC,GACD,MAAMC,kBAA4B;IAChC;IACAC,8BAAmB;IACnBC,sCAA2B;IAC3BC,qDAA0C;IAC1CC,6CAAkC;IAClCC,iDAAsC;CACvC;AAiGD,SAASC,YAAYC,IAAY;IAC/B,MAAMC,aAAaC,IAAAA,8BAAkB,EAACF,MAAM;QAC1CG,iBAAiB;IACnB;IACA,OAAO;QACLH;QACAI,OAAOC,IAAAA,qCAAmB,EAACJ,WAAWK,EAAE,CAACC,MAAM;QAC/CC,WAAWP,WAAWO,SAAS;QAC/BC,YAAYR,WAAWQ,UAAU;IACnC;AACF;AAEA,SAASC,YAAYC,OAAe;IAClC,MAAMC,WAAWC,aAAI,CAACC,IAAI,CAACH,SAAS;IACpC,IAAII,QAAcC,IAAI,IAAI,CAACD,QAAcE,cAAc,EAAE;QACvD,MAAMC,WAAWC,IAAAA,cAAU,EAACP;QAE5B,IAAI,CAACM,UAAU;YACb,kGAAkG;YAClG,sBAAsB;YACtBE,QAAQC,GAAG,CACT,GAAGC,KAAIC,QAAQ,CAACC,IAAI,CAAC,+HAA+H,CAAC;QAEzJ;IACF;IACA,OAAOZ;AACT;AAEA,eAAea,cAAcC,QAAgB,EAAEC,OAAe;IAC5D,MAAMC,YAAE,CAACC,SAAS,CAACH,UAAUC,SAAS;AACxC;AAEA,SAASG,aAAaJ,QAAgB;IACpC,OAAOE,YAAE,CAACG,QAAQ,CAACL,UAAU;AAC/B;AAEA,eAAeM,cACbN,QAAgB,EAChBO,QAAW;IAEX,MAAMR,cAAcC,UAAUQ,IAAAA,8BAAc,EAACD;AAC/C;AAEA,eAAeE,aAA+BT,QAAgB;IAC5D,OAAOU,KAAKC,KAAK,CAAC,MAAMP,aAAaJ;AACvC;AAEA,eAAeY,uBACb3B,OAAe,EACfsB,QAAyC;IAEzC,MAAMD,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAAS4B,8BAAkB,GAAGN;AAC9D;AAEA,eAAeO,uBACbC,iBAAkD,EAClD,EACEC,OAAO,EACP/B,OAAO,EACPgC,OAAO,EAKR;IAED,MAAMC,WAAW,IAAIC,IACnB;WACKC,OAAOC,OAAO,CAACN,kBAAkBO,MAAM,CACxC,4BAA4B;SAC3BC,MAAM,CAAC,CAAC,GAAG,EAAEC,QAAQ,EAAE,CAAC,GAAKA,YAAY,MACzCC,GAAG,CAAC,CAAC,CAACC,MAAM,GAAKC,IAAAA,wCAAmB,EAACD,OAAOT,SAASW,QAAQ;WAC7DR,OAAOS,IAAI,CAACd,kBAAkBe,aAAa;KAC/C,CAACC,IAAI;IAGR,MAAMC,2BAA2B,CAAC,oBAAoB,EAAEC,IAAAA,gBAAO,EAC7Df,UACA,iDAAiD,CAAC;IAEpD,MAAMnB,cACJZ,aAAI,CAACC,IAAI,CAACH,SAASiD,oCAAwB,EAAElB,SAAS,oBACtDgB;AAEJ;AAmBA,eAAeG,6BACblD,OAAe,EACfsB,QAAiC;IAEjC,MAAMD,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEC,qCAAyB,GAC9D9B;AAEJ;AAWA,eAAe+B,iCACbrD,OAAe,EACfsD,mBAAgD;IAEhD,MAAMjC,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASuD,iCAAqB,GACxCD;AAEJ;AAEA,eAAeE,oBACbxD,OAAe,EACfyD,MAA0B;QAODA,gBAUrBA;IAfJ,MAAMC,SAAS;QAAE,GAAGD,OAAOC,MAAM;IAAC;IAClC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAE,GAAGF;IAClCA,OAAeG,KAAK,GAAG;WAAIF;WAAgBC;KAAW;IAExD,8DAA8D;IAC9DF,OAAOI,cAAc,GAAG,AAACL,CAAAA,CAAAA,2BAAAA,iBAAAA,OAAQC,MAAM,qBAAdD,eAAgBK,cAAc,KAAI,EAAE,AAAD,EAAGtB,GAAG,CAAC,CAACuB;YAExDA;eAF+D;YACzE,iEAAiE;YACjEC,QAAQ,GAAED,cAAAA,EAAEC,QAAQ,qBAAVD,YAAYE,OAAO,CAAC,MAAM;YACpCC,UAAUC,IAAAA,iBAAM,EAACJ,EAAEG,QAAQ,EAAEtE,MAAM;YACnCwE,MAAML,EAAEK,IAAI;YACZzB,UAAUwB,IAAAA,iBAAM,EAACJ,EAAEpB,QAAQ,IAAI,MAAM;gBAAE0B,KAAK;YAAK,GAAGzE,MAAM;YAC1D0E,QAAQP,EAAEO,MAAM;QAClB;;IAEA,oEAAoE;IACpE,IAAIb,2BAAAA,kBAAAA,OAAQC,MAAM,qBAAdD,gBAAgBc,aAAa,EAAE;QACjCb,OAAOa,aAAa,GAAGd,OAAOC,MAAM,CAACa,aAAa,CAAC/B,GAAG,CAAC,CAACuB,IAAO,CAAA;gBAC7D,gEAAgE;gBAChEpB,UAAUwB,IAAAA,iBAAM,EAACJ,EAAEpB,QAAQ,IAAI,MAAM;oBAAE0B,KAAK;gBAAK,GAAGzE,MAAM;gBAC1D0E,QAAQP,EAAEO,MAAM;YAClB,CAAA;IACF;IAEA,MAAMjD,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAASwE,2BAAe,GAAG;QACvDC,SAAS;QACTf;IACF;AACF;AAEA,MAAMgB,uBAAuB;AAC7B,eAAeC,yBACbC,aAAmB,EACnB5E,OAAe,EACf6E,QAAwD,EACxDC,oBAA0C,EAC1CC,qBAA6B,EAC7BzB,mBAAgD,EAChD0B,kBAAsC,EACtCC,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB,EACxBC,cAA8B,EAC9BC,MAA0B;IAE1B,MAAMT,cACHU,UAAU,CAAC,8BACXC,YAAY,CAAC;QACZ,MAAMC,IAAAA,uBAAe,EACnB,kFAAkF;QAClFlC,oBAAoB+B,MAAM,EAC1BrF,SACA6E,SAASY,KAAK,EACdX,sBACAC,uBACAzB,oBAAoBG,MAAM,EAC1BuB,oBACAC,mBACAC,wBACAC;QAGF,KAAK,MAAMO,QAAQ;eACdpC,oBAAoBqC,KAAK;YAC5BzF,aAAI,CAACC,IAAI,CAACmD,oBAAoBG,MAAM,CAACzD,OAAO,EAAEuD,iCAAqB;eAChE6B,eAAeQ,MAAM,CAAW,CAACC,KAAKC;gBACvC,IAAI;oBAAC;oBAAQ;iBAAkB,CAACC,QAAQ,CAACD,QAAQ5F,IAAI,GAAG;oBACtD2F,IAAIG,IAAI,CAACF,QAAQ5F,IAAI;gBACvB;gBACA,OAAO2F;YACT,GAAG,EAAE;SACN,CAAE;YACD,kFAAkF;YAClF,MAAM9E,WAAWb,aAAI,CAACC,IAAI,CAACmD,oBAAoB+B,MAAM,EAAEK;YACvD,MAAMO,aAAa/F,aAAI,CAACC,IAAI,CAC1BH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuBhE;YAEvC,MAAME,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACH,aAAa;gBACvCI,WAAW;YACb;YACA,MAAMpF,YAAE,CAACqF,QAAQ,CAACvF,UAAUkF;QAC9B;QAEA,IAAIhB,mBAAmB;YACrB,MAAMsB,mBAAmBrG,aAAI,CAACC,IAAI,CAChCH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB;YAGF,MAAMlC,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACG,mBAAmB;gBAAEF,WAAW;YAAK;YACjE,MAAMpF,YAAE,CAACqF,QAAQ,CACfpG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE,kBACrCoD;QAEJ;QAEA,MAAMC,IAAAA,4BAAa,EACjBtG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE,UACrCjD,aAAI,CAACC,IAAI,CACPH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB,UAEF;YAAEsD,WAAW;QAAK;QAEpB,IAAIpB,QAAQ;YACV,MAAMqB,oBAAoBxG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE;YAC/D,IAAI3C,IAAAA,cAAU,EAACkG,oBAAoB;gBACjC,MAAMF,IAAAA,4BAAa,EACjBE,mBACAxG,aAAI,CAACC,IAAI,CACPH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB,QAEF;oBAAEsD,WAAW;gBAAK;YAEtB;QACF;IACF;AACJ;AAEA,SAASE,mBAAmBlD,MAA0B;IACpD,IACEA,OAAOmD,YAAY,CAACC,IAAI,IACxBpD,OAAOmD,YAAY,CAACC,IAAI,KAAKC,2BAAa,CAACF,YAAY,CAAEC,IAAI,EAC7D;QACA,OAAOpD,OAAOmD,YAAY,CAACC,IAAI;IACjC;IAEA,IAAIpD,OAAOmD,YAAY,CAACG,uBAAuB,EAAE;QAC/C,OAAOC,KAAKC,GAAG,CACbD,KAAKE,GAAG,CAACzD,OAAOmD,YAAY,CAACC,IAAI,IAAI,GAAGG,KAAKG,KAAK,CAACC,WAAE,CAACC,OAAO,KAAK,OAClE,iCAAiC;QACjC;IAEJ;IAEA,IAAI5D,OAAOmD,YAAY,CAACC,IAAI,EAAE;QAC5B,OAAOpD,OAAOmD,YAAY,CAACC,IAAI;IACjC;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEA,MAAMS,mBAAmBC,QAAQC,OAAO,CAAC;AACzC,MAAMC,6BAA6B;IACjC;IACA;IACA;IACA;CACD;AAEM,SAAS7I,mBACd6E,MAA0B,EAC1BiE,QAGC;IAED,2DAA2D;IAC3D,2DAA2D;IAC3D,MAAMC,cAAcC,IAAAA,0CAAkC;IACtD,OAAOD,WAAW,CAAC,qBAAqB;IACxC,OAAOA,WAAW,CAAC,qBAAqB;IAExC,OAAO,IAAIE,cAAM,CAACP,kBAAkB;QAClCQ,QAAQnH;QACRoH,YAAYpB,mBAAmBlD;QAC/BuE,YAAY;YACVN,4BAAAA,SAAUO,GAAG;QACf;QACAC,iBAAiB;YACfR,4BAAAA,SAAUS,KAAK;QACjB;QACAC,aAAa;YACXC,KAAK;gBAAE,GAAGC,QAAQD,GAAG;gBAAEE,cAAcC,IAAAA,yBAAiB,EAACb;YAAa;QACtE;QACAc,qBAAqBhF,OAAOmD,YAAY,CAAC8B,aAAa;QACtDC,gBAAgBlB;IAClB;AACF;AAEA,eAAemB,uBACbnF,MAA0B,EAC1BoF,GAAW,EACXC,kBAA0C,EAC1CC,YAAoB,EACpBnE,aAAmB;IAEnB,MAAMoE,YAAYzB,QAAQ,aACvB0B,OAAO;IAEV,MAAMC,cAActK,mBAAmB6E;IACvC,MAAM0F,YAAYvK,mBAAmB6E;IAErC,MAAMuF,UACJH,KACA;QACEO,aAAa;QACbC,YAAY5F;QACZqF;QACAQ,QAAQ;QACRC,QAAQrJ,aAAI,CAACC,IAAI,CAAC0I,KAAKE;QACvBhB,YAAYpB,mBAAmBlD;IACjC,GACAmB;IAGFsE,YAAYM,GAAG;IACfL,UAAUK,GAAG;AACf;AAEA,eAAeC,WACbC,cAAuB,EACvB1J,OAAe,EACf4E,aAAmB,EACnBnB,MAA0B;IAE1B,IAAIiG,gBAAgB;QAClB,OAAO,MAAMzI,YAAE,CAACG,QAAQ,CAAClB,aAAI,CAACC,IAAI,CAACH,SAAS,aAAa;IAC3D;IACA,OAAO,MAAM4E,cACVU,UAAU,CAAC,oBACXC,YAAY,CAAC,IAAMoE,IAAAA,gCAAe,EAAClG,OAAOkG,eAAe,EAAEC,gBAAM;AACtE;AAEe,eAAe/K,MAC5BgK,GAAW,EACXgB,2BAA2B,KAAK,EAChCC,cAAc,KAAK,EACnBC,UAAU,IAAI,EACdC,aAAa,KAAK,EAClBC,aAAa,KAAK,EAClBC,cAAc,KAAK,EACnBC,qBAA0E,EAC1EC,cAAkC;IAElC,MAAMC,gBAAgBF,0BAA0B;IAChD,MAAMT,iBAAiBS,0BAA0B;IACjDG,8BAAgB,CAACD,aAAa,GAAGA;IACjC,MAAME,iBAAiBC,KAAKC,GAAG;IAE/B,IAAIC;IACJ,IAAI;QACF,MAAM9F,gBAAgB+F,IAAAA,YAAK,EAAC,cAAcC,WAAW;YACnDC,WAAWV;YACXW,cAAcC,OAAOb;YACrBzF,SAAS6D,QAAQD,GAAG,CAAC2C,cAAc;QACrC;QAEAV,8BAAgB,CAAC1F,aAAa,GAAGA;QACjC0F,8BAAgB,CAACzB,GAAG,GAAGA;QACvByB,8BAAgB,CAACL,UAAU,GAAGA;QAC9BK,8BAAgB,CAACT,wBAAwB,GAAGA;QAC5CS,8BAAgB,CAACN,UAAU,GAAGA;QAE9B,MAAMpF,cAAcW,YAAY,CAAC;gBA+XX0F;YA9XpB,4EAA4E;YAC5E,MAAM,EAAE7F,cAAc,EAAE,GAAGR,cACxBU,UAAU,CAAC,eACX4F,OAAO,CAAC,IAAMC,IAAAA,kBAAa,EAACtC,KAAK,OAAOlI;YAC3C2J,8BAAgB,CAAClF,cAAc,GAAGA;YAElC,MAAMgG,6BAA6B,IAAIC,gDAA0B;YACjE,MAAM5H,SAA6B,MAAMmB,cACtCU,UAAU,CAAC,oBACXC,YAAY,CAAC,IACZ+F,IAAAA,0CAAoB,EAClB,IACEC,IAAAA,eAAU,EAACC,kCAAsB,EAAE3C,KAAK;wBACtC,sCAAsC;wBACtCS,QAAQ;wBACRO;oBACF,IACFuB;YAGNV,eAAejH;YAEf6E,QAAQD,GAAG,CAACoD,kBAAkB,GAAGhI,OAAOiI,YAAY,IAAI;YACxDpB,8BAAgB,CAAC7G,MAAM,GAAGA;YAE1B,IAAIsF,eAAe;YACnB,IAAI4C,IAAAA,6BAAqB,EAAClI,SAAS;gBACjCsF,eAAetF,OAAOzD,OAAO;gBAC7ByD,OAAOzD,OAAO,GAAG;YACnB;YACA,MAAMA,UAAUE,aAAI,CAACC,IAAI,CAAC0I,KAAKpF,OAAOzD,OAAO;YAC7CsK,8BAAgB,CAACtK,OAAO,GAAGA;YAC3B4L,IAAAA,gBAAS,EAAC,SAASJ,kCAAsB;YACzCI,IAAAA,gBAAS,EAAC,WAAW5L;YAErB,MAAM+B,UAAU,MAAM0H,WACpBC,gBACA1J,SACA4E,eACAnB;YAEF6G,8BAAgB,CAACvI,OAAO,GAAGA;YAE3B,IAAIoI,0BAA0B,gBAAgB;gBAC5C,IAAID,aAAa;oBACfvJ,KAAIE,IAAI,CAAC;oBACTyH,QAAQuD,IAAI,CAAC;gBACf;gBACAlL,KAAImL,IAAI,CAAC;gBACT,MAAMlH,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMwG,IAAAA,gCAAe,EAAC;wBACpB/L;wBACAyD;oBACF;gBACF;gBAEF9C,KAAImL,IAAI,CAAC;gBACT,MAAME,IAAAA,qBAAc;gBACpBC,IAAAA,4BAAuB;gBACvB3D,QAAQuD,IAAI,CAAC;YACf;YAEA,yDAAyD;YACzD,yCAAyC;YACzC,IAAIxB,iBAAiBX,gBAAgB;gBACnCwC,IAAAA,4BAAiB,EAACzI;YACpB;YAEA,MAAM0I,eAA6B,MAAMvH,cACtCU,UAAU,CAAC,sBACXC,YAAY,CAAC,IAAM6G,IAAAA,yBAAgB,EAAC3I;YAEvC,MAAM,EAAE4I,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGJ;YACzC,MAAMK,mBAA8B;mBAC/BF,SAASG,WAAW;mBACpBH,SAASI,UAAU;mBACnBJ,SAASK,QAAQ;aACrB;YACD,MAAMC,cAAcJ,iBAAiBK,MAAM,GAAG;YAC9CvC,8BAAgB,CAACsC,WAAW,GAAGA;YAC/BtC,8BAAgB,CAACwC,gBAAgB,GAAGrJ,OAAOsJ,iBAAiB;YAC5DzC,8BAAgB,CAAC0C,iBAAiB,GAAGvJ,OAAOwJ,kBAAkB;YAE9D,MAAMhN,WAAWF,YAAYC;YAE7B,MAAMkN,YAAY,IAAIC,kBAAS,CAAC;gBAAEnN;YAAQ;YAE1C4L,IAAAA,gBAAS,EAAC,aAAasB;YAEvB,MAAME,YAAYlN,aAAI,CAACC,IAAI,CAAC0I,KAAK;YACjC,MAAM,EAAEwE,QAAQ,EAAEhI,MAAM,EAAE,GAAGiI,IAAAA,0BAAY,EAACzE;YAC1CyB,8BAAgB,CAAC+C,QAAQ,GAAGA;YAC5B/C,8BAAgB,CAACjF,MAAM,GAAGA;YAE1B,MAAMyD,qBAA6C;gBACjDyE,KAAK,OAAOlI,WAAW;gBACvBI,OAAO,OAAO4H,aAAa;YAC7B;YAEA,mDAAmD;YACnD,wFAAwF;YACxF,MAAMG,gBAAgB,MAAMC,IAAAA,kDAA2B,EAAC;gBACtDC,SAAS;gBACT1N;YACF;YACAsK,8BAAgB,CAACkD,aAAa,GAAGA;YAEjC,MAAMG,WAAWzN,aAAI,CAClBgG,QAAQ,CAAC2C,KAAKwE,YAAYhI,UAAU,IACpCuI,UAAU,CAAC;YACd,MAAMC,eAAerN,IAAAA,cAAU,EAAC4M;YAEhCF,UAAUY,MAAM,CACdC,IAAAA,uBAAe,EAAClF,KAAKpF,QAAQ;gBAC3BuK,gBAAgB;gBAChBC,YAAY;gBACZN;gBACAO,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;oBAAEC,KAAKvF;gBAAI;gBACnDwF,gBAAgB;gBAChBC,WAAW;gBACXjB,UAAU,CAAC,CAACA;gBACZhI,QAAQ,CAAC,CAACA;YACZ;YAGFkJ,IAAAA,wBAAgB,EAACrO,aAAI,CAACsH,OAAO,CAACqB,MAAM2F,IAAI,CAAC,CAACC,SACxCvB,UAAUY,MAAM,CAACW;YAGnBC,IAAAA,2BAAe,EAACxO,aAAI,CAACsH,OAAO,CAACqB,MAAMpF,QAAQ+K,IAAI,CAAC,CAACC,SAC/CvB,UAAUY,MAAM,CAACW;YAGnB,qDAAqD;YACrD,MAAM,EAAEE,OAAO,EAAEC,oBAAoB,EAAE,GAAG,MAAMC,IAAAA,8BAAkB,EAChEhG,KACA;YAEFiG,IAAAA,wBAAY,EAAC;gBACXC,YAAY;gBACZC,QAAQ;gBACRL;gBACAC;YACF;YAEA,MAAMK,eAAeC,QAAQzL,OAAO0L,MAAM,CAACC,kBAAkB;YAC7D,MAAMC,aAAa,CAACJ,gBAAgBlF;YAEpC,MAAMuF,sBAA+D;gBACnEzG;gBACAxD;gBACAgI;gBACAtD;gBACAsF;gBACAJ;gBACA/B;gBACAtI;gBACAnB;gBACAxD;YACF;YAEA,MAAMsP,iBAAiB,MAAM3K,cAC1BU,UAAU,CAAC,mBACXC,YAAY,CAAC;gBACZ,IAAI;oBACF,MAAMtE,YAAE,CAACkF,KAAK,CAACnG,SAAS;wBAAEqG,WAAW;oBAAK;oBAC1C,OAAO;gBACT,EAAE,OAAOmJ,KAAK;oBACZ,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,IAAI,KAAK,SAAS;wBACxC,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YAEF,IAAI,CAACD,kBAAkB,CAAE,MAAMI,IAAAA,wBAAW,EAAC3P,UAAW;gBACpD,MAAM,qBAEL,CAFK,IAAI4P,MACR,iGADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAInM,OAAOoM,YAAY,IAAI,CAACnG,gBAAgB;gBAC1C,MAAMoG,IAAAA,gCAAe,EAAC9P,SAAS;YACjC;YAEA,sEAAsE;YACtE,oEAAoE;YACpE,aAAa;YACb,IAAI,CAACqF,UAAU,CAACgF,eACd,MAAM0F,IAAAA,4BAAiB,EAACT;YAE1B,IAAIjK,UAAU,mBAAmB5B,QAAQ;gBACvC9C,KAAIqP,KAAK,CACP;gBAEF,MAAM9C,UAAU+C,KAAK;gBACrB3H,QAAQuD,IAAI,CAAC;YACf;YAEA,MAAMqE,iBAAyC;gBAC7CC,aAAa;gBACbC,iBAAiBf,aAAa,IAAI;YACpC;YACAnC,UAAUY,MAAM,CAAC;gBACfuC,WAAWC,iCAAyB;gBACpCC,SAASL;YACX;YAEA,MAAMM,mBAAmBC,IAAAA,oCAAsB,EAC7ChN,OAAOiN,cAAc,EACrBrL;YAGF,MAAMsL,oBAA8BlP,KAAKC,KAAK,CAC5C4G,QAAQD,GAAG,CAACuI,uBAAuB,IAAI;YAGzC,IAAIC,aAAa3B,QAAQ5G,QAAQD,GAAG,CAACuI,uBAAuB,IACxDD,oBACA,CAAC1G,cAAcoD,WACb,MAAMzI,cAAcU,UAAU,CAAC,iBAAiBC,YAAY,CAAC,IAC3DuL,IAAAA,kCAAgB,EAACzD,UAAU;oBACzB0D,gBAAgBP,iBAAiBQ,UAAU;gBAC7C,MAEF,EAAE;YAER,MAAMC,4BAA4B,IAAIC,OACpC,CAAC,CAAC,EAAEC,8BAAmB,CAAC,MAAM,EAAE1N,OAAOiN,cAAc,CAACvQ,IAAI,CAAC,KAAK,EAAE,CAAC;YAGrE,MAAMiR,qCAAqC,IAAIF,OAC7C,CAAC,CAAC,EAAEG,wCAA6B,CAAC,MAAM,EAAE5N,OAAOiN,cAAc,CAACvQ,IAAI,CAClE,KACA,EAAE,CAAC;YAGP,MAAMmR,UAAUpR,aAAI,CAACC,IAAI,CAAEkN,YAAYhI,QAAU;YACjD,MAAMU,WAAW;gBACfkL;gBACAG;aACD;YAED,MAAMG,YAAYC,MAAMC,IAAI,CAAC,MAAMC,IAAAA,4BAAa,EAACJ,UAC9ChP,MAAM,CAAC,CAACoD,OAASK,SAAS4L,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAACnM,QACzD5C,IAAI,CAACgP,IAAAA,uBAAc,EAACrO,OAAOiN,cAAc,GACzClO,GAAG,CAAC,CAACkD,OAASxF,aAAI,CAACC,IAAI,CAACmR,SAAS5L,MAAMzB,OAAO,CAAC4E,KAAK;YAEvD,MAAM3D,yBAAyBqM,UAAUI,IAAI,CAAC,CAAC5N,IAC7CA,EAAEgC,QAAQ,CAACsL,wCAA6B;YAE1C,MAAMU,oBAAoBR,UAAUI,IAAI,CAAC,CAAC5N,IACxCA,EAAEgC,QAAQ,CAACoL,8BAAmB;YAGhC7G,8BAAgB,CAACpF,sBAAsB,GAAGA;YAE1C,MAAM8M,eAAkC;gBACtCC,eAAeC,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;gBAC/CC,uBAAuBH,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;gBACvDE,0BAA0BJ,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;YAC5D;YACA9H,8BAAgB,CAAC0H,YAAY,GAAGA;YAEhC,MAAM/G,cAAc,MAAMrG,cACvBU,UAAU,CAAC,wBACXC,YAAY,CAAC,IACZgN,IAAAA,2BAAkB,EAAC;oBACjBC,OAAO;oBACP9B,gBAAgBjN,OAAOiN,cAAc;oBACrC+B,WAAWC,qBAAU,CAACC,KAAK;oBAC3BC,WAAW/B;oBACXxD;oBACAhI;gBACF;YAEJiF,8BAAgB,CAACW,WAAW,GAAGA;YAE/B,IAAI4H;YACJ,IAAI/N;YAEJ,IAAIO,QAAQ;gBACV,MAAMyN,mBAA6BrR,KAAKC,KAAK,CAC3C4G,QAAQD,GAAG,CAAC0K,sBAAsB,IAAI;gBAGxC,IAAIC,WAAW9D,QAAQ5G,QAAQD,GAAG,CAAC0K,sBAAsB,IACrDD,mBACA,MAAMlO,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC,IACZuL,IAAAA,kCAAgB,EAACzL,QAAQ;wBACvB0L,gBAAgB,CAACkC,eACfzC,iBAAiB0C,eAAe,CAACD,iBACjC,8DAA8D;4BAC9D,gCAAgC;4BAChCzC,iBAAiB2C,cAAc,CAACF;wBAClCG,kBAAkB,CAACC,OAASA,KAAKzF,UAAU,CAAC;oBAC9C;gBAGRiF,iBAAiB,MAAMjO,cACpBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZgN,IAAAA,2BAAkB,EAAC;wBACjBK,WAAWI;wBACXR,OAAO;wBACPC,WAAWC,qBAAU,CAACY,GAAG;wBACzB5C,gBAAgBjN,OAAOiN,cAAc;wBACrCrD;wBACAhI;oBACF;gBAGJiF,8BAAgB,CAACuI,cAAc,GAAGA;YACpC;YAEA,MAAMU,kBAAkB,MAAMhB,IAAAA,2BAAkB,EAAC;gBAC/CC,OAAO;gBACP9B,gBAAgBjN,OAAOiN,cAAc;gBACrCkC,WAAWrB;gBACXkB,WAAWC,qBAAU,CAACc,IAAI;gBAC1BnG,UAAUA;gBACVhI;YACF;YACAiF,8BAAgB,CAACiJ,eAAe,GAAGA;YAEnC,MAAME,gBAAgBtR,OAAOS,IAAI,CAACqI;YAElC,MAAMyI,0BAAiE,EAAE;YACzE,MAAMC,cAAc,IAAIzR;YACxB,IAAI2Q,gBAAgB;gBAClB/N,uBAAuB3C,OAAOS,IAAI,CAACiQ;gBACnC,KAAK,MAAMe,UAAU9O,qBAAsB;oBACzC,MAAM+O,uBAAuBC,IAAAA,0BAAgB,EAACF;oBAC9C,MAAMG,WAAW9I,WAAW,CAAC4I,qBAAqB;oBAClD,IAAIE,UAAU;wBACZ,MAAMC,UAAUnB,cAAc,CAACe,OAAO;wBACtCF,wBAAwB1N,IAAI,CAAC;4BAC3B+N,SAAS9P,OAAO,CAAC,uBAAuB;4BACxC+P,QAAQ/P,OAAO,CAAC,yBAAyB;yBAC1C;oBACH;oBACA0P,YAAYM,GAAG,CAACJ;gBAClB;YACF;YAEA,MAAMb,WAAWxB,MAAMC,IAAI,CAACkC;YAC5B,2DAA2D;YAC3DrH,SAASG,WAAW,CAACzG,IAAI,IACpBkO,IAAAA,sEAAkC,EAAClB,UAAUvP,OAAO0Q,QAAQ;YAGjE7J,8BAAgB,CAACgC,QAAQ,GAAGA;YAE5B,MAAM8H,qBAAqBpB,SAASnG,MAAM;YAE1C,MAAMhI,WAAW;gBACfY,OAAOgO;gBACPlG,KAAKyF,SAASnG,MAAM,GAAG,IAAImG,WAAWpI;YACxC;YAEA,6DAA6D;YAC7D,IAAI,CAACV,aAAa;gBAChB,MAAMmK,yBAAyBX,wBAAwB7G,MAAM;gBAC7D,IAAIgG,kBAAkBwB,yBAAyB,GAAG;oBAChD1T,KAAIqP,KAAK,CACP,CAAC,6BAA6B,EAC5BqE,2BAA2B,IAAI,SAAS,SACzC,wDAAwD,CAAC;oBAE5D,KAAK,MAAM,CAACN,UAAUC,QAAQ,IAAIN,wBAAyB;wBACzD/S,KAAIqP,KAAK,CAAC,CAAC,GAAG,EAAE+D,SAAS,KAAK,EAAEC,QAAQ,CAAC,CAAC;oBAC5C;oBACA,MAAM9G,UAAU+C,KAAK;oBACrB3H,QAAQuD,IAAI,CAAC;gBACf;YACF;YAEA,MAAMyI,yBAAmC,EAAE;YAC3C,MAAMC,eAActJ,mBAAAA,WAAW,CAAC,OAAO,qBAAnBA,iBAAqB2C,UAAU,CAAC4G,0BAAe;YACnE,MAAMC,YAAY,CAAC,EAAC5B,kCAAAA,cAAgB,CAAC6B,4CAAgC,CAAC;YACtE,MAAMC,qBACJ1J,WAAW,CAAC,UAAU,CAAC2C,UAAU,CAAC4G,0BAAe;YAEnD,IAAI3G,cAAc;gBAChB,MAAM+G,6BAA6BpU,IAAAA,cAAU,EAC3CN,aAAI,CAACC,IAAI,CAACiN,WAAW;gBAEvB,IAAIwH,4BAA4B;oBAC9B,MAAM,qBAAyC,CAAzC,IAAIhF,MAAMiF,yCAA8B,GAAxC,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwC;gBAChD;YACF;YAEA,MAAMjQ,cACHU,UAAU,CAAC,6BACXC,YAAY,CAAC;gBACZ,iDAAiD;gBACjD,sDAAsD;gBACtD,IAAK,MAAMlG,QAAQ4L,YAAa;oBAC9B,MAAM6J,oBAAoB,MAAMC,IAAAA,sBAAU,EACxC7U,aAAI,CAACC,IAAI,CAACiN,WAAW/N,SAAS,MAAM,WAAWA,OAC/C2V,oBAAQ,CAACC,IAAI;oBAEf,IAAIH,mBAAmB;wBACrBR,uBAAuBtO,IAAI,CAAC3G;oBAC9B;gBACF;gBAEA,MAAM6V,iBAAiBZ,uBAAuBzH,MAAM;gBAEpD,IAAIqI,gBAAgB;oBAClB,MAAM,qBAML,CANK,IAAItF,MACR,CAAC,gCAAgC,EAC/BsF,mBAAmB,IAAI,SAAS,SACjC,uEAAuE,EAAEZ,uBAAuBnU,IAAI,CACnG,OACC,GALC,qBAAA;+BAAA;oCAAA;sCAAA;oBAMN;gBACF;YACF;YAEF,MAAMgV,sBAAsBtQ,SAASY,KAAK,CAACnD,MAAM,CAAC,CAACjD;gBACjD,OACEA,KAAK+V,KAAK,CAAC,iCAAiClV,aAAI,CAACkG,OAAO,CAAC/G,UAAU;YAEvE;YAEA,IAAI8V,oBAAoBtI,MAAM,EAAE;gBAC9BlM,KAAIE,IAAI,CACN,CAAC,4FAA4F,CAAC,GAC5FsU,oBAAoBhV,IAAI,CAAC,QACzB,CAAC,6EAA6E,CAAC;YAErF;YAEA,MAAMkV,0BAA0B;gBAAC;aAAS,CAAC7S,GAAG,CAAC,CAACuB,IAC9CN,OAAO0Q,QAAQ,GAAG,GAAG1Q,OAAO0Q,QAAQ,GAAGpQ,GAAG,GAAGA;YAG/C,MAAMuR,wBAAwBpG,QAAQzL,OAAOmD,YAAY,CAAC2O,SAAS;YACnE,MAAMC,0BAA0BtG,QAC9BzL,OAAOmD,YAAY,CAAC6O,cAAc;YAEpC,MAAMC,kBAAkBC,IAAAA,yBAAoB,EAAClS,OAAOmD,YAAY,CAACgP,GAAG;YAEpE,MAAMC,qBAAqB3V,aAAI,CAACC,IAAI,CAACH,SAAS8V,2BAAe;YAC7D,MAAMC,iBAAiCnR,cACpCU,UAAU,CAAC,4BACX4F,OAAO,CAAC;gBACP,MAAM8K,eAAeC,IAAAA,sBAAe,EAAC;uBAChCpR,SAASY,KAAK;uBACbZ,SAAS0I,GAAG,IAAI,EAAE;iBACvB;gBACD,MAAM1K,gBAAuD,EAAE;gBAC/D,MAAMqT,eAAqC,EAAE;gBAE7C,KAAK,MAAMzT,SAASuT,aAAc;oBAChC,IAAIG,IAAAA,qBAAc,EAAC1T,QAAQ;wBACzBI,cAAcmD,IAAI,CAAC5G,YAAYqD;oBACjC,OAAO,IAAI,CAAC2T,IAAAA,sBAAc,EAAC3T,QAAQ;wBACjCyT,aAAalQ,IAAI,CAAC5G,YAAYqD;oBAChC;gBACF;gBAEA,OAAO;oBACLgC,SAAS;oBACT4R,UAAU;oBACVC,eAAe,CAAC,CAAC7S,OAAOmD,YAAY,CAAC2P,mBAAmB;oBACxDpC,UAAU1Q,OAAO0Q,QAAQ;oBACzB5H,WAAWA,UAAU/J,GAAG,CAAC,CAACgU,IACxBC,IAAAA,kCAAgB,EAAC,YAAYD,GAAGnB;oBAElChJ,SAASA,QAAQ7J,GAAG,CAAC,CAACgU,IAAMC,IAAAA,kCAAgB,EAAC,UAAUD;oBACvD3T;oBACAqT;oBACAQ,YAAY,EAAE;oBACdC,MAAMlT,OAAOkT,IAAI,IAAI/L;oBACrBgM,KAAK;wBACHC,QAAQC,4BAAU;wBAClB,yFAAyF;wBACzF,4DAA4D;wBAC5DC,YAAY,GAAGD,4BAAU,CAAC,EAAE,EAAEE,+CAA6B,CAAC,EAAE,EAAEC,6CAA2B,CAAC,EAAE,EAAEC,qDAAmC,EAAE;wBACrIC,gBAAgBF,6CAA2B;wBAC3CG,mBAAmBC,0CAAwB;wBAC3CC,mBAAmBC,yCAAuB;wBAC1CC,QAAQC,qBAAU;wBAClBC,gBAAgBC,8BAAmB;wBACnCC,uBAAuBV,qDAAmC;wBAC1DW,uBAAuBC,6BAAkB;wBACzCC,0BAA0BC,kCAAuB;oBACnD;oBACAC,gBAAgB;wBACdC,YAAYC,4CAA0B;wBACtCC,aAAaC,6CAA2B;oBAC1C;oBACAC,4BAA4B7U,OAAO6U,0BAA0B;oBAC7D1C,KAAKF,kBACD;wBACE6C,OAAO;4BACLlM,SAAS;gCACP,CAACmM,6BAAkB,CAAC,EAAE;4BACxB;wBACF;oBACF,IACA5N;gBACN;YACF;YAEF,IAAI0B,SAASG,WAAW,CAACI,MAAM,KAAK,KAAKP,SAASK,QAAQ,CAACE,MAAM,KAAK,GAAG;gBACvEkJ,eAAezJ,QAAQ,GAAGA,SAASI,UAAU,CAAClK,GAAG,CAAC,CAACgU,IACjDC,IAAAA,kCAAgB,EAAC,WAAWD;YAEhC,OAAO;gBACLT,eAAezJ,QAAQ,GAAG;oBACxBG,aAAaH,SAASG,WAAW,CAACjK,GAAG,CAAC,CAACgU,IACrCC,IAAAA,kCAAgB,EAAC,WAAWD;oBAE9B9J,YAAYJ,SAASI,UAAU,CAAClK,GAAG,CAAC,CAACgU,IACnCC,IAAAA,kCAAgB,EAAC,WAAWD;oBAE9B7J,UAAUL,SAASK,QAAQ,CAACnK,GAAG,CAAC,CAACgU,IAC/BC,IAAAA,kCAAgB,EAAC,WAAWD;gBAEhC;YACF;YACA,IAAIiC;YAIJ,IAAIhV,OAAOmD,YAAY,CAAC8R,kBAAkB,EAAE;gBAC1C,MAAMC,uBAAuB,AAAClV,CAAAA,OAAOwJ,kBAAkB,IAAI,EAAE,AAAD,EAAG3K,MAAM,CACnE,CAACkU,IAAW,CAACA,EAAEoC,QAAQ;gBAEzBH,sBAAsBI,IAAAA,kDAAwB,EAC5C;uBAAI7F;iBAAS,EACbvP,OAAOmD,YAAY,CAACkS,2BAA2B,GAC3CH,uBACA,EAAE,EACNlV,OAAOmD,YAAY,CAACmS,6BAA6B;gBAEnDzO,8BAAgB,CAACmO,mBAAmB,GAAGA;YACzC;YAEA,8EAA8E;YAC9E,uDAAuD;YACvD,MAAM3X,cACJZ,aAAI,CAACC,IAAI,CAACH,SAAS,iBACnB;YAGF,yFAAyF;YACzF,MAAMgZ,IAAAA,wCAAsB,EAAC1Q,QAAQD,GAAG,CAAC2C,cAAc;YACvD,MAAMiO,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;YACd;YAEA,MAAMnU,wBAAwBtB,OAAOsB,qBAAqB,IAAI8D;YAE9D,MAAMsQ,oBAAoBjZ,aAAI,CAACC,IAAI,CACjCH,SACAmD,4BAAgB,EAChBiW,0BAAc;YAGhB,IAAIC;YACJ,IAAIC,qBAA+C1O;YAEnD,uEAAuE;YACvE,4CAA4C;YAC5C,MAAM2O,iBACJ9V,OAAOmD,YAAY,CAAC4S,kBAAkB,IACrC/V,OAAOmD,YAAY,CAAC4S,kBAAkB,KAAK5O,aAC1C,CAACnH,OAAOgW,OAAO;YACnB,MAAMC,6BACJjW,OAAOmD,YAAY,CAAC+S,sBAAsB;YAC5C,MAAMC,qCACJnW,OAAOmD,YAAY,CAACiT,yBAAyB,IAC5CpW,OAAOmD,YAAY,CAACiT,yBAAyB,KAAKjP,aACjDP;YAEJzF,cAAckV,YAAY,CACxB,6BACA/O,OAAO,CAAC,CAACtH,OAAOgW,OAAO;YAEzB7U,cAAckV,YAAY,CAAC,oBAAoB/O,OAAOwO;YAEtD,IACE,CAACA,kBACAG,CAAAA,8BAA8BE,kCAAiC,GAChE;gBACA,MAAM,qBAEL,CAFK,IAAIhK,MACR,oMADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAjP,KAAImL,IAAI,CAAC;YACTiO,IAAAA,wBAAgB,EAAC,kBAAkBnV;YAEnC,MAAMqU,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;gBACZc,cAAc;oBACZT,gBAAgBxO,OAAOwO;gBACzB;YACF;YAEA,IAAIU,kBAAkBC,QAAQ1S,OAAO;YACrC,IAAI,CAACkC,gBAAgB;gBACnB,IAAIQ,aAAa;oBACf,MAAM,EACJiQ,UAAUC,gBAAgB,EAC1BH,iBAAiBlW,CAAC,EAClB,GAAGsW,MACJ,GAAG,MAAMC,IAAAA,8BAAc,EACtBhS,QAAQD,GAAG,CAACkS,yBAAyB,KAAK3P,aACxCtC,QAAQD,GAAG,CAACkS,yBAAyB,KAAK;oBAE9CN,kBAAkBlW;oBAClBgW,IAAAA,wBAAgB,EAAC,kBAAkBnV;oBAEnCyU,oBAAoBgB,KAAKhB,iBAAiB;oBAE1C,MAAMmB,iBAAiBC,IAAAA,kCAAgB,EAACL;oBACxCzZ,KAAI+Z,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;oBAEtDtN,UAAUY,MAAM,CACd6M,IAAAA,2BAAmB,EAAC9J,YAAY;wBAC9B+J,SAAS;wBACTC,mBAAmB7T,KAAK8T,KAAK,CAACV;wBAC9BhG;oBACF;gBAEJ,OAAO;oBACL,IACEsF,8BACAE,oCACA;wBACA,IAAIiB,oBAAoB;wBAExB,MAAM5B,IAAAA,wCAAsB,EAAC;4BAC3BC,YAAY;wBACd;wBAEA,MAAM6B,qBAAqBC,IAAAA,0BAAY,EAACzB,gBAAgB;4BACtD;yBACD,EAAE/K,IAAI,CAAC,CAACyM;4BACPlB,IAAAA,wBAAgB,EAAC,+BAA+BnV;4BAChDyU,oBAAoB4B,IAAI5B,iBAAiB;4BACzCwB,qBAAqBI,IAAId,QAAQ;4BAEjC,IAAIP,oCAAoC;gCACtC,MAAMsB,mBAAmB,IAAIrT,cAAM,CACjCN,QAAQC,OAAO,CAAC,2BAChB;oCACEO,YAAY;oCACZY,gBAAgB;wCAAC;qCAAqB;gCACxC;gCAGF2Q,qBAAqB4B,iBAClBC,kBAAkB,CAAC;oCAClBtS;oCACApF;oCACAzD;oCACA,+CAA+C;oCAC/Cob,mBAAmBC,IAAAA,qCAA6B,EAAC,IAAIC;oCACrDnW,aAAa,EAAE;oCACfoW,gBAAgB;oCAChBlC;oCACAtU;gCACF,GACCyW,KAAK,CAAC,CAAChM;oCACN/O,QAAQuP,KAAK,CAACR;oCACdlH,QAAQuD,IAAI,CAAC;gCACf;4BACJ;wBACF;wBACA,IAAI,CAAC6N,4BAA4B;4BAC/B,MAAMqB;4BACN,MAAM9B,IAAAA,wCAAsB,EAAC;gCAC3BC,YAAY;4BACd;wBACF;wBAEA,MAAMuC,mBAAmBT,IAAAA,0BAAY,EAACzB,gBAAgB;4BACpD;yBACD,EAAE/K,IAAI,CAAC,CAACyM;4BACPJ,qBAAqBI,IAAId,QAAQ;4BACjCJ,IAAAA,wBAAgB,EACd,oCACAnV;wBAEJ;wBACA,IAAI8U,4BAA4B;4BAC9B,MAAMqB;4BACN,MAAM9B,IAAAA,wCAAsB,EAAC;gCAC3BC,YAAY;4BACd;wBACF;wBACA,MAAMuC;wBAEN,MAAMxC,IAAAA,wCAAsB,EAAC;4BAC3BC,YAAY;wBACd;wBAEA,MAAM8B,IAAAA,0BAAY,EAACzB,gBAAgB;4BAAC;yBAAS,EAAE/K,IAAI,CAAC,CAACyM;4BACnDJ,qBAAqBI,IAAId,QAAQ;4BACjCJ,IAAAA,wBAAgB,EAAC,+BAA+BnV;wBAClD;wBAEA,MAAM4V,iBAAiBC,IAAAA,kCAAgB,EAACI;wBACxCla,KAAI+Z,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;wBAEtDtN,UAAUY,MAAM,CACd6M,IAAAA,2BAAmB,EAAC9J,YAAY;4BAC9B+J,SAASc,uBAAuBxR;4BAChC2Q;4BACAzG;wBACF;oBAEJ,OAAO;wBACL,MAAM,EAAE+F,UAAUC,gBAAgB,EAAE,GAAGC,MAAM,GAAG,MAAMW,IAAAA,0BAAY,EAChEzB,gBACA;wBAEFQ,IAAAA,wBAAgB,EAAC,kBAAkBnV;wBAEnCyU,oBAAoBgB,KAAKhB,iBAAiB;wBAE1CnM,UAAUY,MAAM,CACd6M,IAAAA,2BAAmB,EAAC9J,YAAY;4BAC9B+J,SAASc,uBAAuBxR;4BAChC2Q,mBAAmBT;4BACnBhG;wBACF;oBAEJ;gBACF;YACF;YAEA,uDAAuD;YACvD,IAAI/O,UAAU,CAACgF,iBAAiB,CAACX,gBAAgB;gBAC/C,MAAMuP,IAAAA,wCAAsB,EAAC;oBAC3BC,YAAY;gBACd;gBACA,MAAMnJ,IAAAA,4BAAiB,EAACT;gBACxByK,IAAAA,wBAAgB,EAAC,0BAA0BnV;YAC7C;YAEA,MAAM+W,qBAAqBC,IAAAA,gBAAa,EAAC;YAEzC,MAAMC,oBAAoB3b,aAAI,CAACC,IAAI,CAACH,SAAS8b,0BAAc;YAC3D,MAAMC,uBAAuB7b,aAAI,CAACC,IAAI,CAACH,SAASgc,8BAAkB;YAElE,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,wBAAwB;YAC5B,MAAMna,WAAW,IAAIC;YACrB,MAAMma,yBAAyB,IAAIna;YACnC,MAAMoa,2BAA2B,IAAIpa;YACrC,MAAMiD,cAAc,IAAIjD;YACxB,MAAMqa,eAAe,IAAIra;YACzB,MAAMsa,iBAAiB,IAAIta;YAC3B,MAAMua,mBAAmB,IAAIva;YAC7B,MAAMwa,kBAAkB,IAAIpB;YAC5B,MAAMqB,cAAc,IAAIrB;YACxB,MAAMsB,qBAAqB,IAAItB;YAI/B,MAAMuB,qBAAqB,IAAIvB;YAC/B,MAAMwB,gBAAgB,IAAIxB;YAC1B,MAAMyB,oBAAoB,IAAIzB;YAC9B,MAAM0B,YAAuB,IAAI1B;YACjC,IAAI2B,gBAAgB,MAAMzb,aAA4B2X;YACtD,MAAM+D,gBAAgB,MAAM1b,aAA4Bqa;YACxD,MAAMsB,mBAAmB9X,SACrB,MAAM7D,aAA+Bua,wBACrCnR;YAEJ,MAAMwS,gBAAwC,CAAC;YAE/C,IAAI/X,QAAQ;gBACV,MAAMgY,mBAAmB,MAAM7b,aAC7BtB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEma,8BAAkB;gBAGzD,IAAK,MAAMC,OAAOF,iBAAkB;oBAClCD,aAAa,CAACG,IAAI,GAAGzJ,IAAAA,0BAAgB,EAACyJ;gBACxC;gBAEA,MAAMlc,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASwd,oCAAwB,GAC3CJ;YAEJ;YAEA9U,QAAQD,GAAG,CAACoV,UAAU,GAAGjS,kCAAsB;YAE/C,MAAMkS,SAAS9e,mBAAmB6E;YAElC,MAAMka,gBAAgBrV,QAAQsV,MAAM;YACpC,MAAMC,kBAAkBjZ,cAAcU,UAAU,CAAC;YAEjD,MAAMwY,0BAAmD;gBACvDrZ,SAAS;gBACTsZ,WAAW,CAAC;YACd;YAEA,MAAM,EACJC,wBAAwB,EACxBC,YAAY,EACZC,mBAAmB,EACnB3C,cAAc,EACd4C,qBAAqB,EACtB,GAAG,MAAMN,gBAAgBtY,YAAY,CAAC;oBAcV9B;gBAb3B,IAAI4G,eAAe;oBACjB,OAAO;wBACL2T,0BAA0B;wBAC1BC,cAAc,EAAE;wBAChBC,qBAAqB;wBACrB3C,gBAAgB,CAAC,CAAClO;wBAClB8Q,uBAAuB;oBACzB;gBACF;gBAEA,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE,GAChE7a;gBACF,MAAM8a,mBAAmB;oBAAEF;oBAAqBC;gBAAoB;gBACpE,MAAME,aAAatP,SAAQzL,2BAAAA,OAAOmD,YAAY,CAAC6X,GAAG,qBAAvBhb,yBAAyBib,SAAS;gBAE7D,MAAMC,yBAAyBd,gBAAgBvY,UAAU,CACvD;gBAEF,MAAMsZ,oCACJD,uBAAuBpZ,YAAY,CACjC,UACEoP,sBACC,MAAM+I,OAAOmB,wBAAwB,CAAC;wBACrCxf,MAAM;wBACNW;wBACAue;wBACAO,aAAa;wBACbN;oBACF;gBAGN,MAAMO,wBAAwBJ,uBAAuBpZ,YAAY,CAC/D;wBAWa9B,cACMA;2BAXjBkR,sBACA+I,OAAOsB,YAAY,CAAC;wBAClBnW;wBACAxJ,MAAM;wBACNW;wBACAoe;wBACAG;wBACAhJ,WAAWD;wBACXG,gBAAgBD;wBAChByJ,kBAAkBxb,OAAOwb,gBAAgB;wBACzCjd,OAAO,GAAEyB,eAAAA,OAAOkT,IAAI,qBAAXlT,aAAazB,OAAO;wBAC7Bkd,aAAa,GAAEzb,gBAAAA,OAAOkT,IAAI,qBAAXlT,cAAayb,aAAa;wBACzCC,kBAAkB1b,OAAO2b,MAAM;wBAC/BC,WAAW5b,OAAOmD,YAAY,CAACgP,GAAG;wBAClC0J,mBAAmB7b,OAAOmD,YAAY,CAAC2Y,SAAS;wBAChDxd;wBACAyc;oBACF;;gBAGJ,MAAMgB,iBAAiB;gBAEvB,MAAMC,kCAAkC/B,OAAOmB,wBAAwB,CACrE;oBACExf,MAAMmgB;oBACNxf;oBACAue;oBACAO,aAAa;oBACbN;gBACF;gBAGF,MAAMkB,sBAAsBhC,OAAOiC,sBAAsB,CAAC;oBACxDtgB,MAAMmgB;oBACNxf;oBACAue;oBACAC;gBACF;gBAEA,wDAAwD;gBACxD,IAAIN;gBACJ,wDAAwD;gBACxD,IAAI3C,iBAAiB;gBAErB,MAAMqE,uBAAuB,MAAMC,IAAAA,2BAAmB,EACpD;oBAAEhhB,OAAOqe;oBAAe3P,KAAK4P;gBAAiB,GAC9Cnd,SACAyD,OAAOmD,YAAY,CAACkZ,QAAQ;gBAG9B,MAAM9a,qBAAyCuC,QAC7CrH,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE4c,+BAAmB;gBAG1D,MAAMC,iBAAiB3a,SAClBkC,QACCrH,aAAI,CAACC,IAAI,CACPH,SACAmD,4BAAgB,EAChB8c,qCAAyB,GAAG,YAGhC;gBACJ,MAAMC,oBAAoBF,iBAAiB,IAAI9d,QAAQ;gBACvD,IAAI8d,kBAAkBE,mBAAmB;oBACvC,IAAK,MAAMC,MAAMH,eAAeI,IAAI,CAAE;wBACpC,IAAK,MAAMC,SAASL,eAAeI,IAAI,CAACD,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBjM,GAAG,CAACoM;wBACxB;oBACF;oBACA,IAAK,MAAMF,MAAMH,eAAeO,IAAI,CAAE;wBACpC,IAAK,MAAMF,SAASL,eAAeO,IAAI,CAACJ,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBjM,GAAG,CAACoM;wBACxB;oBACF;gBACF;gBAEA,KAAK,MAAM9C,OAAOpb,OAAOS,IAAI,CAACoC,sCAAAA,mBAAoB+Y,SAAS,EAAG;oBAC5D,IAAIR,IAAI3P,UAAU,CAAC,SAAS;wBAC1BwO;oBACF;gBACF;gBAEA,MAAMlC,QAAQsG,GAAG,CACfre,OAAOC,OAAO,CAACyC,UACZe,MAAM,CACL,CAACC,KAAK,CAAC0X,KAAK5X,MAAM;oBAChB,IAAI,CAACA,OAAO;wBACV,OAAOE;oBACT;oBAEA,MAAM4a,WAAWlD;oBAEjB,KAAK,MAAMle,QAAQsG,MAAO;wBACxBE,IAAIG,IAAI,CAAC;4BAAEya;4BAAUphB;wBAAK;oBAC5B;oBAEA,OAAOwG;gBACT,GACA,EAAE,EAEHrD,GAAG,CAAC,CAAC,EAAEie,QAAQ,EAAEphB,IAAI,EAAE;oBACtB,MAAMqhB,gBAAgB7C,gBAAgBvY,UAAU,CAAC,cAAc;wBAC7DjG;oBACF;oBACA,OAAOqhB,cAAcnb,YAAY,CAAC;wBAChC,MAAMob,aAAaC,IAAAA,oCAAiB,EAACvhB;wBACrC,MAAM,CAACwhB,MAAMC,UAAU,GAAG,MAAMC,IAAAA,yBAAiB,EAC/CN,UACAE,YACA3gB,SACAkd,eACAC,kBACA1Z,OAAOmD,YAAY,CAACkZ,QAAQ,EAC5BF;wBAGF,IAAIoB,oBAAoB;wBACxB,IAAIC,QAAQ;wBACZ,IAAIC,WAAW;wBACf,IAAIC,oBAAoB;wBACxB,IAAIC,cAAc;wBAClB,IAAIC,gBAAiC;wBACrC,IAAItN,WAAW;wBAEf,IAAI0M,aAAa,SAAS;4BACxB1M,WACElD,WAAWyQ,IAAI,CAAC,CAACvd;gCACfA,IAAIwd,IAAAA,kCAAgB,EAACxd;gCACrB,OACEA,EAAE6J,UAAU,CAAC+S,aAAa,QAC1B5c,EAAE6J,UAAU,CAAC+S,aAAa;4BAE9B,MAAM;wBACV;wBACA,IAAIa;wBAEJ,IAAIf,aAAa,SAAS5N,gBAAgB;4BACxC,KAAK,MAAM,CAAC4O,cAAcC,eAAe,IAAIvf,OAAOC,OAAO,CACzDgb,eACC;gCACD,IAAIsE,mBAAmBriB,MAAM;oCAC3B0U,WAAWlB,cAAc,CAAC4O,aAAa,CAACxd,OAAO,CAC7C,yBACA;oCAEFud,kBAAkBC;oCAClB;gCACF;4BACF;wBACF;wBAEA,MAAME,eAAeC,IAAAA,gCAAwB,EAAC7N,YAC1CxM,QAAQC,OAAO,CACb,iDAEFtH,aAAI,CAACC,IAAI,CACP,AAACsgB,CAAAA,aAAa,UAAUpT,WAAWhI,MAAK,KAAM,IAC9C0O;wBAGN,MAAM8N,iBAAiBpB,aAAa;wBACpC,MAAMqB,aAAa/N,WACf,MAAMgO,IAAAA,sCAA6B,EAAC;4BAClCF;4BACAF;4BACAjR,gBAAgBjN,OAAOiN,cAAc;4BACrCrL;4BACA5B;4BACA+O,OAAO;4BACP,yDAAyD;4BACzD,4DAA4D;4BAC5D,gEAAgE;4BAChEnT,MAAMwiB,iBAAiBL,kBAAmBniB;wBAC5C,KACAuL;wBAEJ,8DAA8D;wBAC9D,oDAAoD;wBACpD,IACE,QAAOkX,8BAAAA,WAAYE,OAAO,MAAK,eAC/B,QAAOF,8BAAAA,WAAYG,WAAW,MAAK,aACnC;4BACAnE,wBAAwBC,SAAS,CAAC1e,KAAK,GAAG;gCACxC4iB,WAAW,EAAEH,8BAAAA,WAAYG,WAAW;4BACtC;wBACF;wBAEA,MAAMC,cAAcld,mBAAmB+Y,SAAS,CAC9CyD,mBAAmBniB,KACpB,GACG,SACAyiB,8BAAAA,WAAYE,OAAO;wBAEvB,IAAI,CAAC3X,eAAe;4BAClB8W,oBACEV,aAAa,SACbqB,CAAAA,8BAAAA,WAAYlL,GAAG,MAAKuL,4BAAgB,CAACC,MAAM;4BAE7C,IAAI3B,aAAa,SAAS,CAACrK,IAAAA,sBAAc,EAAC/W,OAAO;gCAC/C,IAAI;oCACF,IAAIgjB;oCAEJ,IAAIC,IAAAA,4BAAa,EAACJ,cAAc;wCAC9B,IAAIzB,aAAa,OAAO;4CACtBtE;wCACF,OAAO;4CACLC;wCACF;wCAEA,MAAMmG,cACJ9B,aAAa,UAAUphB,OAAOmiB,mBAAmB;wCAEnDa,WAAWrd,mBAAmB+Y,SAAS,CAACwE,YAAY;oCACtD;oCAEA,IAAIC,mBACF9B,cAAcpb,UAAU,CAAC;oCAC3B,IAAImd,eAAe,MAAMD,iBAAiBjd,YAAY,CACpD;4CASa9B,cACMA;wCATjB,OAAOia,OAAOsB,YAAY,CAAC;4CACzBnW;4CACAxJ;4CACAmiB;4CACAxhB;4CACAoe;4CACAG;4CACAU,kBAAkBxb,OAAOwb,gBAAgB;4CACzCjd,OAAO,GAAEyB,eAAAA,OAAOkT,IAAI,qBAAXlT,aAAazB,OAAO;4CAC7Bkd,aAAa,GAAEzb,gBAAAA,OAAOkT,IAAI,qBAAXlT,cAAayb,aAAa;4CACzCwD,UAAUF,iBAAiBG,KAAK;4CAChCT;4CACAG;4CACA5B;4CACAlL,WAAWD;4CACXG,gBAAgBD;4CAChBoN,cAAcnf,OAAOmf,YAAY;4CACjCC,eAAepf,OAAOmD,YAAY,CAACic,aAAa;4CAChDC,gBAAgB1iB,QAAcE,cAAc,GACxC,QACAmD,OAAOmD,YAAY,CAACkc,cAAc;4CACtCC,oBAAoBtf,OAAOuf,kBAAkB;4CAC7C7D,kBAAkB1b,OAAO2b,MAAM;4CAC/BC,WAAW5b,OAAOmD,YAAY,CAACgP,GAAG;4CAClC0J,mBAAmB7b,OAAOmD,YAAY,CAAC2Y,SAAS;4CAChDxd;4CACAyc;wCACF;oCACF;oCAGF,IAAIiC,aAAa,SAASe,iBAAiB;wCACzC3E,mBAAmBoG,GAAG,CAACzB,iBAAiBniB;wCACxC,0CAA0C;wCAC1C,IAAIijB,IAAAA,4BAAa,EAACJ,cAAc;4CAC9BhB,WAAW;4CACXD,QAAQ;4CAERtgB,KAAIuiB,QAAQ,CACV,CAAC,+EAA+E,CAAC;wCAErF,OAAO;4CACL,MAAMC,YAAYhN,IAAAA,qBAAc,EAAC9W;4CAEjC,IACE,OAAOojB,aAAazB,iBAAiB,KAAK,WAC1C;gDACAA,oBAAoByB,aAAazB,iBAAiB;4CACpD;4CAEA,oDAAoD;4CACpD,0CAA0C;4CAC1C,yBAAyB;4CACzB,IAAIyB,aAAazB,iBAAiB,EAAE;gDAClCC,QAAQ;gDACRC,WAAW;gDAEXvE,YAAYsG,GAAG,CAACzB,iBAAiB,EAAE;4CACrC,OAOK,IAAI/d,OAAOmD,YAAY,CAAC2O,SAAS,IAAI4N,WAAW;gDACnDvG,mBAAmBqG,GAAG,CAACzB,iBAAiB;oDACtCniB;oDACAmiB;gDACF;4CACF;4CAEA,IAAIiB,aAAaW,iBAAiB,EAAE;gDAClCzG,YAAYsG,GAAG,CACbzB,iBACAiB,aAAaW,iBAAiB;gDAEhC/B,gBAAgBoB,aAAaW,iBAAiB,CAAC5gB,GAAG,CAChD,CAACC,QAAUA,MAAME,QAAQ;gDAE3Bse,QAAQ;4CACV;4CAEA,MAAMoC,YAAYZ,aAAaY,SAAS,IAAI,CAAC;4CAC7C,IAAIA,UAAUC,UAAU,KAAK,GAAG;gDAC9B,MAAMC,0BACJd,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAACvW,MAAM,GAAG;gDAE1C,IACEpJ,OAAO2b,MAAM,KAAK,YAClB+D,aACA,CAACI,yBACD;oDACA,MAAM,qBAEL,CAFK,IAAI3T,MACR,CAAC,MAAM,EAAEvQ,KAAK,wFAAwF,CAAC,GADnG,qBAAA;+DAAA;oEAAA;sEAAA;oDAEN;gDACF;gDAEA,6BAA6B;gDAC7B,4BAA4B;gDAC5B,iEAAiE;gDACjE,8BAA8B;gDAC9B,IAAI,CAAC8jB,WAAW;oDACdxG,YAAYsG,GAAG,CAACzB,iBAAiB;wDAC/B;4DACE7e,UAAUtD;4DACVmkB,iBAAiBnkB;4DACjBokB,qBAAqB7Y;4DACrB8Y,cACEjB,aAAakB,qBAAqB;4DACpCC,oBAAoBhZ;wDACtB;qDACD;oDACDsW,WAAW;gDACb,OAAO,IACL,CAACqC,2BACAF,CAAAA,UAAUQ,OAAO,KAAK,WACrBR,UAAUQ,OAAO,KAAK,cAAa,GACrC;oDACAlH,YAAYsG,GAAG,CAACzB,iBAAiB,EAAE;oDACnCN,WAAW;oDACXF,oBAAoB;gDACtB;4CACF;4CAEA,IAAIyB,aAAakB,qBAAqB,EAAE;gDACtC7G,cAAcmG,GAAG,CACfzB,iBACAiB,aAAakB,qBAAqB;4CAEtC;4CAEA5G,kBAAkBkG,GAAG,CAACzB,iBAAiB6B;wCACzC;oCACF,OAAO;wCACL,IAAIf,IAAAA,4BAAa,EAACJ,cAAc;4CAC9B,IAAIO,aAAaqB,cAAc,EAAE;gDAC/BrjB,QAAQI,IAAI,CACV,CAAC,kFAAkF,EAAExB,MAAM;4CAE/F;4CACA,mDAAmD;4CACnD,8CAA8C;4CAC9CojB,aAAavB,QAAQ,GAAG;4CACxBuB,aAAaqB,cAAc,GAAG;wCAChC;wCAEA,IACErB,aAAavB,QAAQ,KAAK,SACzBuB,CAAAA,aAAarB,WAAW,IAAIqB,aAAasB,SAAS,AAAD,GAClD;4CACAxI,iBAAiB;wCACnB;wCAEA,IAAIkH,aAAarB,WAAW,EAAE;4CAC5BA,cAAc;4CACd5E,eAAevI,GAAG,CAAC5U;wCACrB;wCAEA,IAAIojB,aAAavE,mBAAmB,EAAE;4CACpCA,sBAAsB;wCACxB;wCAEA,IAAIuE,aAAaqB,cAAc,EAAE;4CAC/B7hB,SAASgS,GAAG,CAAC5U;4CACb4hB,QAAQ;4CAER,IACEwB,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAACvW,MAAM,GAAG,GACxC;gDACA6P,gBAAgBuG,GAAG,CACjB5jB,MACAojB,aAAaW,iBAAiB;gDAEhC/B,gBAAgBoB,aAAaW,iBAAiB,CAAC5gB,GAAG,CAChD,CAACC,QAAUA,MAAME,QAAQ;4CAE7B;4CAEA,IACE8f,aAAakB,qBAAqB,KAClCK,sBAAY,CAACC,sBAAsB,EACnC;gDACA3H,yBAAyBrI,GAAG,CAAC5U;4CAC/B,OAAO,IACLojB,aAAakB,qBAAqB,KAClCK,sBAAY,CAACE,SAAS,EACtB;gDACA7H,uBAAuBpI,GAAG,CAAC5U;4CAC7B;wCACF,OAAO,IAAIojB,aAAa0B,cAAc,EAAE;4CACtC1H,iBAAiBxI,GAAG,CAAC5U;wCACvB,OAAO,IACLojB,aAAavB,QAAQ,IACrB,CAACC,qBACD,AAAC,MAAM1B,oCAAqC,OAC5C;4CACAta,YAAY8O,GAAG,CAAC5U;4CAChB6hB,WAAW;wCACb,OAAO,IAAIC,mBAAmB;4CAC5B,2DAA2D;4CAC3D,gDAAgD;4CAChDlf,SAASgS,GAAG,CAAC5U;4CACb4hB,QAAQ;wCACV;wCAEA,IAAI1M,eAAelV,SAAS,QAAQ;4CAClC,IACE,CAACojB,aAAavB,QAAQ,IACtB,CAACuB,aAAaqB,cAAc,EAC5B;gDACA,MAAM,qBAEL,CAFK,IAAIlU,MACR,CAAC,cAAc,EAAEwU,qDAA0C,EAAE,GADzD,qBAAA;2DAAA;gEAAA;kEAAA;gDAEN;4CACF;4CACA,2DAA2D;4CAC3D,mCAAmC;4CACnC,IACE,AAAC,MAAM3E,mCACP,CAACgD,aAAaqB,cAAc,EAC5B;gDACA3e,YAAYkf,MAAM,CAAChlB;4CACrB;wCACF;wCAEA,IACEilB,+BAAmB,CAACve,QAAQ,CAAC1G,SAC7B,CAACojB,aAAavB,QAAQ,IACtB,CAACuB,aAAaqB,cAAc,EAC5B;4CACA,MAAM,qBAEL,CAFK,IAAIlU,MACR,CAAC,OAAO,EAAEvQ,KAAK,GAAG,EAAE+kB,qDAA0C,EAAE,GAD5D,qBAAA;uDAAA;4DAAA;8DAAA;4CAEN;wCACF;oCACF;gCACF,EAAE,OAAO5U,KAAK;oCACZ,IACE,CAACC,IAAAA,gBAAO,EAACD,QACTA,IAAI+U,OAAO,KAAK,0BAEhB,MAAM/U;oCACR+M,aAAatI,GAAG,CAAC5U;gCACnB;4BACF;4BAEA,IAAIohB,aAAa,OAAO;gCACtB,IAAIQ,SAASC,UAAU;oCACrBjF;gCACF,OAAO;oCACLC;gCACF;4BACF;wBACF;wBAEAc,UAAUiG,GAAG,CAAC5jB,MAAM;4BAClBwhB;4BACAC;4BACAI;4BACAD;4BACAD;4BACAI;4BACAC;4BACAmD,qBAAqB5Z;4BACrBoX,SAASE;4BACTuC,cAAc7Z;4BACd8Z,kBAAkB9Z;4BAClB+Z,iBAAiB/Z;wBACnB;oBACF;gBACF;gBAGJ,IAAIga,sCAAmB,EAAE;oBACvBjkB,KAAIqP,KAAK,CACP,CAAC,0IAA0I,CAAC;oBAE9I1H,QAAQuD,IAAI,CAAC;gBACf;gBAEA,MAAMgZ,kBAAkB,MAAM9F;gBAC9B,MAAM+F,qBACJ,AAAC,MAAMlG,qCACNiG,mBAAmBA,gBAAgBV,cAAc;gBAEpD,MAAMY,cAAc;oBAClB/G,0BAA0B,MAAMyB;oBAChCxB,cAAc,MAAMyB;oBACpBxB;oBACA3C;oBACA4C,uBAAuB2G;gBACzB;gBAEA,OAAOC;YACT;YAEA,IAAIpJ,oBAAoBA,mBAAmBqJ,cAAc;YACzDjL,IAAAA,wBAAgB,EAAC,iCAAiCnV;YAElD,IAAIoZ,0BAA0B;gBAC5Bvd,QAAQI,IAAI,CACVokB,IAAAA,gBAAI,EAACC,IAAAA,kBAAM,EAAC,CAAC,SAAS,CAAC,KACrBA,IAAAA,kBAAM,EACJ,CAAC,qJAAqJ,CAAC;gBAG7JzkB,QAAQI,IAAI,CACV;YAEJ;YAEA,MAAM,EAAE+hB,YAAY,EAAE,GAAGnf;YAEzB,MAAM0hB,gCAA0C,EAAE;YAClD,IAAIjgB,wBAAwB;gBAC1BigB,8BAA8Bnf,IAAI,CAChC9F,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE,GAAGkO,wCAA6B,CAAC,GAAG,CAAC;gBAEnE,+DAA+D;gBAC/D,8FAA8F;gBAC9F,IAAI,CAACnH,eAAgBiS,CAAAA,uBAAuBC,qBAAoB,GAAI;oBAClE+I,8BAA8Bnf,IAAI,CAChC9F,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB,CAAC,KAAK,EAAEkO,wCAA6B,CAAC,GAAG,CAAC;gBAGhD;YACF;YAEA,MAAM+T,8BAA8BxgB,cACjCU,UAAU,CAAC,kCACX4F,OAAO,CAAC;gBACP,MAAMma,0BAAkD,CAAC;gBAEzD,KAAK,MAAM,CAAC9H,KAAK+H,MAAM,IAAInjB,OAAOC,OAAO,CACvCqB,OAAOmD,YAAY,CAACic,aAAa,IAAI,CAAC,GACrC;oBACD,IAAItF,OAAO+H,OAAO;wBAChBD,uBAAuB,CAAC9H,IAAI,GAAGrd,aAAI,CAACgG,QAAQ,CAAClG,SAASslB;oBACxD;gBACF;gBAEA,MAAMC,sBAAmD;oBACvD9gB,SAAS;oBACThB,QAAQ;wBACN,GAAGA,MAAM;wBACT+hB,YAAY5a;wBACZ,GAAIxK,QAAcE,cAAc,GAC5B;4BACEmlB,UAAU;wBACZ,IACA,CAAC,CAAC;wBACN7C,cAAcA,eACV1iB,aAAI,CAACgG,QAAQ,CAAClG,SAAS4iB,gBACvBnf,OAAOmf,YAAY;wBACvBhc,cAAc;4BACZ,GAAGnD,OAAOmD,YAAY;4BACtBic,eAAewC;4BACfK,iBAAiBtlB,QAAcE,cAAc;4BAE7C,oGAAoG;4BACpGqlB,uBAAuBtb;wBACzB;oBACF;oBACAhF,QAAQwD;oBACR+c,gBAAgB1lB,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB8D;oBACrDlD,OAAO;wBACLmQ,2BAAe;wBACf5V,aAAI,CAACgG,QAAQ,CAAClG,SAASmZ;wBACvB2C,0BAAc;wBACdla,8BAAkB;wBAClB1B,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEC,qCAAyB;wBACrDlD,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE4c,+BAAmB;wBAC/C7f,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE0iB,qCAAyB,GAAG;2BACpD,CAAC3b,cACD;4BACEhK,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB2iB,8CAAkC,GAAG;4BAEvCC,mCAAuB;yBACxB,GACD,EAAE;2BACF1gB,SACA;+BACM5B,OAAOmD,YAAY,CAAC6X,GAAG,GACvB;gCACEve,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB6iB,0CAA8B,GAAG;gCAEnC9lB,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB6iB,0CAA8B,GAAG;6BAEpC,GACD,EAAE;4BACN9lB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEma,8BAAkB;4BAC9Cpd,aAAI,CAACC,IAAI,CAACqd,oCAAwB;4BAClCxB,8BAAkB;4BAClB9b,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB8c,qCAAyB,GAAG;4BAE9B/f,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB8c,qCAAyB,GAAG;yBAE/B,GACD,EAAE;2BACF5S,YAAY,CAACnD,cACb;4BACE+b,gCAAoB,GAAG;4BACvB/lB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE8iB,gCAAoB,GAAG;yBACpD,GACD,EAAE;wBACNC,yBAAa;wBACbhmB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEgjB,8BAAkB,GAAG;wBACjDjmB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEgjB,8BAAkB,GAAG;2BAC9ChB;qBACJ,CACE7iB,MAAM,CAAC8jB,wBAAW,EAClB5jB,GAAG,CAAC,CAACkD,OAASxF,aAAI,CAACC,IAAI,CAACsD,OAAOzD,OAAO,EAAE0F;oBAC3C2gB,QAAQ,EAAE;gBACZ;gBAEA,OAAOd;YACT;YAEF,IAAI,CAAChK,gBAAgB;gBACnB6J,4BAA4BiB,MAAM,CAACrgB,IAAI,CACrC9F,aAAI,CAACgG,QAAQ,CACX2C,KACA3I,aAAI,CAACC,IAAI,CACPD,aAAI,CAACkG,OAAO,CACVmB,QAAQC,OAAO,CACb,sDAGJ;YAIR;YAEA,MAAM8e,iBAAiB/U,UAAU+P,IAAI,CAAC,CAACvd,IACrCA,EAAEgC,QAAQ,CAACoL,8BAAmB;YAEhC,IAAIlM,oBAAoB;YAExB,IAAIqhB,gBAAgB;gBAClB,MAAMxE,aAAa,MAAMC,IAAAA,sCAA6B,EAAC;oBACrDF,gBAAgB;oBAChBF,cAAczhB,aAAI,CAACC,IAAI,CAAC0I,KAAKyd;oBAC7B7iB;oBACA4B;oBACAqL,gBAAgBjN,OAAOiN,cAAc;oBACrC8B,OAAO;oBACPnT,MAAM;gBACR;gBAEA,IAAIyiB,WAAWE,OAAO,KAAK,UAAU;wBAIvBF;oBAHZ7c,oBAAoB;oBACpB6Y,wBAAwBC,SAAS,CAAC,eAAe,GAAG;wBAClDiE,SAASF,WAAWE,OAAO;wBAC3BuE,UAAUzE,EAAAA,yBAAAA,WAAW0E,UAAU,qBAArB1E,uBAAuByE,QAAQ,KAAI;4BAC3C;gCACEE,QAAQ;gCACRC,gBAAgB;4BAClB;yBACD;oBACH;oBAEA,IAAIxc,aAAa;wBACf,MAAM7I,cACJnB,aAAI,CAACC,IAAI,CACPH,SACA,UACA+B,SACA4kB,gDAAoC,GAEtC7I,wBAAwBC,SAAS,CAAC,eAAe,CAACwI,QAAQ,IAAI,EAAE;oBAEpE;gBACF;YACF;YAEA,MAAMrjB,6BAA6BlD,SAAS8d;YAE5C,IAAI,CAACpU,kBAAkB,CAAC4P,oBAAoB;gBAC1CA,qBAAqB6B,IAAAA,sCAAkB,EAAC;oBACtCtS;oBACApF;oBACAzD;oBACAob,mBAAmBC,IAAAA,qCAA6B,EAAC2B;oBACjD7X,aAAa;2BAAIA;qBAAY;oBAC7BP;oBACA2W;oBACAlC;oBACAtU;gBACF,GAAGyW,KAAK,CAAC,CAAChM;oBACR/O,QAAQuP,KAAK,CAACR;oBACdlH,QAAQuD,IAAI,CAAC;gBACf;YACF;YAEA,IAAI4Q,iBAAiBoE,IAAI,GAAG,KAAK5e,SAAS4e,IAAI,GAAG,GAAG;gBAClD,yDAAyD;gBACzD,+DAA+D;gBAC/D9K,eAAeW,UAAU,GAAGT,IAAAA,sBAAe,EAAC;uBACvCwG;uBACAxa;iBACJ,EAAEO,GAAG,CAAC,CAACnD;oBACN,OAAOunB,IAAAA,8BAAc,EAACvnB,MAAM0C;gBAC9B;YACF;YAEA,2DAA2D;YAC3D,MAAM6C,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMlE,cAAcwU,oBAAoBE;YAExD,iHAAiH;YACjH,8DAA8D;YAC9D,MAAM8Q,oBACJ,CAAC7I,4BAA6B,CAAA,CAACG,yBAAyB5J,WAAU;YAEpE,IAAIgI,aAAasE,IAAI,GAAG,GAAG;gBACzB,MAAMrR,MAAM,qBAQX,CARW,IAAII,MACd,CAAC,qCAAqC,EACpC2M,aAAasE,IAAI,KAAK,IAAI,KAAK,IAChC,kDAAkD,EAAE;uBAAItE;iBAAa,CACnE/Z,GAAG,CAAC,CAACskB,KAAO,CAAC,KAAK,EAAEA,IAAI,EACxB3mB,IAAI,CACH,MACA,sFAAsF,CAAC,GAPjF,qBAAA;2BAAA;gCAAA;kCAAA;gBAQZ;gBACAqP,IAAIE,IAAI,GAAG;gBACX,MAAMF;YACR;YAEA,MAAMuX,IAAAA,0BAAY,EAAC/mB,SAAS+B;YAE5B,IAAI0B,OAAOmD,YAAY,CAACogB,WAAW,EAAE;gBACnC,MAAMC,WACJ1f,QAAQ;gBAEV,MAAM2f,eAAe,MAAM,IAAIhN,QAAkB,CAAC1S,SAAS2f;oBACzDF,SACE,YACA;wBAAE7Y,KAAKlO,aAAI,CAACC,IAAI,CAACH,SAAS;oBAAU,GACpC,CAACwP,KAAK7J;wBACJ,IAAI6J,KAAK;4BACP,OAAO2X,OAAO3X;wBAChB;wBACAhI,QAAQ7B;oBACV;gBAEJ;gBAEAyf,4BAA4Bzf,KAAK,CAACK,IAAI,IACjCkhB,aAAa1kB,GAAG,CAAC,CAACzB,WACnBb,aAAI,CAACC,IAAI,CAACsD,OAAOzD,OAAO,EAAE,UAAUe;YAG1C;YAEA,MAAMqmB,WAAqC;gBACzC;oBACEjX,aAAa;oBACbC,iBAAiB3M,OAAOmD,YAAY,CAAC2O,SAAS,GAAG,IAAI;gBACvD;gBACA;oBACEpF,aAAa;oBACbC,iBAAiB3M,OAAOmD,YAAY,CAACogB,WAAW,GAAG,IAAI;gBACzD;gBACA;oBACE7W,aAAa;oBACbC,iBAAiB3M,OAAOmD,YAAY,CAACygB,iBAAiB,GAAG,IAAI;gBAC/D;gBACA;oBACElX,aAAa;oBACbC,iBAAiB3M,OAAOmD,YAAY,CAACgP,GAAG,GAAG,IAAI;gBACjD;gBACA;oBACEzF,aAAa;oBACbC,iBAAiBkX,IAAAA,kCAA0B,EAAC7jB,UAAU,IAAI;gBAC5D;aACD;YACDyJ,UAAUY,MAAM,CACdsZ,SAAS5kB,GAAG,CAAC,CAAC+kB;gBACZ,OAAO;oBACLlX,WAAWC,iCAAyB;oBACpCC,SAASgX;gBACX;YACF;YAGF,MAAMlkB,iCACJrD,SACAolB;YAGF,iDAAiD;YACjD,sDAAsD;YACtD,IAAI1b,kBAAkB,CAACQ,aAAa;gBAClCvJ,KAAImL,IAAI,CAAC;gBAET,MAAMlH,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMwG,IAAAA,gCAAe,EAAC;wBACpB/L;wBACAyD;oBACF;gBACF;YACJ;YAEA,MAAMuB,qBAAyC,MAAMxD,aACnDtB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE4c,+BAAmB;YAG1D,MAAMje,oBAAuC;gBAC3C2C,SAAS;gBACTpC,QAAQ,CAAC;gBACTQ,eAAe,CAAC;gBAChB2kB,gBAAgB,EAAE;gBAClBC,SAASzV;YACX;YAEA,MAAM0V,qBAA+B,EAAE;YAEvC,MAAM,EAAE/Q,IAAI,EAAE,GAAGlT;YAEjB,MAAMkkB,wBAAwBrD,+BAAmB,CAAChiB,MAAM,CACtD,CAACjD,OACC4L,WAAW,CAAC5L,KAAK,IACjB4L,WAAW,CAAC5L,KAAK,CAACuO,UAAU,CAAC;YAEjC+Z,sBAAsBC,OAAO,CAAC,CAACvoB;gBAC7B,IAAI,CAAC4C,SAAS4lB,GAAG,CAACxoB,SAAS,CAAC2e,0BAA0B;oBACpD7Y,YAAY8O,GAAG,CAAC5U;gBAClB;YACF;YAEA,MAAMyoB,cAAcH,sBAAsB5hB,QAAQ,CAAC;YACnD,MAAMgiB,sBACJ,CAACD,eAAe,CAAC3J,yBAAyB,CAACH;YAE7C,MAAMgK,gBAAgB;mBAAI7iB;mBAAgBlD;aAAS;YACnD,MAAMgmB,iBAAiBtL,YAAYkL,GAAG,CAACnT,4CAAgC;YACvE,MAAMwT,kBAAkBzT,aAAawT;YAErC,MAAMhP,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;YACd;YAEA,sDAAsD;YACtD,mBAAmB;YACnB,yBAAyB;YACzB,gCAAgC;YAChC,IACE,CAAC7O,iBACA2d,CAAAA,cAAcnb,MAAM,GAAG,KACtBga,qBACAkB,uBACA1iB,MAAK,GACP;gBACA,MAAM8iB,uBACJvjB,cAAcU,UAAU,CAAC;gBAC3B,MAAM6iB,qBAAqB5iB,YAAY,CAAC;oBACtC6iB,IAAAA,8BAAsB,EACpB;2BACKJ;2BACAnjB,SAASY,KAAK,CAACnD,MAAM,CAAC,CAACjD,OAAS,CAAC2oB,cAAcjiB,QAAQ,CAAC1G;qBAC5D,EACD4C,UACA,IAAIqZ,IACF9J,MAAMC,IAAI,CAACiL,gBAAgBta,OAAO,IAAII,GAAG,CACvC,CAAC,CAACnD,MAAMgD,OAAO;wBACb,OAAO;4BAAChD;4BAAMgD,OAAOG,GAAG,CAAC,CAACC,QAAUA,MAAME,QAAQ;yBAAE;oBACtD;oBAIN,MAAMqG,YAAYzB,QAAQ,aACvB0B,OAAO;oBAEV,MAAMof,eAAmC;wBACvC,GAAG5kB,MAAM;wBACT,sEAAsE;wBACtE,+BAA+B;wBAC/B,wEAAwE;wBACxE,6DAA6D;wBAC7D6kB,eAAe,CAACC;4BACd,+DAA+D;4BAC/D,iEAAiE;4BACjE,uEAAuE;4BACvE,UAAU;4BACV,EAAE;4BACF,6DAA6D;4BAC7DtmB,SAAS2lB,OAAO,CAAC,CAACvoB;gCAChB,IAAI8W,IAAAA,qBAAc,EAAC9W,OAAO;oCACxBqoB,mBAAmB1hB,IAAI,CAAC3G;oCAExB,IAAIgd,uBAAuBwL,GAAG,CAACxoB,OAAO;wCACpC,iEAAiE;wCACjE,mBAAmB;wCACnB,IAAIsX,MAAM;4CACR4R,UAAU,CAAC,CAAC,CAAC,EAAE5R,KAAKuI,aAAa,GAAG7f,MAAM,CAAC,GAAG;gDAC5CA;gDACAmpB,gBAAgB;4CAClB;wCACF,OAAO;4CACLD,UAAU,CAAClpB,KAAK,GAAG;gDACjBA;gDACAmpB,gBAAgB;4CAClB;wCACF;oCACF,OAAO;wCACL,iEAAiE;wCACjE,iCAAiC;wCACjC,OAAOD,UAAU,CAAClpB,KAAK;oCACzB;gCACF;4BACF;4BAEA,oEAAoE;4BACpE,cAAc;4BACdqd,gBAAgBkL,OAAO,CAAC,CAACvlB,QAAQhD;gCAC/BgD,OAAOulB,OAAO,CAAC,CAACnlB;oCACd8lB,UAAU,CAAC9lB,MAAME,QAAQ,CAAC,GAAG;wCAC3BtD;wCACAopB,UAAUhmB,MAAM+gB,eAAe;oCACjC;gCACF;4BACF;4BAEA,IAAIqD,mBAAmB;gCACrB0B,UAAU,CAAC,OAAO,GAAG;oCACnBlpB,MAAMkV,cAAc,SAAS;gCAC/B;4BACF;4BAEA,IAAIwT,qBAAqB;gCACvBQ,UAAU,CAAC,OAAO,GAAG;oCACnBlpB,MAAM;gCACR;4BACF;4BAEA,wDAAwD;4BACxD,gDAAgD;4BAChDsd,YAAYiL,OAAO,CAAC,CAACvlB,QAAQmf;gCAC3B,MAAM6B,YAAYtG,kBAAkB2L,GAAG,CAAClH;gCACxC,MAAMmH,iBAAiBtF,CAAAA,6BAAAA,UAAWQ,OAAO,MAAK;gCAE9C,MAAM7C,oBAAoBqC,YACtBuF,IAAAA,2BAAsB,EAACnlB,OAAOmD,YAAY,CAACgP,GAAG,EAAEyN,aAChDzY;gCAEJvI,OAAOulB,OAAO,CAAC,CAACnlB;oCACd,8DAA8D;oCAC9D,wDAAwD;oCACxD,0DAA0D;oCAC1D,IACEA,MAAMmhB,kBAAkB,IACxBnhB,MAAMmhB,kBAAkB,CAAC/W,MAAM,GAAG,GAClC;wCACA;oCACF;oCAEA0b,UAAU,CAAC9lB,MAAME,QAAQ,CAAC,GAAG;wCAC3BtD,MAAMmiB;wCACNiH,UAAUhmB,MAAM+gB,eAAe;wCAC/BqF,sBAAsBpmB,MAAMghB,mBAAmB;wCAC/CqF,iBAAiBH;wCACjBI,WAAW;wCACXC,oBAAoBhI;oCACtB;gCACF;4BACF;4BAEA,gEAAgE;4BAChE,gEAAgE;4BAChE,2DAA2D;4BAC3D,wCAAwC;4BACxC,KAAK,MAAM,EACT3hB,IAAI,EACJmiB,eAAe,EAChB,IAAI5E,mBAAmBqM,MAAM,GAAI;gCAChCV,UAAU,CAAClpB,KAAK,GAAG;oCACjBA,MAAMmiB;oCACNiH,UAAUppB;oCACVwpB,sBAAsBK,IAAAA,4BAAY,EAAC7pB;oCACnC,sDAAsD;oCACtD0pB,WAAW;oCACX,6DAA6D;oCAC7DC,oBAAoB;oCACpBG,sBAAsB;oCACtB,+DAA+D;oCAC/DL,iBAAiB;gCACnB;4BACF;4BAEA,IAAInS,MAAM;gCACR,KAAK,MAAMtX,QAAQ;uCACd8F;uCACAlD;uCACC4kB,oBAAoB;wCAAC;qCAAO,GAAG,EAAE;uCACjCkB,sBAAsB;wCAAC;qCAAO,GAAG,EAAE;iCACxC,CAAE;oCACD,MAAMqB,QAAQnnB,SAAS4lB,GAAG,CAACxoB;oCAC3B,MAAM8jB,YAAYhN,IAAAA,qBAAc,EAAC9W;oCACjC,MAAMgqB,aAAaD,SAAS/M,uBAAuBwL,GAAG,CAACxoB;oCAEvD,KAAK,MAAMiqB,UAAU3S,KAAK3U,OAAO,CAAE;4CAMzBumB;wCALR,+DAA+D;wCAC/D,IAAIa,SAASjG,aAAa,CAACkG,YAAY;wCACvC,MAAMpjB,aAAa,CAAC,CAAC,EAAEqjB,SAASjqB,SAAS,MAAM,KAAKA,MAAM;wCAE1DkpB,UAAU,CAACtiB,WAAW,GAAG;4CACvB5G,MAAMkpB,EAAAA,mBAAAA,UAAU,CAAClpB,KAAK,qBAAhBkpB,iBAAkBlpB,IAAI,KAAIA;4CAChCkqB,SAASD;4CACTd,gBAAgBa;wCAClB;oCACF;oCAEA,IAAID,OAAO;wCACT,qDAAqD;wCACrD,OAAOb,UAAU,CAAClpB,KAAK;oCACzB;gCACF;4BACF;4BAEA,OAAOkpB;wBACT;oBACF;oBAEA,MAAMhf,SAASrJ,aAAI,CAACC,IAAI,CAACH,SAAS;oBAClC,MAAMwpB,eAAe,MAAMxgB,UACzBH,KACA;wBACEQ,YAAYgf;wBACZvf;wBACAQ,QAAQ;wBACRF,aAAa;wBACbU;wBACArE,OAAOuiB;wBACPze;wBACAkgB,eAAe;wBACf1hB,YAAYpB,mBAAmB0hB;oBACjC,GACAzjB;oBAGF,sDAAsD;oBACtD,IAAI,CAAC4kB,cAAc;oBAEnB,MAAME,kBAAkB,CACtBC,YACAC,oBAAgC,KAAK;4BAGnCJ;wBADF,MAAMK,gBACJL,2BAAAA,aAAaM,MAAM,CAACpB,GAAG,CAACiB,gCAAxBH,yBAAqCK,YAAY;wBAEnD,IAAI,CAACA,cAAc;4BACjB,OAAO;gCAAEvG,YAAYsG;gCAAmBG,QAAQnf;4BAAU;wBAC5D;wBAEA,IACEif,aAAavG,UAAU,KAAK,SAC5BuG,aAAavG,UAAU,GAAG,KAC1BuG,aAAaE,MAAM,KAAKnf,WACxB;4BACA,OAAO;gCACL0Y,YAAYuG,aAAavG,UAAU;gCACnCyG,QAAQtmB,OAAOumB,UAAU;4BAC3B;wBACF;wBAEA,OAAOH;oBACT;oBAEA,IAAI/f,eAAexB,QAAQD,GAAG,CAAC4hB,sBAAsB,KAAK,KAAK;wBAC7DC,IAAAA,oCAAkB,EAACV;oBACrB;oBAEAW,IAAAA,qDAA+B,EAAC;wBAC9BnqB,SAASyD,OAAOzD,OAAO;wBACvBoqB,QAAQ;4BACNhf;+BACGoe,aAAaa,2BAA2B,CAACpB,MAAM;yBACnD;oBACH;oBAEAnnB,kBAAkB0lB,cAAc,GAAGhW,MAAMC,IAAI,CAC3C+X,aAAac,gBAAgB;oBAG/B,2CAA2C;oBAC3C,KAAK,MAAMjrB,QAAQ8F,YAAa;wBAC9B,MAAMolB,eAAeC,IAAAA,oBAAW,EAACnrB,MAAMW,SAAS4K,WAAW;wBAC3D,MAAM3J,YAAE,CAACwpB,MAAM,CAACF;oBAClB;oBAEA5N,YAAYiL,OAAO,CAAC,CAACxE,mBAAmB5B;4BAWbxE;wBAVzB,MAAM3d,OAAOwd,mBAAmB6L,GAAG,CAAClH;wBACpC,IAAI,CAACniB,MAAM,MAAM,qBAAoC,CAApC,IAAIqrB,8BAAc,CAAC,mBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAmC;wBAEpD,MAAMrH,YAAYtG,kBAAkB2L,GAAG,CAAClH;wBACxC,IAAI,CAAC6B,WAAW,MAAM,qBAA0C,CAA1C,IAAIqH,8BAAc,CAAC,yBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyC;wBAE/D,IAAIC,oBACFtH,UAAUC,UAAU,KAAK,KACzBoG,gBAAgBrqB,MAAMikB,UAAU,KAAK;wBAEvC,IAAIqH,uBAAqB3N,iBAAAA,UAAU0L,GAAG,CAACrpB,0BAAd2d,eAAqBkE,QAAQ,GAAE;4BACtD,uEAAuE;4BACvE,qFAAqF;4BACrFlE,UAAUiG,GAAG,CAAC5jB,MAAM;gCAClB,GAAI2d,UAAU0L,GAAG,CAACrpB,KAAK;gCACvB6hB,UAAU;gCACVD,OAAO;4BACT;wBACF;wBAEA,MAAM2J,oBAAoBC,IAAAA,gCAAe,EAACrJ;wBAE1C,kEAAkE;wBAClE,yBAAyB;wBACzB,MAAMR,oBACJ,CAAC4J,qBACDhC,IAAAA,2BAAsB,EAACnlB,OAAOmD,YAAY,CAACgP,GAAG,EAAEyN,aAC5C,OACAzY;wBAEN,MAAMkgB,sBACJ,uEAAuE;wBACvErnB,OAAOsnB,eAAe,IAAIC,oCAA6B;wBAEzD,0FAA0F;wBAC1F,4CAA4C;wBAC5C,MAAMC,YAAwB;4BAC5B;gCAAEC,MAAM;gCAAU3N,KAAK4N,+BAAa;4BAAC;4BACrC;gCACED,MAAM;gCACN3N,KAAK;gCACL+H,OAAO;4BACT;4BACA,iGAAiG;4BACjG,iGAAiG;+BAC7FtE,oBACA;gCACE;oCACEkK,MAAM;oCACN3N,KAAK;oCACL+H,OAAOwF;gCACT;6BACD,GACD,EAAE;yBACP;wBAED,mEAAmE;wBACnE,6DAA6D;wBAC7D,mEAAmE;wBACnE,8DAA8D;wBAC9D,2BAA2B;wBAC3B,MAAMzoB,SAA6B,EAAE;wBACrC,MAAMQ,gBAAoC,EAAE;wBAE5C,mEAAmE;wBACnE,iEAAiE;wBACjE,+DAA+D;wBAC/D,iEAAiE;wBACjE,mDAAmD;wBACnD,IAAIuoB,yBAA6C,EAAE;wBACnD,IAAIC,uBAA2C,EAAE;wBACjD,KAAK,MAAMC,oBAAoBlI,kBAAmB;4BAChD,IACEkI,iBAAiB7H,mBAAmB,IACpC6H,iBAAiB7H,mBAAmB,CAAC5W,MAAM,GAAG,GAC9C;gCACAue,uBAAuBplB,IAAI,CAACslB;4BAC9B,OAAO;gCACLD,qBAAqBrlB,IAAI,CAACslB;4BAC5B;wBACF;wBAEAF,yBAAyBG,IAAAA,4BAAqB,EAC5CH,wBACA,CAACE,mBAAqBA,iBAAiB3oB,QAAQ;wBAEjD0oB,uBAAuBE,IAAAA,4BAAqB,EAC1CF,sBACA,CAACC,mBAAqBA,iBAAiB3oB,QAAQ;wBAGjDygB,oBAAoB;+BACfiI;+BACAD;yBACJ;wBAED,KAAK,MAAME,oBAAoBlI,kBAAmB;4BAChD,+BAA+B;4BAC/B,iCAAiC;4BACjC,IAAIkI,iBAAiB3oB,QAAQ,KAAK6oB,sCAA0B,EAAE;gCAC5D;4BACF;4BAEA,IACExK,qBACAsK,iBAAiB7H,mBAAmB,IACpC6H,iBAAiB7H,mBAAmB,CAAC5W,MAAM,GAAG,GAC9C;gCACA,6DAA6D;gCAC7D,8BAA8B;gCAC9BhK,cAAcmD,IAAI,CAACslB;4BACrB,OAAO;gCACL,4DAA4D;gCAC5D,gCAAgC;gCAChCjpB,OAAO2D,IAAI,CAACslB;4BACd;wBACF;wBAEA,gCAAgC;wBAChC,KAAK,MAAM7oB,SAASJ,OAAQ;4BAC1B,IAAI8T,IAAAA,qBAAc,EAAC9W,SAASoD,MAAME,QAAQ,KAAKtD,MAAM;4BACrD,IAAIoD,MAAME,QAAQ,KAAK6oB,sCAA0B,EAAE;4BAEnD,MAAM,EACJC,WAAW,CAAC,CAAC,EACb9G,eAAe,EACf+G,YAAY,EACb,GAAGlC,aAAaM,MAAM,CAACpB,GAAG,CAACjmB,MAAME,QAAQ,KAAK,CAAC;4BAEhD,MAAMknB,eAAeH,gBACnBjnB,MAAME,QAAQ,EACd0gB,UAAUC,UAAU;4BAGtBtG,UAAUiG,GAAG,CAACxgB,MAAME,QAAQ,EAAE;gCAC5B,GAAIqa,UAAU0L,GAAG,CAACjmB,MAAME,QAAQ,CAAC;gCACjC+oB;gCACA/G;gCACAH,qBAAqBqF;4BACvB;4BAEA,uEAAuE;4BACvE7M,UAAUiG,GAAG,CAAC5jB,MAAM;gCAClB,GAAI2d,UAAU0L,GAAG,CAACrpB,KAAK;gCACvBqsB;gCACA/G;gCACAH,qBAAqBqF;4BACvB;4BAEA,IAAIA,aAAavG,UAAU,KAAK,GAAG;gCACjC,MAAMqI,kBAAkB/K,IAAAA,oCAAiB,EAACne,MAAME,QAAQ;gCAExD,IAAIipB;gCACJ,IAAIhB,mBAAmB;oCACrBgB,YAAY;gCACd,OAAO;oCACLA,YAAY1rB,aAAI,CAAC2rB,KAAK,CAAC1rB,IAAI,CAAC,GAAGwrB,kBAAkBlU,qBAAU,EAAE;gCAC/D;gCAEA,IAAIqU;gCACJ,6DAA6D;gCAC7D,6DAA6D;gCAC7D,6DAA6D;gCAC7D,uBAAuB;gCACvB,IAAI,CAAClB,qBAAqBlV,iBAAiB;oCACzCoW,oBAAoB5rB,aAAI,CAAC2rB,KAAK,CAAC1rB,IAAI,CACjC,GAAGwrB,kBAAkBhU,8BAAmB,EAAE;gCAE9C;gCAEA,MAAMoU,OAAOC,IAAAA,mBAAW,EAACP;gCAEzB3pB,kBAAkBO,MAAM,CAACI,MAAME,QAAQ,CAAC,GAAG;oCACzCspB,eAAeF,KAAKG,MAAM;oCAC1BC,gBAAgBJ,KAAK1f,OAAO;oCAC5B+f,eAAe1W,kBACXsL,oBACEqL,4BAAa,CAACC,gBAAgB,GAC9BD,4BAAa,CAACE,MAAM,GACtB3hB;oCACJ4hB,iBAAiBxL;oCACjByL,uBAAuBxB;oCACvByB,0BAA0B7C,aAAavG,UAAU;oCACjDqJ,sBAAsB9C,aAAaE,MAAM;oCACzCxnB,UAAUlD;oCACVusB;oCACAE;oCACAc,aAAa9tB;gCACf;4BACF,OAAO;gCACL6rB,oBAAoB;gCACpB,8DAA8D;gCAC9D,oBAAoB;gCACpB3N,UAAUiG,GAAG,CAACxgB,MAAME,QAAQ,EAAE;oCAC5B,GAAIqa,UAAU0L,GAAG,CAACjmB,MAAME,QAAQ,CAAC;oCACjCse,OAAO;oCACPC,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,CAACyJ,qBAAqBxU,IAAAA,qBAAc,EAAC9W,OAAO;4BAC9C,iEAAiE;4BACjE,0DAA0D;4BAC1D,sBAAsB;4BACtB,IAAI,CAAC2hB,mBAAmB;gCACtBne,cAAcmD,IAAI,CAAC;oCACjBrD,UAAUtD;oCACVmkB,iBAAiBnkB;oCACjBokB,qBAAqB7Y;oCACrB8Y,cACE5G,cAAc4L,GAAG,CAAClH,oBAClBwC,sBAAY,CAAC6I,SAAS;oCACxBjJ,oBAAoBhZ;gCACtB;4BACF;4BAEA,KAAK,MAAMnI,SAASI,cAAe;oCAGhB2mB,0BAwFM/mB;gCA1FvB,MAAMkpB,kBAAkB/K,IAAAA,oCAAiB,EAACne,MAAME,QAAQ;gCAExD,MAAM8oB,YAAWjC,2BAAAA,aAAaM,MAAM,CAACpB,GAAG,CACtCjmB,MAAME,QAAQ,sBADC6mB,yBAEdiC,QAAQ;gCAEX,MAAM5B,eAAeH,gBAAgBjnB,MAAME,QAAQ;gCAEnD,IAAIipB,YAA2B;gCAC/B,IAAI,CAAChB,mBAAmB;oCACtBgB,YAAY1rB,aAAI,CAAC2rB,KAAK,CAAC1rB,IAAI,CAAC,GAAGwrB,kBAAkBlU,qBAAU,EAAE;gCAC/D;gCAEA,IAAIqU;gCACJ,IAAI,CAAClB,qBAAqBlV,iBAAiB;oCACzCoW,oBAAoB5rB,aAAI,CAAC2rB,KAAK,CAAC1rB,IAAI,CACjC,GAAGwrB,kBAAkBhU,8BAAmB,EAAE;gCAE9C;gCAEA,IAAI,CAACiT,sBAAqBa,4BAAAA,SAAUqB,YAAY,GAAE;oCAChD,MAAMC,eAAehX,eAAelT,aAAa,CAACye,IAAI,CACpD,CAAC9K,IAAMA,EAAEnX,IAAI,KAAKA;oCAEpB,IAAI,CAAC0tB,cAAc;wCACjB,MAAM,qBAAoC,CAApC,IAAInd,MAAM,4BAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAmC;oCAC3C;oCAEAmd,aAAaC,yBAAyB,KAAK,EAAE;oCAC7C,KAAK,MAAMC,eAAexB,SAASqB,YAAY,CAAE;wCAC/C,MAAMI,SAASC,IAAAA,4DAA6B,EAC1C1qB,MAAME,QAAQ,EACdsqB;wCAEFF,aAAaC,yBAAyB,CAAChnB,IAAI,CAACknB;oCAC9C;gCACF;gCAEAlQ,UAAUiG,GAAG,CAACxgB,MAAME,QAAQ,EAAE;oCAC5B,GAAIqa,UAAU0L,GAAG,CAACjmB,MAAME,QAAQ,CAAC;oCACjCyqB,mBAAmB;oCACnB,gEAAgE;oCAChE,2CAA2C;oCAC3C1B,cAAc1K;gCAChB;gCAEA,MAAM0C,eACJjhB,MAAMihB,YAAY,IAAIM,sBAAY,CAAC6I,SAAS;gCAE9C,+DAA+D;gCAC/D,+DAA+D;gCAC/D,oDAAoD;gCACpD,iDAAiD;gCACjD,MAAMQ,uBACJrM,qBAAqB0C,iBAAiBM,sBAAY,CAACE,SAAS,GACxD2F,eACAjf;gCAEN,MAAM+B,WAAqB2gB,IAAAA,qCAA2B,EACpD5J,cACAjhB,MAAME,QAAQ;gCAGhB,MAAMopB,OACJN,YACAzK,qBACA0C,iBAAiBM,sBAAY,CAACE,SAAS,GACnC8H,IAAAA,mBAAW,EAACP,YACZ,CAAC;gCAEP3pB,kBAAkBe,aAAa,CAACJ,MAAME,QAAQ,CAAC,GAAG;oCAChD6pB,iBAAiBxL;oCACjBoL,eAAe1W,kBACXsL,oBACEqL,4BAAa,CAACC,gBAAgB,GAC9BD,4BAAa,CAACE,MAAM,GACtB3hB;oCACJ6hB,uBAAuBxB;oCACvB3rB,YAAYI,IAAAA,qCAAmB,EAC7BH,IAAAA,8BAAkB,EAACkD,MAAME,QAAQ,EAAE;wCACjCnD,iBAAiB;oCACnB,GAAGG,EAAE,CAACC,MAAM;oCAEdgsB;oCACAjf;oCACA4gB,kBAAkB,EAAEF,wCAAAA,qBAAsB/J,UAAU;oCACpDkK,cAAc,EAAEH,wCAAAA,qBAAsBtD,MAAM;oCAC5C0D,gBAAgB1B,KAAKG,MAAM;oCAC3BwB,iBAAiB3B,KAAK1f,OAAO;oCAC7BuX,oBAAoBnhB,MAAMmhB,kBAAkB;oCAC5C+J,qBAAqBlrB,EAAAA,6BAAAA,MAAMghB,mBAAmB,qBAAzBhhB,2BAA2BoK,MAAM,IAClDxN,OACAuL;oCACJgjB,gBAAgB,CAAChC,YACb,OACAlsB,IAAAA,qCAAmB,EACjBH,IAAAA,8BAAkB,EAACqsB,WAAW;wCAC5BpsB,iBAAiB;wCACjBquB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGnuB,EAAE,CAACC,MAAM;oCAElBksB;oCACAiC,wBAAwB,CAACjC,oBACrBlhB,YACAlL,IAAAA,qCAAmB,EACjBH,IAAAA,8BAAkB,EAACusB,mBAAmB;wCACpCtsB,iBAAiB;wCACjBquB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGnuB,EAAE,CAACC,MAAM;oCAElBgtB,aAAa9tB;gCACf;4BACF;wBACF;oBACF;oBAEA,MAAMkvB,mBAAmB,OACvBC,YACA5uB,MACAqG,MACA0jB,OACA8E,KACAC,oBAAoB,KAAK;wBAEzB,OAAOhG,qBACJ7iB,UAAU,CAAC,sBACXC,YAAY,CAAC;4BACZG,OAAO,GAAGA,KAAK,CAAC,EAAEwoB,KAAK;4BACvB,MAAME,OAAOluB,aAAI,CAACC,IAAI,CAACoJ,QAAQ7D;4BAC/B,MAAMqO,WAAWyW,IAAAA,oBAAW,EAC1ByD,YACAjuB,SACA4K,WACA;4BAGF,MAAMyjB,eAAenuB,aAAI,CACtBgG,QAAQ,CACPhG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,GACnCjD,aAAI,CAACC,IAAI,CACPD,aAAI,CAACC,IAAI,CACP4T,UACA,yDAAyD;4BACzD,4BAA4B;4BAC5Bka,WACGK,KAAK,CAAC,GACNC,KAAK,CAAC,KACN/rB,GAAG,CAAC,IAAM,MACVrC,IAAI,CAAC,OAEVuF,OAGHzB,OAAO,CAAC,OAAO;4BAElB,IACE,CAACmlB,SACD,CACE,mDAAmD;4BACnD,kDAAkD;4BAEhD9E,CAAAA,+BAAmB,CAACve,QAAQ,CAAC1G,SAC7B,CAACsoB,sBAAsB5hB,QAAQ,CAAC1G,KAAI,GAGxC;gCACA4d,aAAa,CAAC5d,KAAK,GAAGgvB;4BACxB;4BAEA,MAAMG,OAAOtuB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEkrB;4BAClD,MAAMI,aACJ3sB,kBAAkB0lB,cAAc,CAACzhB,QAAQ,CAAC1G;4BAE5C,2DAA2D;4BAC3D,0DAA0D;4BAC1D,qBAAqB;4BACrB,IAAI,AAAC,CAAA,CAACsX,QAAQwX,iBAAgB,KAAM,CAACM,YAAY;gCAC/C,MAAMxtB,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACooB,OAAO;oCAAEnoB,WAAW;gCAAK;gCACrD,MAAMpF,YAAE,CAACytB,MAAM,CAACN,MAAMI;4BACxB,OAAO,IAAI7X,QAAQ,CAACyS,OAAO;gCACzB,wDAAwD;gCACxD,oDAAoD;gCACpD,OAAOnM,aAAa,CAAC5d,KAAK;4BAC5B;4BAEA,IAAIsX,MAAM;gCACR,IAAIwX,mBAAmB;gCAEvB,MAAMQ,YAAYtvB,SAAS,MAAMa,aAAI,CAAC0uB,OAAO,CAAClpB,QAAQ;gCACtD,MAAMmpB,sBAAsBR,aAAaC,KAAK,CAC5C,SAASzhB,MAAM;gCAGjB,KAAK,MAAMyc,UAAU3S,KAAK3U,OAAO,CAAE;oCACjC,MAAM8sB,UAAU,CAAC,CAAC,EAAExF,SAASjqB,SAAS,MAAM,KAAKA,MAAM;oCAEvD,IACE+pB,SACAtnB,kBAAkB0lB,cAAc,CAACzhB,QAAQ,CAAC+oB,UAC1C;wCACA;oCACF;oCAEA,MAAMC,sBAAsB7uB,aAAI,CAC7BC,IAAI,CACH,SACAmpB,SAASqF,WACT,8DAA8D;oCAC9D,+BAA+B;oCAC/BtvB,SAAS,MAAM,KAAKwvB,qBAErB5qB,OAAO,CAAC,OAAO;oCAElB,MAAM+qB,cAAc9uB,aAAI,CAACC,IAAI,CAC3BoJ,QACA+f,SAASqF,WACTtvB,SAAS,MAAM,KAAKqG;oCAEtB,MAAMupB,cAAc/uB,aAAI,CAACC,IAAI,CAC3BH,SACAmD,4BAAgB,EAChB4rB;oCAGF,IAAI,CAAC3F,OAAO;wCACVnM,aAAa,CAAC6R,QAAQ,GAAGC;oCAC3B;oCACA,MAAM9tB,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAAC6oB,cAAc;wCACxC5oB,WAAW;oCACb;oCACA,MAAMpF,YAAE,CAACytB,MAAM,CAACM,aAAaC;gCAC/B;4BACF;wBACF;oBACJ;oBAEA,eAAeC;wBACb,OAAO/G,qBACJ7iB,UAAU,CAAC,gCACXC,YAAY,CAAC;4BACZ,MAAM6oB,OAAOluB,aAAI,CAACC,IAAI,CACpBH,SACA,UACA,OACA;4BAEF,MAAM+uB,sBAAsB7uB,aAAI,CAC7BC,IAAI,CAAC,SAAS,YACd8D,OAAO,CAAC,OAAO;4BAElB,IAAIzD,IAAAA,cAAU,EAAC4tB,OAAO;gCACpB,MAAMntB,YAAE,CAACqF,QAAQ,CACf8nB,MACAluB,aAAI,CAACC,IAAI,CAACH,SAAS,UAAU+uB;gCAE/B9R,aAAa,CAAC,OAAO,GAAG8R;4BAC1B;wBACF;oBACJ;oBAEA,oEAAoE;oBACpE,IAAI7G,iBAAiB;wBACnB,MAAMgH;oBACR,OAAO;wBACL,sGAAsG;wBACtG,IAAI,CAAC3a,eAAe,CAACE,aAAaoS,mBAAmB;4BACnD,MAAMmH,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;wBAC3D;oBACF;oBAEA,IAAIjG,qBAAqB;wBACvB,MAAMiG,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;oBAC3D;oBAEA,KAAK,MAAM3uB,QAAQ2oB,cAAe;wBAChC,MAAMoB,QAAQnnB,SAAS4lB,GAAG,CAACxoB;wBAC3B,MAAM8vB,sBAAsB9S,uBAAuBwL,GAAG,CAACxoB;wBACvD,MAAM8jB,YAAYhN,IAAAA,qBAAc,EAAC9W;wBACjC,MAAM+vB,SAAS5S,eAAeqL,GAAG,CAACxoB;wBAClC,MAAMqG,OAAOkb,IAAAA,oCAAiB,EAACvhB;wBAE/B,MAAMgwB,WAAWrS,UAAU0L,GAAG,CAACrpB;wBAC/B,MAAMiwB,eAAe9F,aAAa+F,MAAM,CAAC7G,GAAG,CAACrpB;wBAC7C,IAAIgwB,YAAYC,cAAc;4BAC5B,qBAAqB;4BACrB,IAAID,SAAShO,aAAa,EAAE;gCAC1BgO,SAAS3K,gBAAgB,GAAG2K,SAAShO,aAAa,CAAC7e,GAAG,CACpD,CAACuR;oCACC,MAAMoG,WAAWmV,aAAaE,eAAe,CAAC9G,GAAG,CAAC3U;oCAClD,IAAI,OAAOoG,aAAa,aAAa;wCACnC,MAAM,qBAAyC,CAAzC,IAAIvK,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEA,OAAOuK;gCACT;4BAEJ;4BACAkV,SAAS5K,YAAY,GAAG6K,aAAaE,eAAe,CAAC9G,GAAG,CAACrpB;wBAC3D;wBAEA,+DAA+D;wBAC/D,gEAAgE;wBAChE,YAAY;wBACZ,MAAMowB,gBAAgB,CAAErG,CAAAA,SAASjG,aAAa,CAACgM,mBAAkB;wBAEjE,IAAIM,eAAe;4BACjB,MAAMzB,iBAAiB3uB,MAAMA,MAAMqG,MAAM0jB,OAAO;wBAClD;wBAEA,IAAIgG,UAAW,CAAA,CAAChG,SAAUA,SAAS,CAACjG,SAAS,GAAI;4BAC/C,MAAMuM,UAAU,GAAGhqB,KAAK,IAAI,CAAC;4BAC7B,MAAMsoB,iBAAiB3uB,MAAMqwB,SAASA,SAAStG,OAAO;4BAEtD,IAAIA,OAAO;gCACT,MAAM4E,iBAAiB3uB,MAAMqwB,SAASA,SAAStG,OAAO;4BACxD;wBACF;wBAEA,IAAIA,OAAO;4BACT,yDAAyD;4BACzD,oDAAoD;4BACpD,IAAI,CAACjG,WAAW;gCACd,MAAM6K,iBAAiB3uB,MAAMA,MAAMqG,MAAM0jB,OAAO;gCAEhD,IAAIzS,MAAM;oCACR,+DAA+D;oCAC/D,KAAK,MAAM2S,UAAU3S,KAAK3U,OAAO,CAAE;wCACjC,MAAM2tB,aAAa,CAAC,CAAC,EAAErG,SAASjqB,SAAS,MAAM,KAAKA,MAAM;wCAE1D,MAAMwqB,eAAeH,gBAAgBiG;wCAErC7tB,kBAAkBO,MAAM,CAACstB,WAAW,GAAG;4CACrCjD,0BAA0B7C,aAAavG,UAAU;4CACjDqJ,sBAAsB9C,aAAaE,MAAM;4CACzCyC,iBAAiB5hB;4CACjBwhB,eAAexhB;4CACfrI,UAAU;4CACVqpB,WAAW1rB,aAAI,CAAC2rB,KAAK,CAAC1rB,IAAI,CACxB,eACA4B,SACA,GAAG2D,KAAK,KAAK,CAAC;4CAEhBomB,mBAAmBlhB;4CACnBgiB,aAAa9tB;wCACf;oCACF;gCACF,OAAO;oCACL,MAAM+qB,eAAeH,gBAAgBrqB;oCAErCyC,kBAAkBO,MAAM,CAAChD,KAAK,GAAG;wCAC/BqtB,0BAA0B7C,aAAavG,UAAU;wCACjDqJ,sBAAsB9C,aAAaE,MAAM;wCACzCyC,iBAAiB5hB;wCACjBwhB,eAAexhB;wCACfrI,UAAU;wCACVqpB,WAAW1rB,aAAI,CAAC2rB,KAAK,CAAC1rB,IAAI,CACxB,eACA4B,SACA,GAAG2D,KAAK,KAAK,CAAC;wCAEhB,6CAA6C;wCAC7ComB,mBAAmBlhB;wCACnBgiB,aAAa9tB;oCACf;gCACF;gCACA,IAAIuwB,UAAU;oCACZA,SAAS7K,mBAAmB,GAAGkF,gBAAgBrqB;gCACjD;4BACF,OAAO;gCACL,oEAAoE;gCACpE,4CAA4C;gCAC5C,iEAAiE;gCACjE,yCAAyC;gCACzC,KAAK,MAAMoD,SAASia,gBAAgBgM,GAAG,CAACrpB,SAAS,EAAE,CAAE;oCACnD,MAAMuwB,WAAWhP,IAAAA,oCAAiB,EAACne,MAAME,QAAQ;oCACjD,MAAMqrB,iBACJ3uB,MACAoD,MAAME,QAAQ,EACditB,UACAxG,OACA,QACA;oCAEF,MAAM4E,iBACJ3uB,MACAoD,MAAME,QAAQ,EACditB,UACAxG,OACA,QACA;oCAGF,IAAIgG,QAAQ;wCACV,MAAMM,UAAU,GAAGE,SAAS,IAAI,CAAC;wCACjC,MAAM5B,iBACJ3uB,MACAqwB,SACAA,SACAtG,OACA,QACA;wCAEF,MAAM4E,iBACJ3uB,MACAqwB,SACAA,SACAtG,OACA,QACA;oCAEJ;oCAEA,MAAMS,eAAeH,gBAAgBjnB,MAAME,QAAQ;oCAEnDb,kBAAkBO,MAAM,CAACI,MAAME,QAAQ,CAAC,GAAG;wCACzC+pB,0BAA0B7C,aAAavG,UAAU;wCACjDqJ,sBAAsB9C,aAAaE,MAAM;wCACzCyC,iBAAiB5hB;wCACjBwhB,eAAexhB;wCACfrI,UAAUlD;wCACVusB,WAAW1rB,aAAI,CAAC2rB,KAAK,CAAC1rB,IAAI,CACxB,eACA4B,SACA,GAAG6e,IAAAA,oCAAiB,EAACne,MAAME,QAAQ,EAAE,KAAK,CAAC;wCAE7C,6CAA6C;wCAC7CmpB,mBAAmBlhB;wCACnBgiB,aAAa9tB;oCACf;oCAEA,IAAIuwB,UAAU;wCACZA,SAAS7K,mBAAmB,GAAGqF;oCACjC;gCACF;4BACF;wBACF;oBACF;oBAEA,iCAAiC;oBACjC,MAAM5oB,YAAE,CAAC4uB,EAAE,CAACtmB,QAAQ;wBAAElD,WAAW;wBAAMypB,OAAO;oBAAK;oBACnD,MAAMzuB,cAAc8X,mBAAmB8D;gBACzC;gBAEA,sEAAsE;gBACtE,sBAAsB;gBACtB,MAAMrY,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMlE,cAAcwU,oBAAoBE;YAC1D;YAEA,MAAMga,mBAAmBnU,IAAAA,gBAAa,EAAC;YACvC,IAAIoU,qBAAqBpU,IAAAA,gBAAa,EAAC,CAAC,uBAAuB,CAAC;YAEhE,wCAAwC;YACxC8B,OAAOlU,GAAG;YAEV,MAAMymB,cAAc3nB,QAAQsV,MAAM,CAACD;YACnCzQ,UAAUY,MAAM,CACdoiB,IAAAA,0BAAkB,EAACrf,YAAY;gBAC7BgK,mBAAmBoV,WAAW,CAAC,EAAE;gBACjCE,iBAAiBhrB,YAAY0b,IAAI;gBACjCuP,sBAAsBnuB,SAAS4e,IAAI;gBACnCwP,sBAAsB5T,iBAAiBoE,IAAI;gBAC3CyP,cACEzf,WAAWhE,MAAM,GAChB1H,CAAAA,YAAY0b,IAAI,GAAG5e,SAAS4e,IAAI,GAAGpE,iBAAiBoE,IAAI,AAAD;gBAC1D0P,cAAc1J;gBACd2J,oBACEvS,CAAAA,gCAAAA,aAAclY,QAAQ,CAAC,uBAAsB;gBAC/C0qB,eAAejkB,iBAAiBK,MAAM;gBACtC6jB,cAAcrkB,QAAQQ,MAAM;gBAC5B8jB,gBAAgBpkB,UAAUM,MAAM,GAAG;gBACnC+jB,qBAAqBvkB,QAAQ/J,MAAM,CAAC,CAACkU,IAAW,CAAC,CAACA,EAAEqR,GAAG,EAAEhb,MAAM;gBAC/DgkB,sBAAsBrkB,iBAAiBlK,MAAM,CAAC,CAACkU,IAAW,CAAC,CAACA,EAAEqR,GAAG,EAC9Dhb,MAAM;gBACTikB,uBAAuBvkB,UAAUjK,MAAM,CAAC,CAACkU,IAAW,CAAC,CAACA,EAAEqR,GAAG,EAAEhb,MAAM;gBACnEkkB,iBAAiBhf,oBAAoB,IAAI;gBACzCqC;gBACA6H;gBACAC;gBACAC;gBACAC;YACF;YAGF,IAAI9R,8BAAgB,CAAC0mB,cAAc,EAAE;gBACnC,MAAMviB,SAASwiB,IAAAA,8BAAsB,EACnC3mB,8BAAgB,CAAC0mB,cAAc,CAACE,MAAM;gBAExChkB,UAAUY,MAAM,CAACW;gBACjBvB,UAAUY,MAAM,CACdqjB,IAAAA,4CAAoC,EAClC7mB,8BAAgB,CAAC0mB,cAAc,CAACI,6BAA6B;gBAGjE,MAAMC,kBAAkB/mB,8BAAgB,CAAC0mB,cAAc,CAACK,eAAe;gBAEvE,KAAK,MAAM,CAAC9T,KAAK+H,MAAM,IAAInjB,OAAOC,OAAO,CAACivB,iBAAkB;oBAC1DnkB,UAAUY,MAAM,CACdmjB,IAAAA,8BAAsB,EAAC;wBACrB;4BACE9gB,aAAaoN;4BACbnN,iBAAiBkV;wBACnB;qBACD;gBAEL;YACF;YAEA,IAAIrjB,SAAS4e,IAAI,GAAG,KAAKxb,QAAQ;oBAmDpB5B;gBAlDXikB,mBAAmBE,OAAO,CAAC,CAAC0J;oBAC1B,MAAM3F,kBAAkB/K,IAAAA,oCAAiB,EAAC0Q;oBAC1C,MAAM1F,YAAY1rB,aAAI,CAAC2rB,KAAK,CAAC1rB,IAAI,CAC/B,eACA4B,SACA,GAAG4pB,gBAAgB,KAAK,CAAC;oBAG3B7pB,kBAAkBe,aAAa,CAACyuB,SAAS,GAAG;wBAC1ChyB,YAAYI,IAAAA,qCAAmB,EAC7BH,IAAAA,8BAAkB,EAAC+xB,UAAU;4BAC3B9xB,iBAAiB;wBACnB,GAAGG,EAAE,CAACC,MAAM;wBAEd4sB,iBAAiB5hB;wBACjBwhB,eAAexhB;wBACfghB;wBACAjf,UAAU2P,yBAAyBuL,GAAG,CAACyJ,YACnC,OACAjV,uBAAuBwL,GAAG,CAACyJ,YACzB,GAAG3F,gBAAgB,KAAK,CAAC,GACzB;wBACN4B,oBAAoB3iB;wBACpB4iB,gBAAgB5iB;wBAChB+iB,qBAAqB/iB;wBACrBgZ,oBAAoBhZ;wBACpBgjB,gBAAgBluB,IAAAA,qCAAmB,EACjCH,IAAAA,8BAAkB,EAACqsB,WAAW;4BAC5BpsB,iBAAiB;4BACjBquB,eAAe;4BACfC,8BAA8B;wBAChC,GAAGnuB,EAAE,CAACC,MAAM;wBAEd,6CAA6C;wBAC7CksB,mBAAmBlhB;wBACnBmjB,wBAAwBnjB;wBACxBgiB,aAAa9tB;oBACf;gBACF;gBAEAwL,8BAAgB,CAAC2H,aAAa,GAAGD,aAAaC,aAAa;gBAC3D3H,8BAAgB,CAACinB,mBAAmB,GAClC9tB,OAAOmD,YAAY,CAAC2qB,mBAAmB;gBACzCjnB,8BAAgB,CAACknB,2BAA2B,GAC1C/tB,OAAOmD,YAAY,CAAC4qB,2BAA2B;gBAEjD,MAAM7vB,uBAAuB3B,SAAS8B;gBACtC,MAAMD,uBAAuBC,mBAAmB;oBAC9C9B;oBACA+B;oBACAC,OAAO,GAAEyB,eAAAA,OAAOkT,IAAI,qBAAXlT,aAAazB,OAAO;gBAC/B;YACF,OAAO;gBACL,MAAML,uBAAuB3B,SAAS;oBACpCyE,SAAS;oBACTpC,QAAQ,CAAC;oBACTQ,eAAe,CAAC;oBAChB4kB,SAASzV;oBACTwV,gBAAgB,EAAE;gBACpB;YACF;YAEA,MAAMhkB,oBAAoBxD,SAASyD;YACnC,MAAMpC,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAASyxB,yBAAa,GAAG;gBACrDhtB,SAAS;gBACTitB,kBAAkB,OAAOjuB,OAAO6kB,aAAa,KAAK;gBAClDqJ,qBAAqBluB,OAAOmuB,aAAa,KAAK;gBAC9C1T,qBAAqBA,wBAAwB;YAC/C;YACA,MAAMjd,YAAE,CAACwpB,MAAM,CAACvqB,aAAI,CAACC,IAAI,CAACH,SAAS6xB,yBAAa,GAAGrW,KAAK,CAAC,CAAChM;gBACxD,IAAIA,IAAIE,IAAI,KAAK,UAAU;oBACzB,OAAOwK,QAAQ1S,OAAO;gBACxB;gBACA,OAAO0S,QAAQiN,MAAM,CAAC3X;YACxB;YAEA,IAAIN,QAAQzL,OAAOmD,YAAY,CAACygB,iBAAiB,GAAG;gBAClD,MAAMziB,cACHU,UAAU,CAAC,0BACXC,YAAY,CAAC;oBACZ,MAAMusB,IAAAA,0CAAoB,EACxBjpB,KACA3I,aAAI,CAACC,IAAI,CAACH,SAASiD,oCAAwB;gBAE/C;YACJ;YAEA,MAAMqW;YAEN,IAAI0W,oBAAoB;gBACtBA,mBAAmBhL,cAAc;gBACjCgL,qBAAqBplB;YACvB;YAEA,IAAIP,eAAe;gBACjB1J,KAAImL,IAAI,CACN,CAAC,yGAAyG,CAAC;YAE/G;YAEA,IAAIrI,OAAO2b,MAAM,KAAK,UAAU;gBAC9B,MAAMxW,uBACJnF,QACAoF,KACAC,oBACAC,cACAnE;YAEJ;YAEA,IAAInB,OAAO2b,MAAM,KAAK,cAAc;gBAClC,MAAMza,yBACJC,eACA5E,SACA6E,UACAC,sBACAC,uBACAqgB,6BACApgB,oBACAC,mBACAC,wBACAC,aACAC,gBACAC;YAEJ;YAEA,IAAI0qB,kBAAkBA,iBAAiB/K,cAAc;YACrDvkB,QAAQC,GAAG;YAEX,IAAIoJ,aAAa;gBACflF,cACGU,UAAU,CAAC,uBACX4F,OAAO,CAAC,IAAM6mB,IAAAA,yBAAiB,EAAC;wBAAExlB;wBAAWD;wBAAUD;oBAAQ;YACpE;YAEA,MAAMzH,cAAcU,UAAU,CAAC,mBAAmBC,YAAY,CAAC,IAC7DysB,IAAAA,qBAAa,EAACntB,UAAUmY,WAAW;oBACjCiV,UAAUjyB;oBACV+B,SAASA;oBACTsL;oBACAwZ;oBACAnW,gBAAgBjN,OAAOiN,cAAc;oBACrCyM;oBACAD;oBACAlY;oBACA8a,UAAUrc,OAAOmD,YAAY,CAACkZ,QAAQ;gBACxC;YAGF,MAAMlb,cACHU,UAAU,CAAC,mBACXC,YAAY,CAAC,IAAM2H,UAAU+C,KAAK;YAErC,MAAMgK;QACR;IACF,EAAE,OAAOiY,GAAG;QACV,MAAMhlB,YAAmCilB,oBAAY,CAACzJ,GAAG,CAAC;QAC1D,IAAIxb,WAAW;YACbA,UAAUY,MAAM,CACdskB,IAAAA,wBAAgB,EAAC;gBACfxX,SAASc,uBAAuBxR;gBAChCmoB,WAAWC,yBAAyBJ;gBACpCrX,mBAAmB7T,KAAKG,KAAK,CAAC,AAACqD,CAAAA,KAAKC,GAAG,KAAKF,cAAa,IAAK;YAChE;QAEJ;QACA,MAAM2nB;IACR,SAAU;QACR,kDAAkD;QAClD,MAAMK,yBAAoB,CAACC,GAAG;QAE9B,IAAItoB,eAAe,CAAC5B,QAAQD,GAAG,CAACoqB,gBAAgB,EAAE;YAChDC,yBAAyBhoB;QAC3B;QAEA,6DAA6D;QAC7D,MAAMsB,IAAAA,qBAAc;QACpBC,IAAAA,4BAAuB;QAEvB,IAAI7B,kBAAkBM,cAAc;YAClCioB,IAAAA,oBAAW,EAAC;gBACVvoB;gBACAwoB,MAAM;gBACNC,YAAYhqB;gBACZ7I,SAAS0K,aAAa1K,OAAO;gBAC7B8yB,gBAAgB5oB;gBAChB6oB,MAAM;YACR;QACF;IACF;AACF;AAEA,SAASL,yBAAyBjvB,MAA2B;IAC3D,IAAIuvB,aACF,CAAC,8CAA8C,CAAC,GAChD/N,IAAAA,gBAAI,EACF,CAAC,yEAAyE,CAAC;IAE/E+N,cACE,WACA/N,IAAAA,gBAAI,EACF;IAEJ+N,cACE;IAEF,IAAI,EAACvvB,0BAAAA,OAAQmD,YAAY,CAACqsB,0BAA0B,GAAE;QACpDD,cACE;IACJ;IAEAA,cACE;IACFA,cACE;IAEFryB,KAAIE,IAAI,CAACmyB;AACX;AAEA,SAAStX,uBAAuBxR,WAAoB;IAClD,IAAIA,aAAa;QACf,OAAO;IACT;IAEA,IAAI5B,QAAQD,GAAG,CAAC6qB,WAAW,EAAE;QAC3B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASZ,yBAAyB9iB,GAAY;IAC5C,MAAME,OAAOyjB,IAAAA,yCAAoB,EAAC3jB;IAClC,IAAIE,QAAQ,MAAM;QAChB,OAAOA;IACT;IAEA,IAAIF,eAAeI,SAAS,UAAUJ,OAAO,OAAOA,IAAIE,IAAI,KAAK,UAAU;QACzE,OAAOF,IAAIE,IAAI;IACjB;IAEA,IAAIF,eAAeI,OAAO;QACxB,OAAOJ,IAAI4jB,IAAI;IACjB;IAEA,OAAO;AACT"}