{"version": 3, "sources": ["../../../src/build/templates/edge-ssr.ts"], "sourcesContent": ["import '../../server/web/globals'\nimport { adapter } from '../../server/web/adapter'\nimport { getRender } from '../webpack/loaders/next-edge-ssr-loader/render'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { initializeCacheHandlers } from '../../server/use-cache/handlers'\n\nimport Document from 'VAR_MODULE_DOCUMENT'\nimport * as appMod from 'VAR_MODULE_APP'\nimport * as userlandPage from 'VAR_USERLAND'\nimport * as userlandErrorPage from 'VAR_MODULE_GLOBAL_ERROR'\n\ndeclare const userland500Page: any\ndeclare const incrementalCacheHandler: any\n// OPTIONAL_IMPORT:* as userland500Page\n// OPTIONAL_IMPORT:incrementalCacheHandler\n\n// TODO: re-enable this once we've refactored to use implicit matches\n// const renderToHTML = undefined\n\nimport { renderToHTML } from '../../server/render'\nimport RouteModule from '../../server/route-modules/pages/module'\n\nimport type { RequestData } from '../../server/web/types'\nimport type { BuildManifest } from '../../server/get-page-files'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport type { PAGE_TYPES } from '../../lib/page-types'\n\n// injected by the loader afterwards.\ndeclare const pagesType: PAGE_TYPES\ndeclare const sriEnabled: boolean\ndeclare const dev: boolean\ndeclare const nextConfig: NextConfigComplete\ndeclare const pageRouteModuleOptions: any\ndeclare const errorRouteModuleOptions: any\ndeclare const user500RouteModuleOptions: any\n// INJECT:pagesType\n// INJECT:sriEnabled\n// INJECT:dev\n// INJECT:nextConfig\n// INJECT:pageRouteModuleOptions\n// INJECT:errorRouteModuleOptions\n// INJECT:user500RouteModuleOptions\n\n// Initialize the cache handlers interface.\ninitializeCacheHandlers()\n\nconst pageMod = {\n  ...userlandPage,\n  routeModule: new RouteModule({\n    ...pageRouteModuleOptions,\n    components: {\n      App: appMod.default,\n      Document,\n    },\n    userland: userlandPage,\n  }),\n}\n\nconst errorMod = {\n  ...userlandErrorPage,\n  routeModule: new RouteModule({\n    ...errorRouteModuleOptions,\n    components: {\n      App: appMod.default,\n      Document,\n    },\n    userland: userlandErrorPage,\n  }),\n}\n\n// FIXME: this needs to be made compatible with the template\nconst error500Mod = userland500Page\n  ? {\n      ...userland500Page,\n      routeModule: new RouteModule({\n        ...user500RouteModuleOptions,\n        components: {\n          App: appMod.default,\n          Document,\n        },\n        userland: userland500Page,\n      }),\n    }\n  : null\n\nconst maybeJSONParse = (str?: string) => (str ? JSON.parse(str) : undefined)\n\nconst buildManifest: BuildManifest = self.__BUILD_MANIFEST as any\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST)\nconst dynamicCssManifest = maybeJSONParse(self.__DYNAMIC_CSS_MANIFEST)\nconst subresourceIntegrityManifest = sriEnabled\n  ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST)\n  : undefined\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST)\n\nconst render = getRender({\n  pagesType,\n  dev,\n  page: 'VAR_PAGE',\n  appMod,\n  pageMod,\n  errorMod,\n  error500Mod,\n  Document,\n  buildManifest,\n  renderToHTML,\n  reactLoadableManifest,\n  dynamicCssManifest,\n  subresourceIntegrityManifest,\n  config: nextConfig,\n  buildId: process.env.__NEXT_BUILD_ID!,\n  nextFontManifest,\n  incrementalCacheHandler,\n})\n\nexport const ComponentMod = pageMod\n\nexport default function nHandler(opts: { page: string; request: RequestData }) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    handler: render,\n  })\n}\n"], "names": ["ComponentMod", "nH<PERSON><PERSON>", "initializeCacheHandlers", "pageMod", "userlandPage", "routeModule", "RouteModule", "pageRouteModuleOptions", "components", "App", "appMod", "default", "Document", "userland", "errorMod", "userlandErrorPage", "errorRouteModuleOptions", "error500Mod", "userland500Page", "user500RouteModuleOptions", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "self", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "dynamicCssManifest", "__DYNAMIC_CSS_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "render", "getRender", "pagesType", "dev", "page", "renderToHTML", "config", "nextConfig", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "opts", "adapter", "IncrementalCache", "handler"], "mappings": ";;;;;;;;;;;;;;;IAmHaA,YAAY;eAAZA;;IAEb,OAMC;eANuBC;;;QArHjB;yBACiB;wBACE;kCACO;0BACO;4EAEnB;wEACG;sEACM;iFACK;yBAUN;+DACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAexB,mBAAmB;AACnB,oBAAoB;AACpB,aAAa;AACb,oBAAoB;AACpB,gCAAgC;AAChC,iCAAiC;AACjC,mCAAmC;AAEnC,2CAA2C;AAC3CC,IAAAA,iCAAuB;AAEvB,MAAMC,UAAU;IACd,GAAGC,aAAY;IACfC,aAAa,IAAIC,eAAW,CAAC;QAC3B,GAAGC,sBAAsB;QACzBC,YAAY;YACVC,KAAKC,gBAAOC,OAAO;YACnBC,UAAAA,4BAAQ;QACV;QACAC,UAAUT;IACZ;AACF;AAEA,MAAMU,WAAW;IACf,GAAGC,wBAAiB;IACpBV,aAAa,IAAIC,eAAW,CAAC;QAC3B,GAAGU,uBAAuB;QAC1BR,YAAY;YACVC,KAAKC,gBAAOC,OAAO;YACnBC,UAAAA,4BAAQ;QACV;QACAC,UAAUE;IACZ;AACF;AAEA,4DAA4D;AAC5D,MAAME,cAAcC,kBAChB;IACE,GAAGA,eAAe;IAClBb,aAAa,IAAIC,eAAW,CAAC;QAC3B,GAAGa,yBAAyB;QAC5BX,YAAY;YACVC,KAAKC,gBAAOC,OAAO;YACnBC,UAAAA,4BAAQ;QACV;QACAC,UAAUK;IACZ;AACF,IACA;AAEJ,MAAME,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BC,KAAKC,gBAAgB;AAC1D,MAAMC,wBAAwBR,eAAeM,KAAKG,yBAAyB;AAC3E,MAAMC,qBAAqBV,eAAeM,KAAKK,sBAAsB;AACrE,MAAMC,+BAA+BC,aACjCb,eAAeM,KAAKQ,gCAAgC,IACpDV;AACJ,MAAMW,mBAAmBf,eAAeM,KAAKU,oBAAoB;AAEjE,MAAMC,SAASC,IAAAA,iBAAS,EAAC;IACvBC;IACAC;IACAC,MAAM;IACN/B,QAAAA;IACAP;IACAW;IACAG;IACAL,UAAAA,4BAAQ;IACRa;IACAiB,cAAAA,qBAAY;IACZd;IACAE;IACAE;IACAW,QAAQC;IACRC,SAASC,QAAQC,GAAG,CAACC,eAAe;IACpCb;IACAc;AACF;AAEO,MAAMjD,eAAeG;AAEb,SAASF,SAASiD,IAA4C;IAC3E,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACPE,kBAAAA,kCAAgB;QAChBC,SAAShB;IACX;AACF"}