{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-manifest-plugin.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport path from 'path'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  APP_CLIENT_INTERNALS,\n  BARREL_OPTIMIZATION_PREFIX,\n  CLIENT_REFERENCE_MANIFEST,\n  SYSTEM_ENTRYPOINTS,\n} from '../../../shared/lib/constants'\nimport { relative } from 'path'\nimport { getProxiedPluginState } from '../../build-context'\n\nimport { WEBPACK_LAYERS } from '../../../lib/constants'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\nimport { CLIENT_STATIC_FILES_RUNTIME_MAIN_APP } from '../../../shared/lib/constants'\nimport { getDeploymentIdQueryOrEmptyString } from '../../deployment-id'\nimport {\n  formatBarrelOptimizedResource,\n  getModuleReferencesInOrder,\n} from '../utils'\nimport type { ChunkGroup } from 'webpack'\nimport { encodeURIPath } from '../../../shared/lib/encode-uri-path'\nimport type { ModuleInfo } from './flight-client-entry-plugin'\n\ninterface Options {\n  dev: boolean\n  appDir: string\n  experimentalInlineCss: boolean\n}\n\n/**\n * Webpack module id\n */\n// TODO-APP ensure `null` is included as it is used.\ntype ModuleId = string | number /*| null*/\n\n// double indexed chunkId, filename\nexport type ManifestChunks = Array<string>\n\nconst pluginState = getProxiedPluginState({\n  ssrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n  edgeSsrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n\n  rscModules: {} as { [rscModuleId: string]: ModuleInfo },\n  edgeRscModules: {} as { [rscModuleId: string]: ModuleInfo },\n})\n\nexport interface ManifestNode {\n  [moduleExport: string]: {\n    /**\n     * Webpack module id\n     */\n    id: ModuleId\n    /**\n     * Export name\n     */\n    name: string\n    /**\n     * Chunks for the module. JS and CSS.\n     */\n    chunks: ManifestChunks\n\n    /**\n     * If chunk contains async module\n     */\n    async?: boolean\n  }\n}\n\nexport interface ClientReferenceManifestForRsc {\n  clientModules: ManifestNode\n  rscModuleMapping: {\n    [moduleId: string]: ManifestNode\n  }\n  edgeRscModuleMapping: {\n    [moduleId: string]: ManifestNode\n  }\n}\n\nexport type CssResource = InlinedCssFile | UninlinedCssFile\n\ninterface InlinedCssFile {\n  path: string\n  inlined: true\n  content: string\n}\n\ninterface UninlinedCssFile {\n  path: string\n  inlined: false\n}\n\nexport interface ClientReferenceManifest extends ClientReferenceManifestForRsc {\n  readonly moduleLoading: {\n    prefix: string\n    crossOrigin?: 'use-credentials' | ''\n  }\n  ssrModuleMapping: {\n    [moduleId: string]: ManifestNode\n  }\n  edgeSSRModuleMapping: {\n    [moduleId: string]: ManifestNode\n  }\n  entryCSSFiles: {\n    [entry: string]: CssResource[]\n  }\n  entryJSFiles?: {\n    [entry: string]: string[]\n  }\n}\n\nfunction getAppPathRequiredChunks(\n  chunkGroup: webpack.ChunkGroup,\n  excludedFiles: Set<string>\n) {\n  const deploymentIdChunkQuery = getDeploymentIdQueryOrEmptyString()\n\n  const chunks: Array<string> = []\n  chunkGroup.chunks.forEach((chunk) => {\n    if (SYSTEM_ENTRYPOINTS.has(chunk.name || '')) {\n      return null\n    }\n\n    // Get the actual chunk file names from the chunk file list.\n    // It's possible that the chunk is generated via `import()`, in\n    // that case the chunk file name will be '[name].[contenthash]'\n    // instead of '[name]-[chunkhash]'.\n    if (chunk.id != null) {\n      const chunkId = '' + chunk.id\n      chunk.files.forEach((file) => {\n        // It's possible that a chunk also emits CSS files, that will\n        // be handled separatedly.\n        if (!file.endsWith('.js')) return null\n        if (file.endsWith('.hot-update.js')) return null\n        if (excludedFiles.has(file)) return null\n\n        // We encode the file as a URI because our server (and many other services such as S3)\n        // expect to receive reserved characters such as `[` and `]` as encoded. This was\n        // previously done for dynamic chunks by patching the webpack runtime but we want\n        // these filenames to be managed by React's Flight runtime instead and so we need\n        // to implement any special handling of the file name here.\n        return chunks.push(\n          chunkId,\n          encodeURIPath(file) + deploymentIdChunkQuery\n        )\n      })\n    }\n  })\n  return chunks\n}\n\n// Normalize the entry names to their \"group names\" so a page can easily track\n// all the manifest items it needs from parent groups by looking up the group\n// segments:\n// - app/foo/loading -> app/foo\n// - app/foo/page -> app/foo\n// - app/(group)/@named/foo/page -> app/foo\n// - app/(.)foo/(..)bar/loading -> app/bar\n// - app/[...catchAll]/page -> app\n// - app/foo/@slot/[...catchAll]/page -> app/foo\nfunction entryNameToGroupName(entryName: string) {\n  let groupName = entryName\n    .slice(0, entryName.lastIndexOf('/'))\n    // Remove slots\n    .replace(/\\/@[^/]+/g, '')\n    // Remove the group with lookahead to make sure it's not interception route\n    .replace(/\\/\\([^/]+\\)(?=(\\/|$))/g, '')\n    // Remove catch-all routes since they should be part of the parent group that the catch-all would apply to.\n    // This is necessary to support parallel routes since multiple page components can be rendered on the same page.\n    // In order to do that, we need to ensure that the manifests are merged together by putting them in the same group.\n    .replace(/\\/\\[?\\[\\.\\.\\.[^\\]]*]]?/g, '')\n\n  // Interception routes\n  groupName = groupName\n    .replace(/^.+\\/\\(\\.\\.\\.\\)/g, 'app/')\n    .replace(/\\/\\(\\.\\)/g, '/')\n\n  // Interception routes (recursive)\n  while (/\\/[^/]+\\/\\(\\.\\.\\)/.test(groupName)) {\n    groupName = groupName.replace(/\\/[^/]+\\/\\(\\.\\.\\)/g, '/')\n  }\n\n  return groupName\n}\n\nfunction mergeManifest(\n  manifest: ClientReferenceManifest,\n  manifestToMerge: ClientReferenceManifest\n) {\n  Object.assign(manifest.clientModules, manifestToMerge.clientModules)\n  Object.assign(manifest.ssrModuleMapping, manifestToMerge.ssrModuleMapping)\n  Object.assign(\n    manifest.edgeSSRModuleMapping,\n    manifestToMerge.edgeSSRModuleMapping\n  )\n  Object.assign(manifest.entryCSSFiles, manifestToMerge.entryCSSFiles)\n  Object.assign(manifest.rscModuleMapping, manifestToMerge.rscModuleMapping)\n  Object.assign(\n    manifest.edgeRscModuleMapping,\n    manifestToMerge.edgeRscModuleMapping\n  )\n}\n\nconst PLUGIN_NAME = 'ClientReferenceManifestPlugin'\n\nexport class ClientReferenceManifestPlugin {\n  dev: Options['dev'] = false\n  appDir: Options['appDir']\n  appDirBase: string\n  experimentalInlineCss: Options['experimentalInlineCss']\n\n  constructor(options: Options) {\n    this.dev = options.dev\n    this.appDir = options.appDir\n    this.appDirBase = path.dirname(this.appDir) + path.sep\n    this.experimentalInlineCss = options.experimentalInlineCss\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: PLUGIN_NAME,\n          // Have to be in the optimize stage to run after updating the CSS\n          // asset hash via extract mini css plugin.\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_HASH,\n        },\n        () => this.createAsset(compilation, compiler.context)\n      )\n    })\n  }\n\n  createAsset(compilation: webpack.Compilation, context: string) {\n    const manifestsPerGroup = new Map<string, ClientReferenceManifest[]>()\n    const manifestEntryFiles: string[] = []\n\n    const configuredCrossOriginLoading =\n      compilation.outputOptions.crossOriginLoading\n    const crossOriginMode =\n      typeof configuredCrossOriginLoading === 'string'\n        ? configuredCrossOriginLoading === 'use-credentials'\n          ? configuredCrossOriginLoading\n          : '' // === 'anonymous'\n        : undefined\n\n    if (typeof compilation.outputOptions.publicPath !== 'string') {\n      throw new Error(\n        'Expected webpack publicPath to be a string when using App Router. To customize where static assets are loaded from, use the `assetPrefix` option in next.config.js. If you are customizing your webpack config please make sure you are not modifying or removing the publicPath configuration option'\n      )\n    }\n    const prefix = compilation.outputOptions.publicPath || ''\n\n    // We want to omit any files that will always be loaded on any App Router page\n    // because they will already be loaded by the main entrypoint.\n    const rootMainFiles: Set<string> = new Set()\n    compilation.entrypoints\n      .get(CLIENT_STATIC_FILES_RUNTIME_MAIN_APP)\n      ?.getFiles()\n      .forEach((file) => {\n        if (/(?<!\\.hot-update)\\.(js|css)($|\\?)/.test(file)) {\n          rootMainFiles.add(file.replace(/\\\\/g, '/'))\n        }\n      })\n\n    for (let [entryName, entrypoint] of compilation.entrypoints) {\n      if (\n        entryName === CLIENT_STATIC_FILES_RUNTIME_MAIN_APP ||\n        entryName === APP_CLIENT_INTERNALS\n      ) {\n        entryName = ''\n      } else if (!/^app[\\\\/]/.test(entryName)) {\n        continue\n      }\n\n      const manifest: ClientReferenceManifest = {\n        moduleLoading: {\n          prefix,\n          crossOrigin: crossOriginMode,\n        },\n        ssrModuleMapping: {},\n        edgeSSRModuleMapping: {},\n        clientModules: {},\n        entryCSSFiles: {},\n        rscModuleMapping: {},\n        edgeRscModuleMapping: {},\n      }\n\n      // Absolute path without the extension\n      const chunkEntryName = (this.appDirBase + entryName).replace(\n        /[\\\\/]/g,\n        path.sep\n      )\n\n      manifest.entryCSSFiles[chunkEntryName] = entrypoint\n        .getFiles()\n        .filter((f) => !f.startsWith('static/css/pages/') && f.endsWith('.css'))\n        .map((file) => {\n          const source = compilation.getAsset(file)!.source.source()\n          if (\n            this.experimentalInlineCss &&\n            // Inline CSS currently does not work properly with HMR, so we only\n            // inline CSS in production.\n            !this.dev\n          ) {\n            return {\n              inlined: true,\n              path: file,\n              content: typeof source === 'string' ? source : source.toString(),\n            }\n          }\n          return {\n            inlined: false,\n            path: file,\n          }\n        })\n\n      const requiredChunks = getAppPathRequiredChunks(entrypoint, rootMainFiles)\n      const recordModule = (modId: ModuleId, mod: webpack.NormalModule) => {\n        let resource =\n          mod.type === 'css/mini-extract'\n            ? mod.identifier().slice(mod.identifier().lastIndexOf('!') + 1)\n            : mod.resource\n\n        if (!resource) {\n          return\n        }\n\n        const moduleReferences = manifest.clientModules\n        const moduleIdMapping = manifest.ssrModuleMapping\n        const edgeModuleIdMapping = manifest.edgeSSRModuleMapping\n\n        const rscIdMapping = manifest.rscModuleMapping\n        const edgeRscIdMapping = manifest.edgeRscModuleMapping\n\n        // Note that this isn't that reliable as webpack is still possible to assign\n        // additional queries to make sure there's no conflict even using the `named`\n        // module ID strategy.\n        let ssrNamedModuleId = relative(\n          context,\n          mod.resourceResolveData?.path || resource\n        )\n\n        const rscNamedModuleId = relative(\n          context,\n          mod.resourceResolveData?.path || resource\n        )\n\n        if (!ssrNamedModuleId.startsWith('.'))\n          ssrNamedModuleId = `./${ssrNamedModuleId.replace(/\\\\/g, '/')}`\n\n        // The client compiler will always use the CJS Next.js build, so here we\n        // also add the mapping for the ESM build (Edge runtime) to consume.\n        const esmResource = /[\\\\/]next[\\\\/]dist[\\\\/]/.test(resource)\n          ? resource.replace(\n              /[\\\\/]next[\\\\/]dist[\\\\/]/,\n              '/next/dist/esm/'.replace(/\\//g, path.sep)\n            )\n          : null\n\n        // An extra query param is added to the resource key when it's optimized\n        // through the Barrel Loader. That's because the same file might be created\n        // as multiple modules (depending on what you import from it).\n        // See also: webpack/loaders/next-flight-loader/index.ts.\n        if (mod.matchResource?.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n          ssrNamedModuleId = formatBarrelOptimizedResource(\n            ssrNamedModuleId,\n            mod.matchResource\n          )\n          resource = formatBarrelOptimizedResource(resource, mod.matchResource)\n        }\n\n        function addClientReference() {\n          const isAsync = Boolean(\n            compilation.moduleGraph.isAsync(mod) ||\n              pluginState.ssrModules[ssrNamedModuleId]?.async ||\n              pluginState.edgeSsrModules[ssrNamedModuleId]?.async\n          )\n\n          const exportName = resource\n          manifest.clientModules[exportName] = {\n            id: modId,\n            name: '*',\n            chunks: requiredChunks,\n            async: isAsync,\n          }\n          if (esmResource) {\n            const edgeExportName = esmResource\n            manifest.clientModules[edgeExportName] =\n              manifest.clientModules[exportName]\n          }\n        }\n\n        function addSSRIdMapping() {\n          const exportName = resource\n          const moduleInfo = pluginState.ssrModules[ssrNamedModuleId]\n\n          if (moduleInfo) {\n            moduleIdMapping[modId] = moduleIdMapping[modId] || {}\n            moduleIdMapping[modId]['*'] = {\n              ...manifest.clientModules[exportName],\n              // During SSR, we don't have external chunks to load on the server\n              // side with our architecture of Webpack / Turbopack. We can keep\n              // this field empty to save some bytes.\n              chunks: [],\n              id: moduleInfo.moduleId,\n              async: moduleInfo.async,\n            }\n          }\n\n          const edgeModuleInfo = pluginState.edgeSsrModules[ssrNamedModuleId]\n\n          if (edgeModuleInfo) {\n            edgeModuleIdMapping[modId] = edgeModuleIdMapping[modId] || {}\n            edgeModuleIdMapping[modId]['*'] = {\n              ...manifest.clientModules[exportName],\n              // During SSR, we don't have external chunks to load on the server\n              // side with our architecture of Webpack / Turbopack. We can keep\n              // this field empty to save some bytes.\n              chunks: [],\n              id: edgeModuleInfo.moduleId,\n              async: edgeModuleInfo.async,\n            }\n          }\n        }\n\n        function addRSCIdMapping() {\n          const exportName = resource\n          const moduleInfo = pluginState.rscModules[rscNamedModuleId]\n\n          if (moduleInfo) {\n            rscIdMapping[modId] = rscIdMapping[modId] || {}\n            rscIdMapping[modId]['*'] = {\n              ...manifest.clientModules[exportName],\n              // During SSR, we don't have external chunks to load on the server\n              // side with our architecture of Webpack / Turbopack. We can keep\n              // this field empty to save some bytes.\n              chunks: [],\n              id: moduleInfo.moduleId,\n              async: moduleInfo.async,\n            }\n          }\n\n          const edgeModuleInfo = pluginState.ssrModules[rscNamedModuleId]\n\n          if (edgeModuleInfo) {\n            edgeRscIdMapping[modId] = edgeRscIdMapping[modId] || {}\n            edgeRscIdMapping[modId]['*'] = {\n              ...manifest.clientModules[exportName],\n              // During SSR, we don't have external chunks to load on the server\n              // side with our architecture of Webpack / Turbopack. We can keep\n              // this field empty to save some bytes.\n              chunks: [],\n              id: edgeModuleInfo.moduleId,\n              async: edgeModuleInfo.async,\n            }\n          }\n        }\n\n        addClientReference()\n        addSSRIdMapping()\n        addRSCIdMapping()\n\n        manifest.clientModules = moduleReferences\n        manifest.ssrModuleMapping = moduleIdMapping\n        manifest.edgeSSRModuleMapping = edgeModuleIdMapping\n        manifest.rscModuleMapping = rscIdMapping\n        manifest.edgeRscModuleMapping = edgeRscIdMapping\n      }\n\n      const checkedChunkGroups = new Set()\n      const checkedChunks = new Set()\n\n      function recordChunkGroup(chunkGroup: ChunkGroup) {\n        // Ensure recursion is stopped if we've already checked this chunk group.\n        if (checkedChunkGroups.has(chunkGroup)) return\n        checkedChunkGroups.add(chunkGroup)\n        // Only apply following logic to client module requests from client entry,\n        // or if the module is marked as client module. That's because other\n        // client modules don't need to be in the manifest at all as they're\n        // never be referenced by the server/client boundary.\n        // This saves a lot of bytes in the manifest.\n        chunkGroup.chunks.forEach((chunk: webpack.Chunk) => {\n          // Ensure recursion is stopped if we've already checked this chunk.\n          if (checkedChunks.has(chunk)) return\n          checkedChunks.add(chunk)\n          const entryMods =\n            compilation.chunkGraph.getChunkEntryModulesIterable(chunk)\n          for (const mod of entryMods) {\n            if (mod.layer !== WEBPACK_LAYERS.appPagesBrowser) continue\n\n            const request = (mod as webpack.NormalModule).request\n\n            if (\n              !request ||\n              !request.includes('next-flight-client-entry-loader.js?')\n            ) {\n              continue\n            }\n\n            const connections = getModuleReferencesInOrder(\n              mod,\n              compilation.moduleGraph\n            )\n\n            for (const connection of connections) {\n              const dependency = connection.dependency\n              if (!dependency) continue\n\n              const clientEntryMod = compilation.moduleGraph.getResolvedModule(\n                dependency\n              ) as webpack.NormalModule\n              const modId = compilation.chunkGraph.getModuleId(\n                clientEntryMod\n              ) as string | number | null\n\n              if (modId !== null) {\n                recordModule(modId, clientEntryMod)\n              } else {\n                // If this is a concatenation, register each child to the parent ID.\n                if (\n                  connection.module?.constructor.name === 'ConcatenatedModule'\n                ) {\n                  const concatenatedMod = connection.module\n                  const concatenatedModId =\n                    compilation.chunkGraph.getModuleId(concatenatedMod)\n                  if (concatenatedModId) {\n                    recordModule(concatenatedModId, clientEntryMod)\n                  }\n                }\n              }\n            }\n          }\n        })\n\n        // Walk through all children chunk groups too.\n        for (const child of chunkGroup.childrenIterable) {\n          recordChunkGroup(child)\n        }\n      }\n\n      recordChunkGroup(entrypoint)\n\n      // A page's entry name can have extensions. For example, these are both valid:\n      // - app/foo/page\n      // - app/foo/page.page\n      if (/\\/page(\\.[^/]+)?$/.test(entryName)) {\n        manifestEntryFiles.push(entryName.replace(/\\/page(\\.[^/]+)?$/, '/page'))\n      }\n\n      // We also need to create manifests for route handler entrypoints to\n      // enable `'use cache'`.\n      if (/\\/route$/.test(entryName)) {\n        manifestEntryFiles.push(entryName)\n      }\n\n      const groupName = entryNameToGroupName(entryName)\n      if (!manifestsPerGroup.has(groupName)) {\n        manifestsPerGroup.set(groupName, [])\n      }\n      manifestsPerGroup.get(groupName)!.push(manifest)\n    }\n\n    // Generate per-page manifests.\n    for (const pageName of manifestEntryFiles) {\n      const mergedManifest: ClientReferenceManifest = {\n        moduleLoading: {\n          prefix,\n          crossOrigin: crossOriginMode,\n        },\n        ssrModuleMapping: {},\n        edgeSSRModuleMapping: {},\n        clientModules: {},\n        entryCSSFiles: {},\n        rscModuleMapping: {},\n        edgeRscModuleMapping: {},\n      }\n\n      const segments = [...entryNameToGroupName(pageName).split('/'), 'page']\n      let group = ''\n      for (const segment of segments) {\n        for (const manifest of manifestsPerGroup.get(group) || []) {\n          mergeManifest(mergedManifest, manifest)\n        }\n        group += (group ? '/' : '') + segment\n      }\n\n      const json = JSON.stringify(mergedManifest)\n\n      const pagePath = pageName.replace(/%5F/g, '_')\n      const pageBundlePath = normalizePagePath(pagePath.slice('app'.length))\n      compilation.emitAsset(\n        'server/app' + pageBundlePath + '_' + CLIENT_REFERENCE_MANIFEST + '.js',\n        new sources.RawSource(\n          `globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST[${JSON.stringify(\n            pagePath.slice('app'.length)\n          )}]=${json}`\n        ) as unknown as webpack.sources.RawSource\n      )\n    }\n  }\n}\n"], "names": ["ClientReferenceManifestPlugin", "pluginState", "getProxiedPluginState", "ssrModules", "edgeSsrModules", "rscModules", "edgeRscModules", "getAppPathRequiredChunks", "chunkGroup", "excludedFiles", "deploymentIdChunkQuery", "getDeploymentIdQueryOrEmptyString", "chunks", "for<PERSON>ach", "chunk", "SYSTEM_ENTRYPOINTS", "has", "name", "id", "chunkId", "files", "file", "endsWith", "push", "encodeURIPath", "entryNameToGroupName", "entryName", "groupName", "slice", "lastIndexOf", "replace", "test", "mergeManifest", "manifest", "manifestToMerge", "Object", "assign", "clientModules", "ssrModuleMapping", "edgeSSRModuleMapping", "entryCSSFiles", "rscModuleMapping", "edgeRscModuleMapping", "PLUGIN_NAME", "constructor", "options", "dev", "appDir", "appDirBase", "path", "dirname", "sep", "experimentalInlineCss", "apply", "compiler", "hooks", "compilation", "tap", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "createAsset", "context", "manifestsPerGroup", "Map", "manifestEntryFiles", "configuredCrossOriginLoading", "outputOptions", "crossOriginLoading", "crossOriginMode", "undefined", "publicPath", "Error", "prefix", "rootMainFiles", "Set", "entrypoints", "get", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "getFiles", "add", "entrypoint", "APP_CLIENT_INTERNALS", "moduleLoading", "crossOrigin", "chunkEntryName", "filter", "f", "startsWith", "map", "source", "getAsset", "inlined", "content", "toString", "requiredChunks", "recordModule", "modId", "mod", "resource", "type", "identifier", "moduleReferences", "moduleIdMapping", "edgeModuleIdMapping", "rscIdMapping", "edgeRscIdMapping", "ssrNamedModuleId", "relative", "resourceResolveData", "rscNamedModuleId", "esmResource", "matchResource", "BARREL_OPTIMIZATION_PREFIX", "formatBarrelOptimizedResource", "addClientReference", "isAsync", "Boolean", "moduleGraph", "async", "exportName", "edgeExportName", "addSSRIdMapping", "moduleInfo", "moduleId", "edgeModuleInfo", "addRSCIdMapping", "checkedChunkGroups", "checkedChunks", "recordChunkGroup", "entryMods", "chunkGraph", "getChunkEntryModulesIterable", "layer", "WEBPACK_LAYERS", "appPagesBrowser", "request", "includes", "connections", "getModuleReferencesInOrder", "connection", "dependency", "clientEntryMod", "getResolvedModule", "getModuleId", "module", "concatenatedMod", "concatenatedModId", "child", "childrenIterable", "set", "pageName", "mergedManifest", "segments", "split", "group", "segment", "json", "JSON", "stringify", "pagePath", "pageBundlePath", "normalizePagePath", "length", "emitAsset", "CLIENT_REFERENCE_MANIFEST", "sources", "RawSource"], "mappings": "AAAA;;;;;CAKC;;;;+BA8MYA;;;eAAAA;;;8DA5MI;yBACgB;2BAM1B;8BAE+B;4BAEP;mCACG;8BAEgB;uBAI3C;+BAEuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB9B,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxCC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,YAAY,CAAC;IACbC,gBAAgB,CAAC;AACnB;AAkEA,SAASC,yBACPC,UAA8B,EAC9BC,aAA0B;IAE1B,MAAMC,yBAAyBC,IAAAA,+CAAiC;IAEhE,MAAMC,SAAwB,EAAE;IAChCJ,WAAWI,MAAM,CAACC,OAAO,CAAC,CAACC;QACzB,IAAIC,6BAAkB,CAACC,GAAG,CAACF,MAAMG,IAAI,IAAI,KAAK;YAC5C,OAAO;QACT;QAEA,4DAA4D;QAC5D,+DAA+D;QAC/D,+DAA+D;QAC/D,mCAAmC;QACnC,IAAIH,MAAMI,EAAE,IAAI,MAAM;YACpB,MAAMC,UAAU,KAAKL,MAAMI,EAAE;YAC7BJ,MAAMM,KAAK,CAACP,OAAO,CAAC,CAACQ;gBACnB,6DAA6D;gBAC7D,0BAA0B;gBAC1B,IAAI,CAACA,KAAKC,QAAQ,CAAC,QAAQ,OAAO;gBAClC,IAAID,KAAKC,QAAQ,CAAC,mBAAmB,OAAO;gBAC5C,IAAIb,cAAcO,GAAG,CAACK,OAAO,OAAO;gBAEpC,sFAAsF;gBACtF,iFAAiF;gBACjF,iFAAiF;gBACjF,iFAAiF;gBACjF,2DAA2D;gBAC3D,OAAOT,OAAOW,IAAI,CAChBJ,SACAK,IAAAA,4BAAa,EAACH,QAAQX;YAE1B;QACF;IACF;IACA,OAAOE;AACT;AAEA,8EAA8E;AAC9E,6EAA6E;AAC7E,YAAY;AACZ,+BAA+B;AAC/B,4BAA4B;AAC5B,2CAA2C;AAC3C,0CAA0C;AAC1C,kCAAkC;AAClC,gDAAgD;AAChD,SAASa,qBAAqBC,SAAiB;IAC7C,IAAIC,YAAYD,UACbE,KAAK,CAAC,GAAGF,UAAUG,WAAW,CAAC,KAChC,eAAe;KACdC,OAAO,CAAC,aAAa,GACtB,2EAA2E;KAC1EA,OAAO,CAAC,0BAA0B,GACnC,2GAA2G;IAC3G,gHAAgH;IAChH,mHAAmH;KAClHA,OAAO,CAAC,2BAA2B;IAEtC,sBAAsB;IACtBH,YAAYA,UACTG,OAAO,CAAC,oBAAoB,QAC5BA,OAAO,CAAC,aAAa;IAExB,kCAAkC;IAClC,MAAO,oBAAoBC,IAAI,CAACJ,WAAY;QAC1CA,YAAYA,UAAUG,OAAO,CAAC,sBAAsB;IACtD;IAEA,OAAOH;AACT;AAEA,SAASK,cACPC,QAAiC,EACjCC,eAAwC;IAExCC,OAAOC,MAAM,CAACH,SAASI,aAAa,EAAEH,gBAAgBG,aAAa;IACnEF,OAAOC,MAAM,CAACH,SAASK,gBAAgB,EAAEJ,gBAAgBI,gBAAgB;IACzEH,OAAOC,MAAM,CACXH,SAASM,oBAAoB,EAC7BL,gBAAgBK,oBAAoB;IAEtCJ,OAAOC,MAAM,CAACH,SAASO,aAAa,EAAEN,gBAAgBM,aAAa;IACnEL,OAAOC,MAAM,CAACH,SAASQ,gBAAgB,EAAEP,gBAAgBO,gBAAgB;IACzEN,OAAOC,MAAM,CACXH,SAASS,oBAAoB,EAC7BR,gBAAgBQ,oBAAoB;AAExC;AAEA,MAAMC,cAAc;AAEb,MAAM3C;IAMX4C,YAAYC,OAAgB,CAAE;aAL9BC,MAAsB;QAMpB,IAAI,CAACA,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,UAAU,GAAGC,aAAI,CAACC,OAAO,CAAC,IAAI,CAACH,MAAM,IAAIE,aAAI,CAACE,GAAG;QACtD,IAAI,CAACC,qBAAqB,GAAGP,QAAQO,qBAAqB;IAC5D;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAACd,aAAa,CAACa;YAC3CA,YAAYD,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACExC,MAAM0B;gBACN,iEAAiE;gBACjE,0CAA0C;gBAC1CgB,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;YAC/D,GACA,IAAM,IAAI,CAACC,WAAW,CAACP,aAAaF,SAASU,OAAO;QAExD;IACF;IAEAD,YAAYP,WAAgC,EAAEQ,OAAe,EAAE;YAuB7DR;QAtBA,MAAMS,oBAAoB,IAAIC;QAC9B,MAAMC,qBAA+B,EAAE;QAEvC,MAAMC,+BACJZ,YAAYa,aAAa,CAACC,kBAAkB;QAC9C,MAAMC,kBACJ,OAAOH,iCAAiC,WACpCA,iCAAiC,oBAC/BA,+BACA,GAAG,kBAAkB;WACvBI;QAEN,IAAI,OAAOhB,YAAYa,aAAa,CAACI,UAAU,KAAK,UAAU;YAC5D,MAAM,qBAEL,CAFK,IAAIC,MACR,0SADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMC,SAASnB,YAAYa,aAAa,CAACI,UAAU,IAAI;QAEvD,8EAA8E;QAC9E,8DAA8D;QAC9D,MAAMG,gBAA6B,IAAIC;SACvCrB,+BAAAA,YAAYsB,WAAW,CACpBC,GAAG,CAACC,+CAAoC,sBAD3CxB,6BAEIyB,QAAQ,GACTpE,OAAO,CAAC,CAACQ;YACR,IAAI,oCAAoCU,IAAI,CAACV,OAAO;gBAClDuD,cAAcM,GAAG,CAAC7D,KAAKS,OAAO,CAAC,OAAO;YACxC;QACF;QAEF,KAAK,IAAI,CAACJ,WAAWyD,WAAW,IAAI3B,YAAYsB,WAAW,CAAE;YAC3D,IACEpD,cAAcsD,+CAAoC,IAClDtD,cAAc0D,+BAAoB,EAClC;gBACA1D,YAAY;YACd,OAAO,IAAI,CAAC,YAAYK,IAAI,CAACL,YAAY;gBACvC;YACF;YAEA,MAAMO,WAAoC;gBACxCoD,eAAe;oBACbV;oBACAW,aAAaf;gBACf;gBACAjC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;gBAChBC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;YACzB;YAEA,sCAAsC;YACtC,MAAM6C,iBAAiB,AAAC,CAAA,IAAI,CAACvC,UAAU,GAAGtB,SAAQ,EAAGI,OAAO,CAC1D,UACAmB,aAAI,CAACE,GAAG;YAGVlB,SAASO,aAAa,CAAC+C,eAAe,GAAGJ,WACtCF,QAAQ,GACRO,MAAM,CAAC,CAACC,IAAM,CAACA,EAAEC,UAAU,CAAC,wBAAwBD,EAAEnE,QAAQ,CAAC,SAC/DqE,GAAG,CAAC,CAACtE;gBACJ,MAAMuE,SAASpC,YAAYqC,QAAQ,CAACxE,MAAOuE,MAAM,CAACA,MAAM;gBACxD,IACE,IAAI,CAACxC,qBAAqB,IAC1B,mEAAmE;gBACnE,4BAA4B;gBAC5B,CAAC,IAAI,CAACN,GAAG,EACT;oBACA,OAAO;wBACLgD,SAAS;wBACT7C,MAAM5B;wBACN0E,SAAS,OAAOH,WAAW,WAAWA,SAASA,OAAOI,QAAQ;oBAChE;gBACF;gBACA,OAAO;oBACLF,SAAS;oBACT7C,MAAM5B;gBACR;YACF;YAEF,MAAM4E,iBAAiB1F,yBAAyB4E,YAAYP;YAC5D,MAAMsB,eAAe,CAACC,OAAiBC;oBAsBnCA,0BAKAA,2BAmBEA;gBA7CJ,IAAIC,WACFD,IAAIE,IAAI,KAAK,qBACTF,IAAIG,UAAU,GAAG3E,KAAK,CAACwE,IAAIG,UAAU,GAAG1E,WAAW,CAAC,OAAO,KAC3DuE,IAAIC,QAAQ;gBAElB,IAAI,CAACA,UAAU;oBACb;gBACF;gBAEA,MAAMG,mBAAmBvE,SAASI,aAAa;gBAC/C,MAAMoE,kBAAkBxE,SAASK,gBAAgB;gBACjD,MAAMoE,sBAAsBzE,SAASM,oBAAoB;gBAEzD,MAAMoE,eAAe1E,SAASQ,gBAAgB;gBAC9C,MAAMmE,mBAAmB3E,SAASS,oBAAoB;gBAEtD,4EAA4E;gBAC5E,6EAA6E;gBAC7E,sBAAsB;gBACtB,IAAImE,mBAAmBC,IAAAA,cAAQ,EAC7B9C,SACAoC,EAAAA,2BAAAA,IAAIW,mBAAmB,qBAAvBX,yBAAyBnD,IAAI,KAAIoD;gBAGnC,MAAMW,mBAAmBF,IAAAA,cAAQ,EAC/B9C,SACAoC,EAAAA,4BAAAA,IAAIW,mBAAmB,qBAAvBX,0BAAyBnD,IAAI,KAAIoD;gBAGnC,IAAI,CAACQ,iBAAiBnB,UAAU,CAAC,MAC/BmB,mBAAmB,CAAC,EAAE,EAAEA,iBAAiB/E,OAAO,CAAC,OAAO,MAAM;gBAEhE,wEAAwE;gBACxE,oEAAoE;gBACpE,MAAMmF,cAAc,0BAA0BlF,IAAI,CAACsE,YAC/CA,SAASvE,OAAO,CACd,2BACA,kBAAkBA,OAAO,CAAC,OAAOmB,aAAI,CAACE,GAAG,KAE3C;gBAEJ,wEAAwE;gBACxE,2EAA2E;gBAC3E,8DAA8D;gBAC9D,yDAAyD;gBACzD,KAAIiD,qBAAAA,IAAIc,aAAa,qBAAjBd,mBAAmBV,UAAU,CAACyB,qCAA0B,GAAG;oBAC7DN,mBAAmBO,IAAAA,oCAA6B,EAC9CP,kBACAT,IAAIc,aAAa;oBAEnBb,WAAWe,IAAAA,oCAA6B,EAACf,UAAUD,IAAIc,aAAa;gBACtE;gBAEA,SAASG;wBAGHpH,0CACAA;oBAHJ,MAAMqH,UAAUC,QACd/D,YAAYgE,WAAW,CAACF,OAAO,CAAClB,UAC9BnG,2CAAAA,YAAYE,UAAU,CAAC0G,iBAAiB,qBAAxC5G,yCAA0CwH,KAAK,OAC/CxH,+CAAAA,YAAYG,cAAc,CAACyG,iBAAiB,qBAA5C5G,6CAA8CwH,KAAK;oBAGvD,MAAMC,aAAarB;oBACnBpE,SAASI,aAAa,CAACqF,WAAW,GAAG;wBACnCxG,IAAIiF;wBACJlF,MAAM;wBACNL,QAAQqF;wBACRwB,OAAOH;oBACT;oBACA,IAAIL,aAAa;wBACf,MAAMU,iBAAiBV;wBACvBhF,SAASI,aAAa,CAACsF,eAAe,GACpC1F,SAASI,aAAa,CAACqF,WAAW;oBACtC;gBACF;gBAEA,SAASE;oBACP,MAAMF,aAAarB;oBACnB,MAAMwB,aAAa5H,YAAYE,UAAU,CAAC0G,iBAAiB;oBAE3D,IAAIgB,YAAY;wBACdpB,eAAe,CAACN,MAAM,GAAGM,eAAe,CAACN,MAAM,IAAI,CAAC;wBACpDM,eAAe,CAACN,MAAM,CAAC,IAAI,GAAG;4BAC5B,GAAGlE,SAASI,aAAa,CAACqF,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvC9G,QAAQ,EAAE;4BACVM,IAAI2G,WAAWC,QAAQ;4BACvBL,OAAOI,WAAWJ,KAAK;wBACzB;oBACF;oBAEA,MAAMM,iBAAiB9H,YAAYG,cAAc,CAACyG,iBAAiB;oBAEnE,IAAIkB,gBAAgB;wBAClBrB,mBAAmB,CAACP,MAAM,GAAGO,mBAAmB,CAACP,MAAM,IAAI,CAAC;wBAC5DO,mBAAmB,CAACP,MAAM,CAAC,IAAI,GAAG;4BAChC,GAAGlE,SAASI,aAAa,CAACqF,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvC9G,QAAQ,EAAE;4BACVM,IAAI6G,eAAeD,QAAQ;4BAC3BL,OAAOM,eAAeN,KAAK;wBAC7B;oBACF;gBACF;gBAEA,SAASO;oBACP,MAAMN,aAAarB;oBACnB,MAAMwB,aAAa5H,YAAYI,UAAU,CAAC2G,iBAAiB;oBAE3D,IAAIa,YAAY;wBACdlB,YAAY,CAACR,MAAM,GAAGQ,YAAY,CAACR,MAAM,IAAI,CAAC;wBAC9CQ,YAAY,CAACR,MAAM,CAAC,IAAI,GAAG;4BACzB,GAAGlE,SAASI,aAAa,CAACqF,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvC9G,QAAQ,EAAE;4BACVM,IAAI2G,WAAWC,QAAQ;4BACvBL,OAAOI,WAAWJ,KAAK;wBACzB;oBACF;oBAEA,MAAMM,iBAAiB9H,YAAYE,UAAU,CAAC6G,iBAAiB;oBAE/D,IAAIe,gBAAgB;wBAClBnB,gBAAgB,CAACT,MAAM,GAAGS,gBAAgB,CAACT,MAAM,IAAI,CAAC;wBACtDS,gBAAgB,CAACT,MAAM,CAAC,IAAI,GAAG;4BAC7B,GAAGlE,SAASI,aAAa,CAACqF,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvC9G,QAAQ,EAAE;4BACVM,IAAI6G,eAAeD,QAAQ;4BAC3BL,OAAOM,eAAeN,KAAK;wBAC7B;oBACF;gBACF;gBAEAJ;gBACAO;gBACAI;gBAEA/F,SAASI,aAAa,GAAGmE;gBACzBvE,SAASK,gBAAgB,GAAGmE;gBAC5BxE,SAASM,oBAAoB,GAAGmE;gBAChCzE,SAASQ,gBAAgB,GAAGkE;gBAC5B1E,SAASS,oBAAoB,GAAGkE;YAClC;YAEA,MAAMqB,qBAAqB,IAAIpD;YAC/B,MAAMqD,gBAAgB,IAAIrD;YAE1B,SAASsD,iBAAiB3H,UAAsB;gBAC9C,yEAAyE;gBACzE,IAAIyH,mBAAmBjH,GAAG,CAACR,aAAa;gBACxCyH,mBAAmB/C,GAAG,CAAC1E;gBACvB,0EAA0E;gBAC1E,oEAAoE;gBACpE,oEAAoE;gBACpE,qDAAqD;gBACrD,6CAA6C;gBAC7CA,WAAWI,MAAM,CAACC,OAAO,CAAC,CAACC;oBACzB,mEAAmE;oBACnE,IAAIoH,cAAclH,GAAG,CAACF,QAAQ;oBAC9BoH,cAAchD,GAAG,CAACpE;oBAClB,MAAMsH,YACJ5E,YAAY6E,UAAU,CAACC,4BAA4B,CAACxH;oBACtD,KAAK,MAAMsF,OAAOgC,UAAW;wBAC3B,IAAIhC,IAAImC,KAAK,KAAKC,0BAAc,CAACC,eAAe,EAAE;wBAElD,MAAMC,UAAU,AAACtC,IAA6BsC,OAAO;wBAErD,IACE,CAACA,WACD,CAACA,QAAQC,QAAQ,CAAC,wCAClB;4BACA;wBACF;wBAEA,MAAMC,cAAcC,IAAAA,iCAA0B,EAC5CzC,KACA5C,YAAYgE,WAAW;wBAGzB,KAAK,MAAMsB,cAAcF,YAAa;4BACpC,MAAMG,aAAaD,WAAWC,UAAU;4BACxC,IAAI,CAACA,YAAY;4BAEjB,MAAMC,iBAAiBxF,YAAYgE,WAAW,CAACyB,iBAAiB,CAC9DF;4BAEF,MAAM5C,QAAQ3C,YAAY6E,UAAU,CAACa,WAAW,CAC9CF;4BAGF,IAAI7C,UAAU,MAAM;gCAClBD,aAAaC,OAAO6C;4BACtB,OAAO;oCAGHF;gCAFF,oEAAoE;gCACpE,IACEA,EAAAA,qBAAAA,WAAWK,MAAM,qBAAjBL,mBAAmBlG,WAAW,CAAC3B,IAAI,MAAK,sBACxC;oCACA,MAAMmI,kBAAkBN,WAAWK,MAAM;oCACzC,MAAME,oBACJ7F,YAAY6E,UAAU,CAACa,WAAW,CAACE;oCACrC,IAAIC,mBAAmB;wCACrBnD,aAAamD,mBAAmBL;oCAClC;gCACF;4BACF;wBACF;oBACF;gBACF;gBAEA,8CAA8C;gBAC9C,KAAK,MAAMM,SAAS9I,WAAW+I,gBAAgB,CAAE;oBAC/CpB,iBAAiBmB;gBACnB;YACF;YAEAnB,iBAAiBhD;YAEjB,8EAA8E;YAC9E,iBAAiB;YACjB,sBAAsB;YACtB,IAAI,oBAAoBpD,IAAI,CAACL,YAAY;gBACvCyC,mBAAmB5C,IAAI,CAACG,UAAUI,OAAO,CAAC,qBAAqB;YACjE;YAEA,oEAAoE;YACpE,wBAAwB;YACxB,IAAI,WAAWC,IAAI,CAACL,YAAY;gBAC9ByC,mBAAmB5C,IAAI,CAACG;YAC1B;YAEA,MAAMC,YAAYF,qBAAqBC;YACvC,IAAI,CAACuC,kBAAkBjD,GAAG,CAACW,YAAY;gBACrCsC,kBAAkBuF,GAAG,CAAC7H,WAAW,EAAE;YACrC;YACAsC,kBAAkBc,GAAG,CAACpD,WAAYJ,IAAI,CAACU;QACzC;QAEA,+BAA+B;QAC/B,KAAK,MAAMwH,YAAYtF,mBAAoB;YACzC,MAAMuF,iBAA0C;gBAC9CrE,eAAe;oBACbV;oBACAW,aAAaf;gBACf;gBACAjC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;gBAChBC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;YACzB;YAEA,MAAMiH,WAAW;mBAAIlI,qBAAqBgI,UAAUG,KAAK,CAAC;gBAAM;aAAO;YACvE,IAAIC,QAAQ;YACZ,KAAK,MAAMC,WAAWH,SAAU;gBAC9B,KAAK,MAAM1H,YAAYgC,kBAAkBc,GAAG,CAAC8E,UAAU,EAAE,CAAE;oBACzD7H,cAAc0H,gBAAgBzH;gBAChC;gBACA4H,SAAS,AAACA,CAAAA,QAAQ,MAAM,EAAC,IAAKC;YAChC;YAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACP;YAE5B,MAAMQ,WAAWT,SAAS3H,OAAO,CAAC,QAAQ;YAC1C,MAAMqI,iBAAiBC,IAAAA,oCAAiB,EAACF,SAAStI,KAAK,CAAC,MAAMyI,MAAM;YACpE7G,YAAY8G,SAAS,CACnB,eAAeH,iBAAiB,MAAMI,oCAAyB,GAAG,OAClE,IAAIC,gBAAO,CAACC,SAAS,CACnB,CAAC,oFAAoF,EAAET,KAAKC,SAAS,CACnGC,SAAStI,KAAK,CAAC,MAAMyI,MAAM,GAC3B,EAAE,EAAEN,MAAM;QAGlB;IACF;AACF"}