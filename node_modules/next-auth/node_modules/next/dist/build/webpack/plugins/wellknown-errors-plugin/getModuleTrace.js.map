{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/getModuleTrace.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport loaderUtils from 'next/dist/compiled/loader-utils3'\nimport { relative } from 'path'\n\nfunction formatModule(compiler: webpack.Compiler, module: any) {\n  const relativePath = relative(compiler.context, module.resource).replace(\n    /\\?.+$/,\n    ''\n  )\n  return loaderUtils.isUrlRequest(relativePath)\n    ? loaderUtils.urlToRequest(relativePath)\n    : relativePath\n}\n\nexport function formatModuleTrace(\n  compiler: webpack.Compiler,\n  moduleTrace: any[]\n) {\n  let importTrace: string[] = []\n  let firstExternalModule: any\n  for (let i = moduleTrace.length - 1; i >= 0; i--) {\n    const mod = moduleTrace[i]\n    if (!mod.resource) continue\n\n    if (!mod.resource.includes('node_modules/')) {\n      importTrace.unshift(formatModule(compiler, mod))\n    } else {\n      firstExternalModule = mod\n      break\n    }\n  }\n\n  let invalidImportMessage = ''\n  if (firstExternalModule) {\n    const firstExternalPackageName =\n      firstExternalModule.resourceResolveData?.descriptionFileData?.name\n\n    if (firstExternalPackageName === 'styled-jsx') {\n      invalidImportMessage += `\\n\\nThe error was caused by using 'styled-jsx' in '${importTrace[0]}'. It only works in a Client Component but none of its parents are marked with \"use client\", so they're Server Components by default.`\n    } else {\n      let formattedExternalFile =\n        firstExternalModule.resource.split('node_modules')\n      formattedExternalFile =\n        formattedExternalFile[formattedExternalFile.length - 1]\n\n      invalidImportMessage += `\\n\\nThe error was caused by importing '${formattedExternalFile.slice(\n        1\n      )}' in '${importTrace[0]}'.`\n    }\n  }\n\n  return {\n    lastInternalFileName: importTrace[0],\n    invalidImportMessage,\n    formattedModuleTrace: importTrace.map((mod) => '  ' + mod).join('\\n'),\n  }\n}\n\nexport function getModuleTrace(\n  module: any,\n  compilation: webpack.Compilation,\n  compiler: webpack.Compiler\n) {\n  // Get the module trace:\n  // https://cs.github.com/webpack/webpack/blob/9fcaa243573005d6fdece9a3f8d89a0e8b399613/lib/stats/DefaultStatsFactoryPlugin.js#L414\n  const visitedModules = new Set()\n  const moduleTrace = []\n\n  let current = module\n  let isPagesDir = false\n  while (current) {\n    if (visitedModules.has(current)) break\n    if (/[\\\\/]pages/.test(current.resource.replace(compiler.context, ''))) {\n      isPagesDir = true\n    }\n    visitedModules.add(current)\n    moduleTrace.push(current)\n    const origin = compilation.moduleGraph.getIssuer(current)\n    if (!origin) break\n    current = origin\n  }\n\n  return {\n    moduleTrace,\n    isPagesDir,\n  }\n}\n"], "names": ["formatModuleTrace", "getModuleTrace", "formatModule", "compiler", "module", "relativePath", "relative", "context", "resource", "replace", "loaderUtils", "isUrlRequest", "urlToRequest", "moduleTrace", "importTrace", "firstExternalModule", "i", "length", "mod", "includes", "unshift", "invalidImportMessage", "firstExternalPackageName", "resourceResolveData", "descriptionFileData", "name", "formattedExternalFile", "split", "slice", "lastInternalFileName", "formattedModuleTrace", "map", "join", "compilation", "visitedModules", "Set", "current", "isPagesDir", "has", "test", "add", "push", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;IAcgBA,iBAAiB;eAAjBA;;IA4CAC,cAAc;eAAdA;;;qEAzDQ;sBACC;;;;;;AAEzB,SAASC,aAAaC,QAA0B,EAAEC,OAAW;IAC3D,MAAMC,eAAeC,IAAAA,cAAQ,EAACH,SAASI,OAAO,EAAEH,QAAOI,QAAQ,EAAEC,OAAO,CACtE,SACA;IAEF,OAAOC,qBAAW,CAACC,YAAY,CAACN,gBAC5BK,qBAAW,CAACE,YAAY,CAACP,gBACzBA;AACN;AAEO,SAASL,kBACdG,QAA0B,EAC1BU,WAAkB;IAElB,IAAIC,cAAwB,EAAE;IAC9B,IAAIC;IACJ,IAAK,IAAIC,IAAIH,YAAYI,MAAM,GAAG,GAAGD,KAAK,GAAGA,IAAK;QAChD,MAAME,MAAML,WAAW,CAACG,EAAE;QAC1B,IAAI,CAACE,IAAIV,QAAQ,EAAE;QAEnB,IAAI,CAACU,IAAIV,QAAQ,CAACW,QAAQ,CAAC,kBAAkB;YAC3CL,YAAYM,OAAO,CAAClB,aAAaC,UAAUe;QAC7C,OAAO;YACLH,sBAAsBG;YACtB;QACF;IACF;IAEA,IAAIG,uBAAuB;IAC3B,IAAIN,qBAAqB;YAErBA,8DAAAA;QADF,MAAMO,4BACJP,2CAAAA,oBAAoBQ,mBAAmB,sBAAvCR,+DAAAA,yCAAyCS,mBAAmB,qBAA5DT,6DAA8DU,IAAI;QAEpE,IAAIH,6BAA6B,cAAc;YAC7CD,wBAAwB,CAAC,mDAAmD,EAAEP,WAAW,CAAC,EAAE,CAAC,qIAAqI,CAAC;QACrO,OAAO;YACL,IAAIY,wBACFX,oBAAoBP,QAAQ,CAACmB,KAAK,CAAC;YACrCD,wBACEA,qBAAqB,CAACA,sBAAsBT,MAAM,GAAG,EAAE;YAEzDI,wBAAwB,CAAC,uCAAuC,EAAEK,sBAAsBE,KAAK,CAC3F,GACA,MAAM,EAAEd,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;QAC9B;IACF;IAEA,OAAO;QACLe,sBAAsBf,WAAW,CAAC,EAAE;QACpCO;QACAS,sBAAsBhB,YAAYiB,GAAG,CAAC,CAACb,MAAQ,OAAOA,KAAKc,IAAI,CAAC;IAClE;AACF;AAEO,SAAS/B,eACdG,OAAW,EACX6B,WAAgC,EAChC9B,QAA0B;IAE1B,wBAAwB;IACxB,kIAAkI;IAClI,MAAM+B,iBAAiB,IAAIC;IAC3B,MAAMtB,cAAc,EAAE;IAEtB,IAAIuB,UAAUhC;IACd,IAAIiC,aAAa;IACjB,MAAOD,QAAS;QACd,IAAIF,eAAeI,GAAG,CAACF,UAAU;QACjC,IAAI,aAAaG,IAAI,CAACH,QAAQ5B,QAAQ,CAACC,OAAO,CAACN,SAASI,OAAO,EAAE,MAAM;YACrE8B,aAAa;QACf;QACAH,eAAeM,GAAG,CAACJ;QACnBvB,YAAY4B,IAAI,CAACL;QACjB,MAAMM,SAAST,YAAYU,WAAW,CAACC,SAAS,CAACR;QACjD,IAAI,CAACM,QAAQ;QACbN,UAAUM;IACZ;IAEA,OAAO;QACL7B;QACAwB;IACF;AACF"}