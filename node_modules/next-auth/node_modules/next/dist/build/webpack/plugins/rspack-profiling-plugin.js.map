{"version": 3, "sources": ["../../../../src/build/webpack/plugins/rspack-profiling-plugin.ts"], "sourcesContent": ["// A basic implementation to allow loaders access to loaderContext.currentTraceSpan\n\nimport type { Span } from '../../../trace'\n\nimport { getRspackCore } from '../../../shared/lib/get-rspack'\n\nconst pluginName = 'RspackProfilingPlugin'\nconst moduleSpansByCompilation = new WeakMap()\nexport const compilationSpans: WeakMap<any, Span> = new WeakMap()\n\nexport class RspackProfilingPlugin {\n  runWebpackSpan: Span\n\n  constructor({ runWebpackSpan }: { runWebpackSpan: Span }) {\n    this.runWebpackSpan = runWebpackSpan\n  }\n\n  apply(compiler: any) {\n    compiler.hooks.compilation.tap(\n      { name: pluginName, stage: -Infinity },\n      (compilation: any) => {\n        const rspack = getRspackCore()\n\n        moduleSpansByCompilation.set(compilation, new WeakMap())\n        compilationSpans.set(\n          compilation,\n          this.runWebpackSpan.traceChild('compilation-' + compilation.name)\n        )\n\n        const compilationSpan = this.runWebpackSpan.traceChild(\n          `compilation-${compilation.name}`\n        )\n\n        const moduleHooks = rspack.NormalModule.getCompilationHooks(compilation)\n        moduleHooks.loader.tap(\n          pluginName,\n          (loaderContext: any, module: any) => {\n            const moduleSpan = moduleSpansByCompilation\n              .get(compilation)\n              ?.get(module)\n            loaderContext.currentTraceSpan = moduleSpan\n          }\n        )\n\n        compilation.hooks.buildModule.tap(pluginName, (module: any) => {\n          const span = compilationSpan.traceChild('build-module')\n          span.setAttribute('name', module.userRequest)\n          span.setAttribute('layer', module.layer)\n\n          moduleSpansByCompilation?.get(compilation)?.set(module, span)\n        })\n\n        compilation.hooks.succeedModule.tap(pluginName, (module: any) => {\n          moduleSpansByCompilation?.get(compilation)?.get(module)?.stop()\n        })\n      }\n    )\n  }\n}\n"], "names": ["RspackProfilingPlugin", "compilationSpans", "pluginName", "moduleSpansByCompilation", "WeakMap", "constructor", "runWebpackSpan", "apply", "compiler", "hooks", "compilation", "tap", "name", "stage", "Infinity", "rspack", "getRspackCore", "set", "<PERSON><PERSON><PERSON><PERSON>", "compilationSpan", "moduleHooks", "NormalModule", "getCompilationHooks", "loader", "loaderContext", "module", "moduleSpan", "get", "currentTraceSpan", "buildModule", "span", "setAttribute", "userRequest", "layer", "succeedModule", "stop"], "mappings": "AAAA,mFAAmF;;;;;;;;;;;;;;;;IAUtEA,qBAAqB;eAArBA;;IAFAC,gBAAgB;eAAhBA;;;2BAJiB;AAE9B,MAAMC,aAAa;AACnB,MAAMC,2BAA2B,IAAIC;AAC9B,MAAMH,mBAAuC,IAAIG;AAEjD,MAAMJ;IAGXK,YAAY,EAAEC,cAAc,EAA4B,CAAE;QACxD,IAAI,CAACA,cAAc,GAAGA;IACxB;IAEAC,MAAMC,QAAa,EAAE;QACnBA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5B;YAAEC,MAAMV;YAAYW,OAAO,CAACC;QAAS,GACrC,CAACJ;YACC,MAAMK,SAASC,IAAAA,wBAAa;YAE5Bb,yBAAyBc,GAAG,CAACP,aAAa,IAAIN;YAC9CH,iBAAiBgB,GAAG,CAClBP,aACA,IAAI,CAACJ,cAAc,CAACY,UAAU,CAAC,iBAAiBR,YAAYE,IAAI;YAGlE,MAAMO,kBAAkB,IAAI,CAACb,cAAc,CAACY,UAAU,CACpD,CAAC,YAAY,EAAER,YAAYE,IAAI,EAAE;YAGnC,MAAMQ,cAAcL,OAAOM,YAAY,CAACC,mBAAmB,CAACZ;YAC5DU,YAAYG,MAAM,CAACZ,GAAG,CACpBT,YACA,CAACsB,eAAoBC;oBACAtB;gBAAnB,MAAMuB,cAAavB,gCAAAA,yBAChBwB,GAAG,CAACjB,iCADYP,8BAEfwB,GAAG,CAACF;gBACRD,cAAcI,gBAAgB,GAAGF;YACnC;YAGFhB,YAAYD,KAAK,CAACoB,WAAW,CAAClB,GAAG,CAACT,YAAY,CAACuB;oBAK7CtB;gBAJA,MAAM2B,OAAOX,gBAAgBD,UAAU,CAAC;gBACxCY,KAAKC,YAAY,CAAC,QAAQN,QAAOO,WAAW;gBAC5CF,KAAKC,YAAY,CAAC,SAASN,QAAOQ,KAAK;gBAEvC9B,6CAAAA,gCAAAA,yBAA0BwB,GAAG,CAACjB,iCAA9BP,8BAA4Cc,GAAG,CAACQ,SAAQK;YAC1D;YAEApB,YAAYD,KAAK,CAACyB,aAAa,CAACvB,GAAG,CAACT,YAAY,CAACuB;oBAC/CtB,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BwB,GAAG,CAACjB,kCAA9BP,oCAAAA,8BAA4CwB,GAAG,CAACF,6BAAhDtB,kCAAyDgC,IAAI;YAC/D;QACF;IAEJ;AACF"}