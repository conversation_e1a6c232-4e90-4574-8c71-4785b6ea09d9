{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNextFontError.ts"], "sourcesContent": ["import { SimpleWebpackError } from './simpleWebpackError'\n\nexport function getNextFontError(\n  err: Error,\n  module: any\n): SimpleWebpackError | false {\n  try {\n    const resourceResolveData = module.resourceResolveData\n    if (\n      !module.loaders.find((loader: any) =>\n        /next-font-loader[/\\\\]index.js/.test(loader.loader)\n      )\n    ) {\n      return false\n    }\n\n    // Parse the query and get the path of the file where the font function was called.\n    // provided by next-swc next-transform-font\n    const file = JSON.parse(resourceResolveData.query.slice(1)).path\n\n    if (err.name === 'NextFontError') {\n      // Known error thrown by @next/font, display the error message\n      return new SimpleWebpackError(\n        file,\n        `\\`next/font\\` error:\\n${err.message}`\n      )\n    } else {\n      // Unknown error thrown by @next/font\n      return new SimpleWebpackError(\n        file,\n        `An error occurred in \\`next/font\\`.\\n\\n${err.stack}`\n      )\n    }\n  } catch {\n    return false\n  }\n}\n"], "names": ["getNextFontError", "err", "module", "resourceResolveData", "loaders", "find", "loader", "test", "file", "JSON", "parse", "query", "slice", "path", "name", "SimpleWebpackError", "message", "stack"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;oCAFmB;AAE5B,SAASA,iBACdC,GAAU,EACVC,MAAW;IAEX,IAAI;QACF,MAAMC,sBAAsBD,OAAOC,mBAAmB;QACtD,IACE,CAACD,OAAOE,OAAO,CAACC,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;YACA,OAAO;QACT;QAEA,mFAAmF;QACnF,2CAA2C;QAC3C,MAAME,OAAOC,KAAKC,KAAK,CAACP,oBAAoBQ,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;QAEhE,IAAIZ,IAAIa,IAAI,KAAK,iBAAiB;YAChC,8DAA8D;YAC9D,OAAO,IAAIC,sCAAkB,CAC3BP,MACA,CAAC,sBAAsB,EAAEP,IAAIe,OAAO,EAAE;QAE1C,OAAO;YACL,qCAAqC;YACrC,OAAO,IAAID,sCAAkB,CAC3BP,MACA,CAAC,uCAAuC,EAAEP,IAAIgB,KAAK,EAAE;QAEzD;IACF,EAAE,OAAM;QACN,OAAO;IACT;AACF"}