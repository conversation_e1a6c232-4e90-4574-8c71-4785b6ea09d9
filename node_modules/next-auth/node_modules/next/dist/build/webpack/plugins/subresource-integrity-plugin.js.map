{"version": 3, "sources": ["../../../../src/build/webpack/plugins/subresource-integrity-plugin.ts"], "sourcesContent": ["import { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport crypto from 'crypto'\nimport { SUBRESOURCE_INTEGRITY_MANIFEST } from '../../../shared/lib/constants'\n\nconst PLUGIN_NAME = 'SubresourceIntegrityPlugin'\n\nexport type SubresourceIntegrityAlgorithm = 'sha256' | 'sha384' | 'sha512'\n\nexport class SubresourceIntegrityPlugin {\n  constructor(private readonly algorithm: SubresourceIntegrityAlgorithm) {}\n\n  public apply(compiler: webpack.Compiler) {\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.afterProcessAssets.tap(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => {\n          // Collect all the assets.\n          let files = new Set<string>()\n          for (const asset of compilation.getAssets()) {\n            files.add(asset.name)\n          }\n\n          // For each file, deduped, calculate the file hash.\n          const hashes: Record<string, string> = {}\n          for (const file of files.values()) {\n            // Get the buffer for the asset.\n            const asset = compilation.getAsset(file)\n            if (!asset) {\n              throw new Error(`could not get asset: ${file}`)\n            }\n\n            // Get the buffer for the asset.\n            const buffer = asset.source.buffer()\n\n            // Create the hash for the content.\n            const hash = crypto\n              .createHash(this.algorithm)\n              .update(buffer)\n              .digest()\n              .toString('base64')\n\n            hashes[file] = `${this.algorithm}-${hash}`\n          }\n\n          const json = JSON.stringify(hashes, null, 2)\n          const file = 'server/' + SUBRESOURCE_INTEGRITY_MANIFEST\n          compilation.emitAsset(\n            file + '.js',\n            new sources.RawSource(\n              `self.__SUBRESOURCE_INTEGRITY_MANIFEST=${JSON.stringify(json)}`\n              // Work around webpack 4 type of RawSource being used\n              // TODO: use webpack 5 type by default\n            ) as unknown as webpack.sources.RawSource\n          )\n          compilation.emitAsset(\n            file + '.json',\n            new sources.RawSource(\n              json\n              // Work around webpack 4 type of RawSource being used\n              // TODO: use webpack 5 type by default\n            ) as unknown as webpack.sources.RawSource\n          )\n        }\n      )\n    })\n  }\n}\n"], "names": ["SubresourceIntegrityPlugin", "PLUGIN_NAME", "constructor", "algorithm", "apply", "compiler", "hooks", "make", "tap", "compilation", "afterProcessAssets", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "files", "Set", "asset", "getAssets", "add", "hashes", "file", "values", "getAsset", "Error", "buffer", "source", "hash", "crypto", "createHash", "update", "digest", "toString", "json", "JSON", "stringify", "SUBRESOURCE_INTEGRITY_MANIFEST", "emitAsset", "sources", "RawSource"], "mappings": ";;;;+BAQaA;;;eAAAA;;;yBARoB;+DACd;2BAC4B;;;;;;AAE/C,MAAMC,cAAc;AAIb,MAAMD;IACXE,YAAY,AAAiBC,SAAwC,CAAE;aAA1CA,YAAAA;IAA2C;IAEjEC,MAAMC,QAA0B,EAAE;QACvCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACP,aAAa,CAACQ;YACpCA,YAAYH,KAAK,CAACI,kBAAkB,CAACF,GAAG,CACtC;gBACEG,MAAMV;gBACNW,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA;gBACE,0BAA0B;gBAC1B,IAAIC,QAAQ,IAAIC;gBAChB,KAAK,MAAMC,SAAST,YAAYU,SAAS,GAAI;oBAC3CH,MAAMI,GAAG,CAACF,MAAMP,IAAI;gBACtB;gBAEA,mDAAmD;gBACnD,MAAMU,SAAiC,CAAC;gBACxC,KAAK,MAAMC,QAAQN,MAAMO,MAAM,GAAI;oBACjC,gCAAgC;oBAChC,MAAML,QAAQT,YAAYe,QAAQ,CAACF;oBACnC,IAAI,CAACJ,OAAO;wBACV,MAAM,qBAAyC,CAAzC,IAAIO,MAAM,CAAC,qBAAqB,EAAEH,MAAM,GAAxC,qBAAA;mCAAA;wCAAA;0CAAA;wBAAwC;oBAChD;oBAEA,gCAAgC;oBAChC,MAAMI,SAASR,MAAMS,MAAM,CAACD,MAAM;oBAElC,mCAAmC;oBACnC,MAAME,OAAOC,eAAM,CAChBC,UAAU,CAAC,IAAI,CAAC3B,SAAS,EACzB4B,MAAM,CAACL,QACPM,MAAM,GACNC,QAAQ,CAAC;oBAEZZ,MAAM,CAACC,KAAK,GAAG,GAAG,IAAI,CAACnB,SAAS,CAAC,CAAC,EAAEyB,MAAM;gBAC5C;gBAEA,MAAMM,OAAOC,KAAKC,SAAS,CAACf,QAAQ,MAAM;gBAC1C,MAAMC,OAAO,YAAYe,yCAA8B;gBACvD5B,YAAY6B,SAAS,CACnBhB,OAAO,OACP,IAAIiB,gBAAO,CAACC,SAAS,CACnB,CAAC,sCAAsC,EAAEL,KAAKC,SAAS,CAACF,OAAO;gBAKnEzB,YAAY6B,SAAS,CACnBhB,OAAO,SACP,IAAIiB,gBAAO,CAACC,SAAS,CACnBN;YAKN;QAEJ;IACF;AACF"}