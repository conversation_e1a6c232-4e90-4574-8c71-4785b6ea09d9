{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/global.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { ConfigurationContext } from '../../../utils'\n\nimport { getClientStyleLoader } from './client'\nimport { cssFileResolve } from './file-resolve'\n\nexport function getGlobalCssLoader(\n  ctx: ConfigurationContext,\n  postcss: any,\n  preProcessors: readonly webpack.RuleSetUseItem[] = []\n): webpack.RuleSetUseItem[] {\n  const loaders: webpack.RuleSetUseItem[] = []\n\n  if (ctx.isClient) {\n    // Add appropriate development more or production mode style\n    // loader\n    loaders.push(\n      getClientStyleLoader({\n        hasAppDir: ctx.hasAppDir,\n        isAppDir: ctx.isAppDir,\n        isDevelopment: ctx.isDevelopment,\n        assetPrefix: ctx.assetPrefix,\n      })\n    )\n  }\n\n  if (ctx.experimental.useLightningcss) {\n    loaders.push({\n      loader: require.resolve('../../../../loaders/lightningcss-loader/src'),\n      options: {\n        importLoaders: 1 + preProcessors.length,\n        url: (url: string, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        import: (url: string, _: any, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        modules: false,\n        targets: ctx.supportedBrowsers,\n        postcss,\n      },\n    })\n  } else {\n    // Resolve CSS `@import`s and `url()`s\n    loaders.push({\n      loader: require.resolve('../../../../loaders/css-loader/src'),\n      options: {\n        postcss,\n        importLoaders: 1 + preProcessors.length,\n        // Next.js controls CSS Modules eligibility:\n        modules: false,\n        url: (url: string, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        import: (url: string, _: any, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n      },\n    })\n\n    // Compile CSS\n    loaders.push({\n      loader: require.resolve('../../../../loaders/postcss-loader/src'),\n      options: {\n        postcss,\n      },\n    })\n  }\n\n  loaders.push(\n    // Webpack loaders run like a stack, so we need to reverse the natural\n    // order of preprocessors.\n    ...preProcessors.slice().reverse()\n  )\n\n  return loaders\n}\n"], "names": ["getGlobalCssLoader", "ctx", "postcss", "preProcessors", "loaders", "isClient", "push", "getClientStyleLoader", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "experimental", "useLightningcss", "loader", "require", "resolve", "options", "importLoaders", "length", "url", "resourcePath", "cssFileResolve", "urlImports", "import", "_", "modules", "targets", "supportedBrowsers", "slice", "reverse"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;wBAHqB;6BACN;AAExB,SAASA,mBACdC,GAAyB,EACzBC,OAAY,EACZC,gBAAmD,EAAE;IAErD,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVC,IAAAA,4BAAoB,EAAC;YACnBC,WAAWP,IAAIO,SAAS;YACxBC,UAAUR,IAAIQ,QAAQ;YACtBC,eAAeT,IAAIS,aAAa;YAChCC,aAAaV,IAAIU,WAAW;QAC9B;IAEJ;IAEA,IAAIV,IAAIW,YAAY,CAACC,eAAe,EAAE;QACpCT,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPC,eAAe,IAAIf,cAAcgB,MAAM;gBACvCC,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DG,SAAS;gBACTC,SAAS1B,IAAI2B,iBAAiB;gBAC9B1B;YACF;QACF;IACF,OAAO;QACL,sCAAsC;QACtCE,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPf;gBACAgB,eAAe,IAAIf,cAAcgB,MAAM;gBACvC,4CAA4C;gBAC5CO,SAAS;gBACTN,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;YACjE;QACF;QAEA,cAAc;QACdnB,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPf;YACF;QACF;IACF;IAEAE,QAAQE,IAAI,CACV,sEAAsE;IACtE,0BAA0B;OACvBH,cAAc0B,KAAK,GAAGC,OAAO;IAGlC,OAAO1B;AACT"}