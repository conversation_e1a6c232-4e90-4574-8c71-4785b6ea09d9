{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/action-validate.ts"], "sourcesContent": ["// This function ensures that all the exported values are valid server actions,\n// during the runtime. By definition all actions are required to be async\n// functions, but here we can only check that they are functions.\nexport function ensureServerEntryExports(actions: any[]) {\n  for (let i = 0; i < actions.length; i++) {\n    const action = actions[i]\n    if (typeof action !== 'function') {\n      throw new Error(\n        `A \"use server\" file can only export async functions, found ${typeof action}.\\nRead more: https://nextjs.org/docs/messages/invalid-use-server-value`\n      )\n    }\n  }\n}\n"], "names": ["ensureServerEntryExports", "actions", "i", "length", "action", "Error"], "mappings": "AAAA,+EAA+E;AAC/E,yEAAyE;AACzE,iEAAiE;;;;;+BACjDA;;;eAAAA;;;AAAT,SAASA,yBAAyBC,OAAc;IACrD,IAAK,IAAIC,IAAI,GAAGA,IAAID,QAAQE,MAAM,EAAED,IAAK;QACvC,MAAME,SAASH,OAAO,CAACC,EAAE;QACzB,IAAI,OAAOE,WAAW,YAAY;YAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,2DAA2D,EAAE,OAAOD,OAAO,uEAAuE,CAAC,GADhJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF"}