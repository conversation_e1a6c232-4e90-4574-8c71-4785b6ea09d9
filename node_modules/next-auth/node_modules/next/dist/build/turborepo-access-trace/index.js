"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    TurborepoAccessTraceResult: null,
    turborepoTraceAccess: null,
    writeTurborepoAccessTraceResult: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    TurborepoAccessTraceResult: function() {
        return _result.TurborepoAccessTraceResult;
    },
    turborepoTraceAccess: function() {
        return _helpers.turborepoTraceAccess;
    },
    writeTurborepoAccessTraceResult: function() {
        return _helpers.writeTurborepoAccessTraceResult;
    }
});
const _helpers = require("./helpers");
const _result = require("./result");

//# sourceMappingURL=index.js.map