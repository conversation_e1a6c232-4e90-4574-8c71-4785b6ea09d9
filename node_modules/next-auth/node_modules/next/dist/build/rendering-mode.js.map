{"version": 3, "sources": ["../../src/build/rendering-mode.ts"], "sourcesContent": ["/**\n * The rendering mode for a route.\n */\nexport const enum RenderingMode {\n  /**\n   * `STATIC` rendering mode will output a fully static HTML page or error if\n   * anything dynamic is used.\n   */\n  STATIC = 'STATIC',\n\n  /**\n   * `PARTIALLY_STATIC` rendering mode will output a fully static HTML page if\n   * the route is fully static, but will output a partially static HTML page if\n   * the route uses uses any dynamic API's.\n   */\n  PARTIALLY_STATIC = 'PARTIALLY_STATIC',\n}\n"], "names": ["RenderingMode"], "mappings": "AAAA;;CAEC;;;;+BACiBA;;;eAAAA;;;AAAX,IAAA,AAAWA,uCAAAA;IAChB;;;GAGC;IAGD;;;;GAIC;WAXeA"}