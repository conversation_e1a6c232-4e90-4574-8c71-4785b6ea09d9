{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "sourcesContent": ["import type { COMPILER_INDEXES } from '../../shared/lib/constants'\nimport * as Log from '../output/log'\nimport { NextBuildContext } from '../build-context'\nimport type { BuildTraceContext } from '../webpack/plugins/next-trace-entrypoints-plugin'\nimport { Worker } from '../../lib/worker'\nimport origDebug from 'next/dist/compiled/debug'\nimport path from 'path'\nimport { exportTraceState, recordTraceEvents } from '../../trace'\nimport {\n  formatNodeOptions,\n  getParsedNodeOptionsWithoutInspect,\n} from '../../server/lib/utils'\nimport { mergeUseCacheTrackers } from '../webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport { durationToString } from '../duration-to-string'\n\nconst debug = origDebug('next:build:webpack-build')\n\nconst ORDERED_COMPILER_NAMES = [\n  'server',\n  'edge-server',\n  'client',\n] as (keyof typeof COMPILER_INDEXES)[]\n\nlet pluginState: Record<any, any> = {}\n\nfunction deepMerge(target: any, source: any) {\n  const result = { ...target, ...source }\n  for (const key of Object.keys(result)) {\n    result[key] = Array.isArray(target[key])\n      ? (target[key] = [...target[key], ...(source[key] || [])])\n      : typeof target[key] == 'object' && typeof source[key] == 'object'\n        ? deepMerge(target[key], source[key])\n        : result[key]\n  }\n  return result\n}\n\nasync function webpackBuildWithWorker(\n  compilerNamesArg: typeof ORDERED_COMPILER_NAMES | null\n) {\n  const compilerNames = compilerNamesArg || ORDERED_COMPILER_NAMES\n  const { nextBuildSpan, ...prunedBuildContext } = NextBuildContext\n\n  prunedBuildContext.pluginState = pluginState\n\n  const combinedResult = {\n    duration: 0,\n    buildTraceContext: {} as BuildTraceContext,\n  }\n\n  const nodeOptions = getParsedNodeOptionsWithoutInspect()\n\n  for (const compilerName of compilerNames) {\n    const worker = new Worker(path.join(__dirname, 'impl.js'), {\n      exposedMethods: ['workerMain'],\n      numWorkers: 1,\n      maxRetries: 0,\n      forkOptions: {\n        env: {\n          ...process.env,\n          NEXT_PRIVATE_BUILD_WORKER: '1',\n          NODE_OPTIONS: formatNodeOptions(nodeOptions),\n        },\n      },\n    }) as Worker & typeof import('./impl')\n\n    const curResult = await worker.workerMain({\n      buildContext: prunedBuildContext,\n      compilerName,\n      traceState: {\n        ...exportTraceState(),\n        defaultParentSpanId: nextBuildSpan?.getId(),\n        shouldSaveTraceEvents: true,\n      },\n    })\n    if (nextBuildSpan && curResult.debugTraceEvents) {\n      recordTraceEvents(curResult.debugTraceEvents)\n    }\n    // destroy worker so it's not sticking around using memory\n    await worker.end()\n\n    // Update plugin state\n    pluginState = deepMerge(pluginState, curResult.pluginState)\n    prunedBuildContext.pluginState = pluginState\n\n    if (curResult.telemetryState) {\n      NextBuildContext.telemetryState = {\n        ...curResult.telemetryState,\n        useCacheTracker: mergeUseCacheTrackers(\n          NextBuildContext.telemetryState?.useCacheTracker,\n          curResult.telemetryState.useCacheTracker\n        ),\n      }\n    }\n\n    combinedResult.duration += curResult.duration\n\n    if (curResult.buildTraceContext?.entriesTrace) {\n      const { entryNameMap } = curResult.buildTraceContext.entriesTrace!\n\n      if (entryNameMap) {\n        combinedResult.buildTraceContext.entriesTrace =\n          curResult.buildTraceContext.entriesTrace\n        combinedResult.buildTraceContext.entriesTrace!.entryNameMap =\n          entryNameMap\n      }\n\n      if (curResult.buildTraceContext?.chunksTrace) {\n        const { entryNameFilesMap } = curResult.buildTraceContext.chunksTrace!\n\n        if (entryNameFilesMap) {\n          combinedResult.buildTraceContext.chunksTrace =\n            curResult.buildTraceContext.chunksTrace!\n\n          combinedResult.buildTraceContext.chunksTrace!.entryNameFilesMap =\n            entryNameFilesMap\n        }\n      }\n    }\n  }\n\n  if (compilerNames.length === 3) {\n    const durationString = durationToString(combinedResult.duration)\n    Log.event(`Compiled successfully in ${durationString}`)\n  }\n\n  return combinedResult\n}\n\nexport async function webpackBuild(\n  withWorker: boolean,\n  compilerNames: typeof ORDERED_COMPILER_NAMES | null\n): ReturnType<typeof webpackBuildWithWorker> {\n  if (withWorker) {\n    debug('using separate compiler workers')\n    return await webpackBuildWithWorker(compilerNames)\n  } else {\n    debug('building all compilers in same process')\n    const webpackBuildImpl = require('./impl').webpackBuildImpl\n    const curResult = await webpackBuildImpl(null, null)\n\n    // Mirror what happens in webpackBuildWithWorker\n    if (curResult.telemetryState) {\n      NextBuildContext.telemetryState = {\n        ...curResult.telemetryState,\n        useCacheTracker: mergeUseCacheTrackers(\n          NextBuildContext.telemetryState?.useCacheTracker,\n          curResult.telemetryState.useCacheTracker\n        ),\n      }\n    }\n\n    return curResult\n  }\n}\n"], "names": ["webpackBuild", "debug", "origDebug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNamesArg", "compilerNames", "nextBuildSpan", "prunedBuildContext", "NextBuildContext", "combinedResult", "duration", "buildTraceContext", "nodeOptions", "getParsedNodeOptionsWithoutInspect", "compilerName", "curR<PERSON>ult", "worker", "Worker", "path", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "NODE_OPTIONS", "formatNodeOptions", "worker<PERSON>ain", "buildContext", "traceState", "exportTraceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "recordTraceEvents", "end", "telemetryState", "useCacheTracker", "mergeUseCacheTrackers", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "durationString", "durationToString", "Log", "event", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": ";;;;+BAiIsBA;;;eAAAA;;;6DAhID;8BACY;wBAEV;8DACD;6DACL;uBACmC;uBAI7C;sCAC+B;kCACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAExB,MAAMC,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACtDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACnB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAAsD;IAEtD,MAAMC,gBAAgBD,oBAAoBZ;IAC1C,MAAM,EAAEc,aAAa,EAAE,GAAGC,oBAAoB,GAAGC,8BAAgB;IAEjED,mBAAmBd,WAAW,GAAGA;IAEjC,MAAMgB,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,MAAMC,cAAcC,IAAAA,yCAAkC;IAEtD,KAAK,MAAMC,gBAAgBT,cAAe;YA6CpCU;QA5CJ,MAAMC,SAAS,IAAIC,cAAM,CAACC,aAAI,CAACC,IAAI,CAACC,WAAW,YAAY;YACzDC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;oBAC3BC,cAAcC,IAAAA,wBAAiB,EAACjB;gBAClC;YACF;QACF;QAEA,MAAMG,YAAY,MAAMC,OAAOc,UAAU,CAAC;YACxCC,cAAcxB;YACdO;YACAkB,YAAY;gBACV,GAAGC,IAAAA,uBAAgB,GAAE;gBACrBC,mBAAmB,EAAE5B,iCAAAA,cAAe6B,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAI9B,iBAAiBS,UAAUsB,gBAAgB,EAAE;YAC/CC,IAAAA,wBAAiB,EAACvB,UAAUsB,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMrB,OAAOuB,GAAG;QAEhB,sBAAsB;QACtB9C,cAAcC,UAAUD,aAAasB,UAAUtB,WAAW;QAC1Dc,mBAAmBd,WAAW,GAAGA;QAEjC,IAAIsB,UAAUyB,cAAc,EAAE;gBAIxBhC;YAHJA,8BAAgB,CAACgC,cAAc,GAAG;gBAChC,GAAGzB,UAAUyB,cAAc;gBAC3BC,iBAAiBC,IAAAA,2CAAqB,GACpClC,mCAAAA,8BAAgB,CAACgC,cAAc,qBAA/BhC,iCAAiCiC,eAAe,EAChD1B,UAAUyB,cAAc,CAACC,eAAe;YAE5C;QACF;QAEAhC,eAAeC,QAAQ,IAAIK,UAAUL,QAAQ;QAE7C,KAAIK,+BAAAA,UAAUJ,iBAAiB,qBAA3BI,6BAA6B4B,YAAY,EAAE;gBAUzC5B;YATJ,MAAM,EAAE6B,YAAY,EAAE,GAAG7B,UAAUJ,iBAAiB,CAACgC,YAAY;YAEjE,IAAIC,cAAc;gBAChBnC,eAAeE,iBAAiB,CAACgC,YAAY,GAC3C5B,UAAUJ,iBAAiB,CAACgC,YAAY;gBAC1ClC,eAAeE,iBAAiB,CAACgC,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAI7B,gCAAAA,UAAUJ,iBAAiB,qBAA3BI,8BAA6B8B,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAG/B,UAAUJ,iBAAiB,CAACkC,WAAW;gBAErE,IAAIC,mBAAmB;oBACrBrC,eAAeE,iBAAiB,CAACkC,WAAW,GAC1C9B,UAAUJ,iBAAiB,CAACkC,WAAW;oBAEzCpC,eAAeE,iBAAiB,CAACkC,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAIzC,cAAc0C,MAAM,KAAK,GAAG;QAC9B,MAAMC,iBAAiBC,IAAAA,kCAAgB,EAACxC,eAAeC,QAAQ;QAC/DwC,KAAIC,KAAK,CAAC,CAAC,yBAAyB,EAAEH,gBAAgB;IACxD;IAEA,OAAOvC;AACT;AAEO,eAAepB,aACpB+D,UAAmB,EACnB/C,aAAmD;IAEnD,IAAI+C,YAAY;QACd9D,MAAM;QACN,OAAO,MAAMa,uBAAuBE;IACtC,OAAO;QACLf,MAAM;QACN,MAAM+D,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,MAAMtC,YAAY,MAAMsC,iBAAiB,MAAM;QAE/C,gDAAgD;QAChD,IAAItC,UAAUyB,cAAc,EAAE;gBAIxBhC;YAHJA,8BAAgB,CAACgC,cAAc,GAAG;gBAChC,GAAGzB,UAAUyB,cAAc;gBAC3BC,iBAAiBC,IAAAA,2CAAqB,GACpClC,mCAAAA,8BAAgB,CAACgC,cAAc,qBAA/BhC,iCAAiCiC,eAAe,EAChD1B,UAAUyB,cAAc,CAACC,eAAe;YAE5C;QACF;QAEA,OAAO1B;IACT;AACF"}