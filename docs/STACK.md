- Frontend Routing: Next.js
- Database: Neon
- Database Service: Neon + Drizzle
- Authentication: Clerk
- Package Manager & JavaScript Runtime: Bun
- Full-stack Framework (Frontend & API Routes): Next.js
- Monorepo Tool: Turborepo
- Agent Framework: AISDK from Vercel
- Openrouter: As a Model Provider
- Prefered Models: Gemini 2.5 Flash for simple tasks, o3 for difficult tasks
- MCPs connection via AISDK
- Uploadthing for File Storage like Images and Videos