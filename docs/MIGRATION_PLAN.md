# AI Chatbot Stack Migration Plan

## Overview

This document outlines the comprehensive migration plan for moving the Next.js AI Chatbot from its current stack to the BonKai stack requirements. This is a complex migration involving multiple core system changes.

## Current vs Target Stack

### Current Stack
- **Framework**: Next.js 15 with App Router ✅
- **AI Provider**: xAI Grok models ❌
- **Authentication**: NextAuth.js v5 ❌
- **Database**: Vercel Postgres + Drizzle ❌
- **File Storage**: Vercel Blob ❌
- **Package Manager**: pnpm ❌
- **Monorepo**: None ❌
- **AI SDK**: Vercel AI SDK ✅
- **MCP Integration**: None ❌

### Target Stack (BonKai Requirements)
- **Framework**: Next.js ✅
- **AI Provider**: OpenRouter (Gemini 2.5 Flash, o3) 🔄
- **Authentication**: Clerk 🔄
- **Database**: Convex (Real-time, TypeScript-first) 🔄
- **File Storage**: UploadThing 🔄
- **Package Manager**: Bun 🔄
- **Monorepo**: Turborepo 🔄
- **AI SDK**: Vercel AI SDK ✅
- **MCP Integration**: Via AI SDK 🔄

## Migration Phases

### Phase 1: Foundation Setup
**Duration**: 3-4 days
**Priority**: Critical

#### 1.1 Turborepo Monorepo Setup
```
BonKai/
├── apps/
│   └── chatbot/          # Main Next.js app
├── packages/
│   ├── ui/               # Shared UI components
│   ├── database/         # Database schema & queries
│   ├── auth/             # Auth utilities
│   ├── ai/               # AI utilities & tools
│   └── config/           # Shared configs
├── turbo.json
├── package.json
└── bun.lockb
```

#### 1.2 Bun Migration
- Remove `pnpm-lock.yaml`
- Update all `package.json` scripts
- Install Bun globally
- Test all build/dev scripts
- Update CI/CD configurations

#### 1.3 Workspace Configuration
- Configure Turborepo pipeline
- Set up shared TypeScript configs
- Configure ESLint/Biome for monorepo
- Set up shared Tailwind config

### Phase 2: Database Migration
**Duration**: 2-3 days
**Priority**: High

#### 2.1 Convex Database Setup
- Initialize Convex project
- Configure Convex schema with TypeScript
- Set up real-time subscriptions
- Test connectivity and performance

#### 2.2 Schema Migration
- Convert Drizzle schema to Convex schema
- Implement Convex functions for queries and mutations
- Set up real-time data synchronization
- Test all database operations

#### 2.3 Environment Variables
```env
# Old
POSTGRES_URL=vercel_postgres_url

# New
CONVEX_DEPLOYMENT=your_convex_deployment_url
NEXT_PUBLIC_CONVEX_URL=your_convex_url
```

### Phase 3: Authentication Overhaul
**Duration**: 4-5 days
**Priority**: Critical

#### 3.1 Clerk Installation & Setup
```bash
bun add @clerk/nextjs @clerk/themes
```

#### 3.2 Configuration Changes
- Replace `app/(auth)/auth.ts` with Clerk config
- Update `middleware.ts` for Clerk
- Configure Clerk environment variables

#### 3.3 Component Updates
Files to modify:
- `components/auth-form.tsx`
- `components/sign-out-form.tsx`
- `components/sidebar-user-nav.tsx`
- `app/(auth)/login/page.tsx`
- `app/(auth)/register/page.tsx`

#### 3.4 Database Schema Updates
- Update user ID references for Clerk
- Modify user-related queries
- Update session management

#### 3.5 API Route Updates
- Replace NextAuth session checks
- Update protected route middleware
- Modify user-related API endpoints

### Phase 4: AI Provider Migration
**Duration**: 3-4 days
**Priority**: High

#### 4.1 OpenRouter Integration
```bash
bun remove @ai-sdk/xai
bun add @openrouter/ai-sdk
```

#### 4.2 Provider Configuration
Update `lib/ai/providers.ts`:
```typescript
import { openrouter } from '@openrouter/ai-sdk';

export const myProvider = customProvider({
  languageModels: {
    'chat-model': openrouter('google/gemini-2.0-flash-exp'),
    'chat-model-reasoning': openrouter('openai/o3-mini'),
    'title-model': openrouter('google/gemini-2.0-flash-exp'),
    'artifact-model': openrouter('google/gemini-2.0-flash-exp'),
  },
});
```

#### 4.3 Model Configuration
- Update `lib/ai/models.ts` for new models
- Configure model switching logic
- Test streaming capabilities
- Verify tool calling functionality

#### 4.4 Environment Variables
```env
# Old
XAI_API_KEY=xai_key

# New
OPENROUTER_API_KEY=openrouter_key
```

### Phase 5: File Storage Migration
**Duration**: 2-3 days
**Priority**: Medium

#### 5.1 UploadThing Setup
```bash
bun remove @vercel/blob
bun add uploadthing
```

#### 5.2 API Route Updates
- Replace `app/(chat)/api/files/upload/route.ts`
- Update file handling logic
- Configure UploadThing endpoints

#### 5.3 Component Updates
- Modify `components/multimodal-input.tsx`
- Update `components/preview-attachment.tsx`
- Update artifact file handling

#### 5.4 Environment Variables
```env
# Old
BLOB_READ_WRITE_TOKEN=vercel_blob_token

# New
UPLOADTHING_SECRET=uploadthing_secret
UPLOADTHING_APP_ID=uploadthing_app_id
```

### Phase 6: MCP Integration
**Duration**: 3-4 days
**Priority**: Medium

#### 6.1 MCP Client Implementation
- Research MCP specification
- Implement MCP client wrapper
- Create connection management

#### 6.2 AI SDK Integration
- Integrate MCP with existing tools system
- Add MCP tool discovery
- Implement error handling

#### 6.3 Chat Interface Updates
- Add MCP-specific UI components
- Update tool calling interface
- Add MCP server configuration

### Phase 7: Testing & Optimization
**Duration**: 3-4 days
**Priority**: Critical

#### 7.1 Testing Strategy
- Unit tests for migrated components
- Integration tests for auth flow
- E2E tests for user journeys
- Performance testing
- Security audit

#### 7.2 Performance Optimization
- Bundle size analysis
- Runtime performance testing
- Database query optimization
- Caching strategy review

#### 7.3 Documentation Updates
- Update README.md
- Update API documentation
- Create migration guide
- Update deployment instructions

## Risk Assessment

### High Risk Areas
1. **Authentication Migration**: Complete user system overhaul
2. **Database Changes**: Potential data loss or corruption
3. **AI Provider Changes**: Different API behaviors and capabilities
4. **File Storage Migration**: Existing file reference issues

### Mitigation Strategies
1. **Backup Strategy**: Full system backup before migration
2. **Feature Flags**: Gradual rollout with toggles
3. **Parallel Testing**: Test environment with new stack
4. **Rollback Plans**: Quick revert procedures for each phase
5. **Data Validation**: Comprehensive data integrity checks

## Environment Variables Migration

### Complete Environment Setup
```env
# Database
NEON_DATABASE_URL=******************************

# Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...

# AI Provider
OPENROUTER_API_KEY=sk-or-...

# File Storage
UPLOADTHING_SECRET=sk_live_...
UPLOADTHING_APP_ID=app_...

# MCP (if needed)
MCP_SERVER_URL=ws://localhost:3001
MCP_API_KEY=mcp_key_...

# Next.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret_here
```

## Success Criteria

### Functional Requirements
- ✅ All original features working
- ✅ Authentication flow complete
- ✅ Database operations functional
- ✅ File uploads working
- ✅ AI responses working
- ✅ MCP integration functional

### Performance Requirements
- ✅ Response times ≤ original system
- ✅ Bundle size optimized
- ✅ Database queries optimized
- ✅ Memory usage acceptable

### Security Requirements
- ✅ Authentication secure
- ✅ Data encryption maintained
- ✅ API endpoints protected
- ✅ File uploads validated

## Timeline Summary

| Phase | Duration | Dependencies |
|-------|----------|--------------|
| Phase 1: Foundation | 3-4 days | None |
| Phase 2: Database | 2-3 days | Phase 1 |
| Phase 3: Authentication | 4-5 days | Phase 1, 2 |
| Phase 4: AI Provider | 3-4 days | Phase 1 |
| Phase 5: File Storage | 2-3 days | Phase 1 |
| Phase 6: MCP Integration | 3-4 days | Phase 1, 4 |
| Phase 7: Testing | 3-4 days | All phases |

**Total Estimated Duration**: 20-27 days (4-5 weeks)

## Next Steps

1. **Immediate**: Start Phase 1 - Foundation Setup
2. **Week 1**: Complete Phases 1-2
3. **Week 2**: Complete Phases 3-4
4. **Week 3**: Complete Phases 5-6
5. **Week 4**: Complete Phase 7 and deployment

This migration plan provides a structured approach to successfully transition the AI Chatbot to the BonKai stack while minimizing risks and maintaining system functionality.
