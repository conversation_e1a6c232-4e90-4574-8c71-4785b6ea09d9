# BonKai: Detailed Technical Product Requirements Document

## Executive Summary

<PERSON><PERSON><PERSON> is an innovative Web3 meme project that combines NFTs, AI agents, and gamification on the Solana blockchain. This PRD provides comprehensive technical specifications and implementation patterns for building a multi-platform ecosystem featuring a 6969-supply NFT collection, multi-tier staking system, AI-powered agents with token-gated access, and cross-platform integration via web and Telegram interfaces.

## 1. System Architecture Overview

### Technology Stack
- **Frontend**: Next.js 14+ (App Router)
- **Database**: Neon PostgreSQL + Drizzle ORM
- **Authentication**: Clerk with Web3 wallet integration
- **Runtime**: Bun
- **Monorepo**: Turborepo
- **AI Framework**: AISDK from Vercel with OpenRouter
- **Models**: Gemini 2.5 Flash, O3, GPT-1, Veo 3 Fast
- **File Storage**: Uploadthing
- **Blockchain**: Solana
- **Real-time**: WebSockets

### Project Structure (Turborepo)
```
bonkai-web3/
├── apps/
│   ├── web/                 # Next.js frontend
│   ├── telegram-bot/        # Telegram bot service
│   └── api/                 # API services
├── packages/
│   ├── blockchain/          # Solana utilities
│   ├── ui/                  # Shared components
│   ├── ai/                  # AI integration
│   ├── database/            # Drizzle schemas
│   └── types/               # TypeScript definitions
├── contracts/               # Solana smart contracts
└── turbo.json              # Turborepo config
```

## 2. Blockchain Implementation

### 2.1 NFT Collection (6969 Supply)

**5-Tier Structure Implementation**
```typescript
// Using Metaplex Core for cost efficiency
const tiers = [
  { name: 'Common', supply: 3500, power: 10, stakingTier: 0 },
  { name: 'Uncommon', supply: 2000, power: 25, stakingTier: 0 },
  { name: 'Rare', supply: 1000, power: 50, stakingTier: 1 },
  { name: 'Epic', supply: 400, power: 100, stakingTier: 1 },
  { name: 'Legendary', supply: 69, power: 250, stakingTier: 2 }
];
```

**Minting with BonKai Token**
```typescript
const purchaseNFTWithToken = async (
  buyer: PublicKey,
  price: number,
  bonkaiTokenMint: PublicKey
) => {
  const buyerTokenAccount = await createAssociatedTokenAccount(
    connection,
    buyer,
    bonkaiTokenMint,
    buyer
  );
  
  await transfer(
    connection,
    buyer,
    buyerTokenAccount,
    treasuryTokenAccount,
    buyer,
    price * Math.pow(10, 9)
  );
};
```

### 2.2 Multi-Tier Staking System

**Staking Smart Contract (Anchor)**
```rust
#[program]
pub mod bonkai_staking {
    pub fn stake_nft(
        ctx: Context<StakeNFT>,
        nft_tier: u8, // 0=Common/Uncommon, 1=Rare/Epic, 2=Legendary
    ) -> Result<()> {
        let staking_pool = &mut ctx.accounts.staking_pool;
        let user_stake = &mut ctx.accounts.user_stake;
        let clock = Clock::get()?;
        
        // Transfer NFT to staking vault
        // Initialize stake record with tier-based rewards
        user_stake.staking_tier = nft_tier;
        user_stake.last_claim_time = clock.unix_timestamp;
        
        Ok(())
    }
}
```

**Tier-Based Access Levels**
- Bronze Tier ($20): Basic AI access, 100K tokens/month
- Silver Tier ($50): Premium AI access, 500K tokens/month
- Diamond Tier ($100): Full AI access, 2M tokens/month

## 3. Authentication System

### 3.1 Clerk + Web3 Wallet Integration

**Hybrid Authentication Component**
```typescript
export default function Web3Auth() {
  const { user } = useUser();
  const { publicKey, connected } = useWallet();
  
  const linkWalletToUser = async (walletAddress: string) => {
    await fetch('/api/link-wallet', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ walletAddress }),
    });
  };

  return (
    <div className="auth-container">
      <SignedIn>
        <WalletMultiButton />
        {connected && publicKey && (
          <span className={walletLinked ? 'linked' : 'not-linked'}>
            {walletLinked ? '✓ Linked' : '○ Not Linked'}
          </span>
        )}
      </SignedIn>
    </div>
  );
}
```

### 3.2 Token-Gating Middleware

```typescript
export async function tokenGateMiddleware(
  req: NextRequest,
  requiredTier: string
): Promise<NextResponse | null> {
  const { userId } = await auth();
  const userWallet = await getUserWalletAddress(userId);
  
  const hasAccess = await checkTokenOwnership(userWallet, requiredTier);
  
  if (!hasAccess) {
    return NextResponse.json(
      { error: 'Insufficient token balance for access' },
      { status: 403 }
    );
  }
  
  return null;
}
```

## 4. AI Agent Implementation

### 4.1 Multi-Model Configuration

```typescript
export const models = {
  fast: {
    gemini: openrouter('google/gemini-2.5-flash'),
    gpt4oMini: openrouter('openai/gpt-4o-mini'),
  },
  premium: {
    o3: openrouter('openai/o3'),
    gpt4o: openrouter('openai/gpt-4o'),
  },
  specialized: {
    imageGen: openrouter('openai/gpt-1'),
    videoGen: openrouter('google/veo-3-fast'),
  }
};
```

### 4.2 Task Complexity Routing

```typescript
export function getModelForTask(
  complexity: TaskComplexity,
  userTier: UserTier,
  taskType: 'text' | 'image' | 'video' = 'text'
) {
  if (userTier === UserTier.BRONZE && complexity === TaskComplexity.COMPLEX) {
    return models.fast.gemini; // Downgrade for bronze tier
  }
  
  switch (complexity) {
    case TaskComplexity.SIMPLE:
      return models.fast.gemini;
    case TaskComplexity.COMPLEX:
      return userTier === UserTier.DIAMOND ? models.premium.o3 : models.premium.gemini25Flash;
    default:
      return models.fast.gemini;
  }
}
```

### 4.3 MCP Integration

```typescript
const mcpConfig = {
  mcpServers: {
    'file-manager': {
      command: 'npx',
      args: ['-y', 'mcp-server-filesystem'],
    },
    'web-search': {
      command: 'npx',
      args: ['-y', 'mcp-server-brave-search'],
    }
  }
};
```

## 5. Telegram Bot Integration

### 5.1 grammY Bot Setup

```typescript
import { Bot, Context, SessionFlavor } from "grammy";

interface SessionData {
  walletAddress?: string;
  tier?: string;
  authenticated?: boolean;
}

type BotContext = Context & SessionFlavor<SessionData>;
const bot = new Bot<BotContext>(process.env.BOT_TOKEN);
```

### 5.2 Bankai Moves Gamification

```typescript
const bankaiMoves = {
  "fire_slash": { damage: 100, cost: 0.05 },
  "water_shield": { defense: 50, cost: 0.05 },
  "earth_stomp": { damage: 150, cost: 0.05 },
  "wind_dash": { speed: 200, cost: 0.05 }
};

bot.command("bankai", async (ctx) => {
  const keyboard = new InlineKeyboard();
  
  Object.entries(bankaiMoves).forEach(([move, stats]) => {
    keyboard.text(`${move} (${stats.cost} SOL)`, `bankai_${move}`);
    keyboard.row();
  });
  
  await ctx.reply("Choose your Bankai move:", { reply_markup: keyboard });
});
```

### 5.3 WebSocket Real-time Sync

```typescript
const server = serve({
  port: 3001,
  websocket: {
    message(ws, message) {
      const data = JSON.parse(message);
      
      switch (data.type) {
        case "auth":
          handleAuth(ws, data);
          break;
        case "transaction":
          broadcastTransaction(data);
          break;
      }
    }
  }
});
```

## 6. Database Schema

### 6.1 Core Tables (Drizzle ORM)

```typescript
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  walletAddress: varchar('wallet_address', { length: 42 }).unique().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const stakingPositions = pgTable('staking_positions', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  amount: decimal('amount', { precision: 36, scale: 18 }).notNull(),
  stakingTier: integer('staking_tier').notNull(),
  startTime: timestamp('start_time').defaultNow().notNull(),
});

export const auraPoints = pgTable('aura_points', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  points: decimal('points', { precision: 15, scale: 2 }).default('0'),
  level: integer('level').default(1),
});
```

### 6.2 Performance Optimization

```typescript
// Connection pooling with Neon
const pool = new Pool({ 
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
});

// Optimized indexes
export const optimizedIndexes = {
  userWalletIdx: index('user_wallet_idx').on(wallets.userId, wallets.chainId),
  txByAddressTimeIdx: index('tx_by_address_time_idx').on(
    blockchainTransactions.fromAddress, 
    blockchainTransactions.timestamp
  ),
};
```

## 7. Frontend Implementation

### 7.1 Component Architecture

**Reusable Web3 Components**
```typescript
// Wallet Connect Component
export default function WalletConnectButton({
  onConnect,
  onDisconnect
}: WalletConnectButtonProps) {
  const { connect, connectors, isPending } = useConnect();
  const { isConnected, address } = useAccount();
  
  if (isConnected) {
    return (
      <div className="flex items-center space-x-2">
        <span>{address?.slice(0, 6)}...{address?.slice(-4)}</span>
        <button onClick={onDisconnect}>Disconnect</button>
      </div>
    );
  }
  
  return connectors.map((connector) => (
    <button
      key={connector.id}
      onClick={() => connect({ connector })}
      disabled={isPending}
    >
      Connect {connector.name}
    </button>
  ));
}
```

### 7.2 State Management

```typescript
export const useWalletStore = create<WalletState>()(
  persist(
    (set) => ({
      isConnected: false,
      address: null,
      balance: 0,
      transactions: [],
      setConnected: (connected) => set({ isConnected: connected }),
      addTransaction: (transaction) =>
        set((state) => ({
          transactions: [...state.transactions, transaction]
        }))
    }),
    { name: 'wallet-storage' }
  )
);
```

## 8. File Storage Integration

### 8.1 Uploadthing Configuration

```typescript
export const uploadRouter = {
  aiImageUpload: f(['image'])
    .middleware(async ({ req }) => {
      const user = await auth(req);
      const userTier = await getUserTier(user.id);
      const quota = await checkUploadQuota(user.id, userTier);
      
      if (!quota.canUpload) {
        throw new Error('Upload quota exceeded');
      }
      
      return { userId: user.id, userTier };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      await saveAIAsset({
        userId: metadata.userId,
        fileUrl: file.url,
        assetType: 'image',
        generationPrompt: metadata.prompt,
      });
    }),
};
```

## 9. Security Implementation

### 9.1 Rate Limiting

```typescript
const rateLimiters = {
  [UserTier.BRONZE]: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(50, '1 h'),
  }),
  [UserTier.SILVER]: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(200, '1 h'),
  }),
  [UserTier.DIAMOND]: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(1000, '1 h'),
  })
};
```

### 9.2 Message Signing Verification

```typescript
export function verifySignature(
  message: SignMessage,
  signature: string,
  walletAddress: string
): boolean {
  const messageBytes = new TextEncoder().encode(formatSignMessage(message));
  const signatureBytes = bs58.decode(signature);
  const publicKeyBytes = new PublicKey(walletAddress).toBytes();
  
  return nacl.sign.detached.verify(messageBytes, signatureBytes, publicKeyBytes);
}
```

## 10. Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
- Deploy BonKai SPL token
- Set up Turborepo structure
- Implement basic authentication with Clerk
- Create database schemas

### Phase 2: NFT System (Weeks 3-4)
- Deploy Metaplex Core collection
- Implement minting functionality
- Create staking contracts
- Build NFT display components

### Phase 3: AI Integration (Weeks 5-6)
- Integrate AISDK with OpenRouter
- Implement token-gated access
- Add MCP connections
- Deploy file storage system

### Phase 4: Platform Integration (Weeks 7-8)
- Deploy Telegram bot
- Implement WebSocket sync
- Add gamification features
- Complete cross-platform testing

### Phase 5: Launch Preparation (Weeks 9-10)
- Security audit
- Performance optimization
- Documentation completion
- Mainnet deployment

## 11. Cost Estimates

- **NFT Minting**: ~0.0037 SOL per mint (Metaplex Core)
- **Staking Operations**: ~0.005 SOL per transaction
- **AI Usage**: Variable based on model selection
- **Infrastructure**: ~$500-1000/month (Neon, Vercel, Uploadthing)

## 12. Key Technical Advantages

1. **Cost Efficiency**: 70% reduction using Metaplex Core
2. **Scalability**: Turborepo enables efficient development
3. **Security**: Multi-layer authentication and rate limiting
4. **Performance**: Optimistic UI patterns and caching
5. **Cross-Platform**: Unified experience across web and Telegram

This PRD provides a comprehensive technical foundation for building the BonKai Web3 ecosystem, combining cutting-edge blockchain technology with AI capabilities and engaging gamification features.